from django.db.models.signals import post_save, pre_save, post_delete
from django.dispatch import receiver
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from decimal import Decimal
from .models import (
    OrderItem, Product, Order, UserProfile, CartItem,
    Payment, Offer, Stock, User, Category, Review
)
from .optimization import clear_product_cache
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)

# Stock Management Signals
@receiver(post_save, sender=OrderItem)
def update_stock_on_order(sender, instance, created, **kwargs):
    """Update product stock when an order item is created or updated."""
    if created:
        # New order item created
        instance.product.stock -= instance.quantity
        instance.product.save()
        
        # Create stock record
        Stock.objects.create(
            product=instance.product,
            stock_quantity=instance.product.stock
        )
        
        # Send low stock notification if needed
        if instance.product.stock < 10:
            send_mail(
                'Low Stock Alert',
                f'Product {instance.product.name} is running low on stock. Current stock: {instance.product.stock}',
                settings.DEFAULT_FROM_EMAIL,
                [settings.ADMIN_EMAIL],
                fail_silently=True,
            )

@receiver(pre_save, sender=Product)
def update_stock_status(sender, instance, **kwargs):
    """Update stock status based on stock quantity."""
    if instance.stock == 0:
        instance.stock_status = 'out of stock'
    elif instance.stock < 10:
        instance.stock_status = 'low stock'
    else:
        instance.stock_status = 'in stock'

# Order Management Signals
@receiver(post_save, sender=Order)
def handle_order_status_change(sender, instance, **kwargs):
    """Handle order status changes and send notifications."""
    if instance.tracker.has_changed('status'):
        # Send email notification to customer
        send_mail(
            f'Order #{instance.id} Status Update',
            f'Your order status has been updated to: {instance.status}',
            settings.DEFAULT_FROM_EMAIL,
            [instance.user.email],
            fail_silently=True,
        )
        
        # Update stock status for order items
        for item in instance.items.all():
            item.stock_status = instance.status
            item.save()

# User Profile Signals
@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """Create a UserProfile for every new User."""
    if created:
        UserProfile.objects.create(user=instance)
        # Send welcome email
        send_mail(
            'Welcome to GB Farm!',
            'Thank you for joining our community. We hope you enjoy shopping with us!',
            settings.DEFAULT_FROM_EMAIL,
            [instance.email],
            fail_silently=True,
        )

@receiver(post_save, sender=UserProfile)
def update_user_phone(sender, instance, **kwargs):
    """Sync user phone with profile phone."""
    if instance.user_phone and instance.user.phone != instance.user_phone:
        instance.user.phone = instance.user_phone
        instance.user.save()

# Cart Management Signals
@receiver(post_save, sender=CartItem)
def update_cart_total(sender, instance, **kwargs):
    """Update cart total when items are added or modified."""
    cart = instance.cart
    cart.save()  # This will trigger the total property calculation

@receiver(post_delete, sender=CartItem)
def cleanup_empty_cart(sender, instance, **kwargs):
    """Delete cart if it becomes empty."""
    cart = instance.cart
    if cart.item_count == 0:
        cart.delete()

# Payment Processing Signals
@receiver(post_save, sender=Payment)
def handle_payment_status(sender, instance, created, **kwargs):
    """Handle payment processing and update order status."""
    if created:
        order = instance.order
        order.status = 'processing'
        order.save()
        
        # Send payment confirmation email
        send_mail(
            f'Payment Confirmation - Order #{order.id}',
            f'Your payment of ${instance.amount} has been received.',
            settings.DEFAULT_FROM_EMAIL,
            [order.user.email],
            fail_silently=True,
        )

# Offer Management Signals
@receiver(post_save, sender=Offer)
def handle_offer_status(sender, instance, **kwargs):
    """Handle offer status and send notifications."""
    now = timezone.now()
    
    if instance.valid_from <= now <= instance.valid_to:
        # Send offer notification to users
        users = User.objects.filter(profile__newsletter_subscription=True)
        for user in users:
            send_mail(
                'New Offer Available!',
                f'Get {instance.discount_percent}% off on {instance.product.name}!',
                settings.DEFAULT_FROM_EMAIL,
                [user.email],
                fail_silently=True,
            )
    
    # Update product stock status based on offer
    if instance.stock_status != instance.product.stock_status:
        instance.product.stock_status = instance.stock_status
        instance.product.save()

# Stock Record Signals
@receiver(post_save, sender=Stock)
def notify_stock_changes(sender, instance, created, **kwargs):
    """Notify admin of significant stock changes."""
    if created:
        # Get previous stock record
        previous_stock = Stock.objects.filter(
            product=instance.product
        ).exclude(id=instance.id).order_by('-created_at').first()
        
        if previous_stock:
            change = instance.stock_quantity - previous_stock.stock_quantity
            if abs(change) > 50:  # Significant change threshold
                send_mail(
                    'Significant Stock Change Alert',
                    f'Product {instance.product.name} stock changed by {change} units.',
                    settings.DEFAULT_FROM_EMAIL,
                    [settings.ADMIN_EMAIL],
                    fail_silently=True,
                )

@receiver(post_save, sender=Product)
def product_save_handler(sender, instance, **kwargs):
    """Clear product cache when a product is saved"""
    try:
        clear_product_cache(instance.id)
        logger.info(f"Cleared cache for product {instance.id}")
    except Exception as e:
        logger.error(f"Error clearing product cache: {str(e)}")

@receiver(post_delete, sender=Product)
def product_delete_handler(sender, instance, **kwargs):
    """Clear product cache when a product is deleted"""
    try:
        clear_product_cache(instance.id)
        logger.info(f"Cleared cache for deleted product {instance.id}")
    except Exception as e:
        logger.error(f"Error clearing product cache: {str(e)}")

@receiver(post_save, sender=Category)
def category_save_handler(sender, instance, **kwargs):
    """Clear category list cache when a category is saved"""
    try:
        cache.delete('category_list')
        logger.info("Cleared category list cache")
    except Exception as e:
        logger.error(f"Error clearing category cache: {str(e)}")

@receiver(post_save, sender=Review)
def review_save_handler(sender, instance, **kwargs):
    """Clear product cache when a review is saved"""
    try:
        clear_product_cache(instance.product.id)
        logger.info(f"Cleared cache for product {instance.product.id} after review update")
    except Exception as e:
        logger.error(f"Error clearing product cache after review: {str(e)}")

@receiver(post_save, sender=Order)
def order_save_handler(sender, instance, **kwargs):
    """Clear daily sales cache when an order is saved"""
    try:
        cache.delete('daily_sales')
        logger.info("Cleared daily sales cache")
    except Exception as e:
        logger.error(f"Error clearing daily sales cache: {str(e)}")