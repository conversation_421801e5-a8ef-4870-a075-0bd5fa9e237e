from functools import wraps
from django.http import JsonResponse
from django.contrib.auth import authenticate
from .models import APIToken
from django.utils import timezone
from datetime import timedelta
import requests

def require_api_token(view_func):
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        # Get token from header
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Token '):
            return JsonResponse({
                'error': 'No token provided',
                'status': 401
            }, status=401)

        token = auth_header.split(' ')[1]
        user = authenticate(request, token=token)

        if not user:
            return JsonResponse({
                'error': 'Invalid token',
                'status': 401
            }, status=401)

        request.user = user
        return view_func(request, *args, **kwargs)

    return _wrapped_view 

@require_api_token
def my_api_view(request):
    # Your view logic here
    pass 