# Generated by Django 4.2.20 on 2025-07-20 23:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0059_alter_excategory_options_remove_excategory_color_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExCategoryProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('excategory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.excategory')),
                ('exproduct', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.exproduct')),
            ],
            options={
                'verbose_name': 'Category Product Link',
                'verbose_name_plural': 'Category Product Links',
                'unique_together': {('excategory', 'exproduct')},
            },
        ),
    ] 