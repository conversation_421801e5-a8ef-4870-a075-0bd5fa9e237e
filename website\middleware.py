from django.http import JsonResponse
from django.conf import settings
import logging
import traceback
from django.core.exceptions import PermissionDenied, ValidationError
from django.db import IntegrityError
from django.http import Http404
from django.core.cache import cache
from django.utils import timezone
import re
from datetime import datetime, timedelta
from django.shortcuts import redirect
from django.urls import reverse, resolve, Resolver404

logger = logging.getLogger(__name__)

class InterfaceMiddleware:
    """
    Middleware to route users to the appropriate interface based on their user_type.
    Internal users (admin) will be directed to the admin interface, while external
    users (customers) will be directed to the customer interface.
    """
    def __init__(self, get_response):
        self.get_response = get_response
        
        # URL patterns that should only be accessible by internal users
        self.internal_url_patterns = [
            r'^/admin/',  # Django built-in admin URLs
            r'^/dashboard/',  # Custom admin URLs starting with dashboard
            r'^/dashboard/careers/',
            r'^/dashboard/orders/',
            r'^/dashboard/products/',
            r'^/dashboard/customers/',
            r'^/dashboard/analytics/',
            r'^/dashboard/zones/',
            r'^/dashboard/invoices/',
        ]
        
        # URL patterns that are always accessible regardless of user type
        self.public_url_patterns = [
            r'^/login/',
            r'^/logout/',
            r'^/register/',
            r'^/static/',
            r'^/media/',
            r'^/i18n/',
        ]

    def __call__(self, request):
        # Set interface context for templates
        request.is_internal_interface = False
        
        # Skip middleware for static and media files
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            return self.get_response(request)
            
        # Always allow access to public URLs
        for pattern in self.public_url_patterns:
            if re.match(pattern, request.path):
                return self.get_response(request)
        
        # Check if the URL is an internal URL
        is_internal_url = any(re.match(pattern, request.path) for pattern in self.internal_url_patterns)
        
        # Allow access to internal URLs only for authenticated staff/admin users
        if is_internal_url:
            if request.user.is_authenticated and request.user.user_type == 'admin':
                request.is_internal_interface = True
                return self.get_response(request)
            else:
                # Redirect non-admin users to login page
                return redirect('GB_FARM:loginpage')
        
        # For regular views, no redirection needed
        response = self.get_response(request)
        return response

class RateLimitMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        self.rate_limit = 100  # requests per minute
        self.rate_limit_window = 60  # seconds

    def __call__(self, request):
        # Get client IP
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')

        # Create cache key for rate limiting
        cache_key = f'rate_limit_{ip}'
        
        # Get current request count
        request_count = cache.get(cache_key, 0)
        
        # Check if rate limit exceeded
        if request_count >= self.rate_limit:
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'status': 429,
                'retry_after': self.rate_limit_window
            }, status=429)
        
        # Increment request count
        cache.set(cache_key, request_count + 1, self.rate_limit_window)
        
        # Add security headers to response
        response = self.get_response(request)
        
        # Security Headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        response['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; img-src 'self' data: https:; font-src 'self' https://cdn.jsdelivr.net;"
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
        
        return response

class ErrorHandlingMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        return response

    def process_exception(self, request, exception):
        if settings.DEBUG:
            return None

        error_data = {
            'error': str(exception),
            'path': request.path,
            'method': request.method,
        }

        if isinstance(exception, Http404):
            return JsonResponse({
                'error': 'Resource not found',
                'status': 404,
                **error_data
            }, status=404)

        elif isinstance(exception, PermissionDenied):
            return JsonResponse({
                'error': 'Permission denied',
                'status': 403,
                **error_data
            }, status=403)

        elif isinstance(exception, ValidationError):
            return JsonResponse({
                'error': 'Validation error',
                'status': 400,
                'details': exception.message_dict if hasattr(exception, 'message_dict') else str(exception),
                **error_data
            }, status=400)

        elif isinstance(exception, IntegrityError):
            return JsonResponse({
                'error': 'Database integrity error',
                'status': 400,
                **error_data
            }, status=400)

        else:
            # Log unexpected errors
            logger.error(f"Unexpected error: {str(exception)}")
            logger.error(traceback.format_exc())

            return JsonResponse({
                'error': 'Internal server error',
                'status': 500,
                **error_data
            }, status=500)