{% extends 'GB_FARM/base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}Admin Order Dashboard{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card shadow-sm mb-4">
                <div class="card-header text-white">
                    <h5 class="mb-0">Admin Dashboard</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:admin_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-shopping-cart me-2"></i> Orders
                    </a>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-box me-2"></i> Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i> Customers
                    </a>
                    <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                    <a href="{% url 'GB_FARM:admin_invoice_logs' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-file-invoice me-2"></i> Invoice Logs
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <h6 class="card-subtitle mb-2 text-muted">Total Orders</h6>
                            <h2 class="card-title mb-0">{{ total_orders }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <h6 class="card-subtitle mb-2 text-muted">Total Revenue</h6>
                            <h2 class="card-title mb-0">EGP {{ total_revenue|floatformat:2 }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <h6 class="card-subtitle mb-2 text-muted">Average Order Value</h6>
                            <h2 class="card-title mb-0">EGP {{ average_order_value|floatformat:2 }}</h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Orders Table -->
            <div class="card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Orders</h5>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-1"></i> Filter
                        </button>
                        <div class="dropdown-menu p-3" style="min-width: 250px;">
                            <form method="get">
                                <div class="mb-3">
                                    <label class="form-label">Status</label>
                                    <select name="status" class="form-select">
                                        <option value="">All Statuses</option>
                                        {% for status in STATUS_CHOICES %}
                                        <option value="{{ status.0 }}" {% if status.0 == status_filter %}selected{% endif %}>
                                            {{ status.1 }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Date Range</label>
                                    <input type="date" name="date_from" class="form-control mb-2" value="{{ date_from }}">
                                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                                </div>
                                <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr>
                                    <td>#{{ order.id }}</td>
                                    <td>
                                        {{ order.user.username }}
                                        {% if order.office_number %}
                                        <br><small class="text-muted">Office: {{ order.office_number }}</small>
                                        {% endif %}
                                        {% if order.delivery_zone %}
                                        <br><small class="text-muted">Zone: {{ order.delivery_zone.name }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ order.created_at|date:"M d, Y" }}</td>
                                    <td>EGP {{ order.total_amount|floatformat:2 }}</td>
                                    <td>
                                        <span class="badge bg-{{ order.status_color }} text-dark">
                                            {{ order.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <a href="{% url 'GB_FARM:admin_order_detail' order.id %}" class="btn btn-sm btn-outline-primary me-2">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <div class="dropdown me-2">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    Update Status
                                                </button>
                                                <ul class="dropdown-menu">
                                                    {% for status in STATUS_CHOICES %}
                                                    <li>
                                                        <form method="post" action="{% url 'GB_FARM:update_order_status' order.id %}" class="status-update-form">
                                                            {% csrf_token %}
                                                            <input type="hidden" name="status" value="{{ status.0 }}">
                                                            <button type="submit" class="dropdown-item {% if status.0 == order.status %}active{% endif %}">
                                                                {{ status.1 }}
                                                            </button>
                                                        </form>
                                                    </li>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                            <a href="{% url 'GB_FARM:admin_order_message' order_id=order.id %}" 
                                               class="btn btn-sm {% if order.unread_messages_count > 0 %}btn-info{% else %}btn-outline-info{% endif %} position-relative">
                                                <i class="fas fa-comment{% if order.unread_messages_count > 0 %}-dots{% endif %}"></i>
                                                {% if order.unread_messages_count > 0 %}
                                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger animate-pulse">
                                                    {{ order.unread_messages_count }}
                                                    <span class="visually-hidden">unread messages</span>
                                                </span>
                                                {% endif %}
                                            </a>
                                            {% if order.is_new %}
                                            <span class="badge bg-success">New</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center py-4">No orders found</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all modals
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modalEl => new bootstrap.Modal(modalEl));

    // Handle all status update forms
    document.querySelectorAll('.status-update-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            fetch(this.action, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': this.querySelector('[name="csrfmiddlewaretoken"]').value
                },
                body: new URLSearchParams(new FormData(this))
            })
            .then(response => {
                if (response.ok) {
                    window.location.reload();
                } else {
                    throw new Error('Failed to update status');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to update order status. Please try again.');
            });
        });
    });
});
</script>
{% endblock %}

{% block extra_css %}
{{ block.super }} {# Include base CSS if needed #}
<style>
    .animate-pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(0.95) translate(-50%, -50%);
        }
        50% {
            transform: scale(1.1) translate(-50%, -50%);
        }
        100% {
            transform: scale(0.95) translate(-50%, -50%);
        }
    }

    .btn-info.position-relative {
        transition: all 0.3s ease;
    }

    .btn-info.position-relative:hover {
        transform: scale(1.1);
    }

    /* Style for the status dropdown */
    .status-update-form .dropdown-item {
        /* Add general styles if needed */
    }

    .status-update-form .dropdown-item.active,
    .status-update-form .dropdown-item:active {
        background-color: var(--bs-primary); /* Use primary color for active background */
        color: white; /* Ensure text is white for contrast */
    }
</style>
{% endblock %} 