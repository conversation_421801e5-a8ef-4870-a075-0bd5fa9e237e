# Generated by Django 4.2.20 on 2025-05-09 20:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0009_carouselimage_button_text_ar_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='About',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Title')),
                ('story_title', models.CharField(max_length=200)),
                ('story_title_ar', models.Char<PERSON>ield(blank=True, max_length=200, null=True, verbose_name='Arabic Story Title')),
                ('story_content', models.TextField()),
                ('story_content_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Story Content')),
                ('operations_title', models.Char<PERSON>ield(max_length=200)),
                ('operations_title_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Operations Title')),
                ('operations_content', models.TextField()),
                ('operations_content_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Operations Content')),
                ('values_title', models.CharField(max_length=200)),
                ('values_title_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Values Title')),
                ('values_content', models.TextField()),
                ('values_content_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Values Content')),
                ('phone', models.CharField(max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'About Information',
                'verbose_name_plural': 'About Information',
            },
        ),
    ]
