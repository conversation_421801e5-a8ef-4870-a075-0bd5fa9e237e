from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse, HttpResponseRedirect
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_protect
from django.contrib.auth import login, logout, authenticate, update_session_auth_hash
from django.contrib.auth.forms import UserCreationForm, PasswordChangeForm
from django.utils.translation import activate, get_language
from django.conf import settings
from django.urls import translate_url
from django.utils import translation
from django.http import HttpResponseRedirect
from .models import *
from .optimization import (
    get_product_list, get_product_detail, get_category_products,
    get_featured_products, get_category_list
)
from .decorators import require_api_token
import logging
from django.db import models
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count, Avg, F
from decimal import Decimal
import random
from django.utils import timezone
from django.db.models.functions import TruncDate
from datetime import datetime, timedelta, date
from .forms import UserRegisterForm, UserLoginForm, UserUpdateForm, UserProfileForm, CheckoutForm, ZoneForm, ProductForm, ContactUsMessageForm
import json
import calendar
from django.db.models import Prefetch
from django.template import RequestContext

logger = logging.getLogger(__name__)

def is_admin(user):
    """Check if a user is an admin (staff or user_type='admin')."""
    return user.is_authenticated and (user.is_staff or user.user_type == 'admin')

def set_language(request):
    """
    Set the language for the current session/user.
    """
    if request.method == 'POST':
        language = request.POST.get('language', settings.LANGUAGE_CODE)
        next_url = request.POST.get('next', '/')
        
        # Activate the new language
        activate(language)
        response = HttpResponseRedirect(next_url)
        
        # Set the language cookie
        response.set_cookie(
            settings.LANGUAGE_COOKIE_NAME,
            language,
            max_age=settings.LANGUAGE_COOKIE_AGE,
            path=settings.LANGUAGE_COOKIE_PATH,
            domain=settings.LANGUAGE_COOKIE_DOMAIN,
            secure=settings.LANGUAGE_COOKIE_SECURE,
            httponly=settings.LANGUAGE_COOKIE_HTTPONLY,
            samesite=settings.LANGUAGE_COOKIE_SAMESITE,
        )
        
        # Update the session
        if hasattr(request, 'session'):
            request.session[translation.LANGUAGE_SESSION_KEY] = language
        
        return response
    
    return redirect('GB_FARM:home')

def get_common_context(request=None):
    """Get common context for all views"""
    categories = Category.objects.all()
    cart_count = 0
    
    # Calculate cart count if user is authenticated
    if request and request.user.is_authenticated:
        cart_count = Cart.objects.filter(user=request.user).count()
    
    website_video = WebsiteVideo.objects.filter(is_active=True).first()
    
    # Add language information to context
    current_language = get_language()
    available_languages = [
        ('en', 'English'),
        ('ar', 'العربية')
    ]
    
    return {
        'categories': categories,
        'cart_count': cart_count,
        'website_video': website_video,
        'current_language': current_language,
        'available_languages': available_languages,
    }

def home(request):
    """Home page with featured products"""
    products = Product.objects.all().order_by('-created_at')[:8]
    featured_products = Product.objects.filter(is_featured=True)[:6]
    carousel_images = CarouselImage.objects.filter(is_active=True).order_by('order')
    about_us_video = AboutUsVideo.objects.filter(is_active=True).first()
    
    context = {
        'products': products,
        'featured_products': featured_products,
        'carousel_images': carousel_images,
        'about_us_video': about_us_video,
        **get_common_context(request)
    }
    
    if request.user.is_authenticated:
        return render(request, 'GB_FARM/home.html', context)
    else:
        return render(request, 'Extrnal.html/index.html', context)

def product_list(request):
    """List all products with optional filtering"""
    # Get all products from the database, regardless of status
    products = Product.objects.all()
    
    # Log the product count for debugging
    product_count = products.count()
    logger.debug(f"Total products found: {product_count}")
    
    # Apply filters if needed
    category_id = request.GET.get('category')
    search_query = request.GET.get('q')

    if category_id:
        products = products.filter(category_id=category_id)
        logger.debug(f"After category filter: {products.count()} products")
    
    if search_query:
        products = products.filter(
            Q(name__icontains=search_query) | 
            Q(description__icontains=search_query)
        )
        logger.debug(f"After search filter: {products.count()} products")
    
    # Paginate results
    paginator = Paginator(products, 12)
    page = request.GET.get('page', 1)
    products_page = paginator.get_page(page)
    
    # Create context with all necessary data
    context = {
        'products': products_page,
        'categories': Category.objects.all(),
        'category_id': category_id,
        'search_query': search_query,
        **get_common_context(request)
    }
    
    return render(request, 'GB_FARM/product_list.html', context)

def product_detail(request, product_id):
    """Product detail page"""
    product = get_product_detail(product_id)
    reviews = Review.objects.filter(product=product).select_related('user').order_by('-created_at')
    
    # Calculate review summary
    review_count = reviews.count()
    average_rating = reviews.aggregate(Avg('rating'))['rating__avg'] or 0
    
    context = {
        'product': product,
        'reviews': reviews,
        'review_count': review_count,
        'average_rating': average_rating,
        **get_common_context(request)  # Include common context with cart count
    }
    
    return render(request, 'GB_FARM/product_detail.html', context)

def category_products(request, category_id):
    """List products in a category"""
    # Get the category
    category = get_object_or_404(Category, id=category_id)
    
    # Get all products in this category
    products = Product.objects.filter(category=category)
    logger.debug(f"Category '{category.name}' has {products.count()} products")
    
    # Paginate results
    paginator = Paginator(products, 12)
    page = request.GET.get('page', 1)
    products_page = paginator.get_page(page)
    
    # Create context with all necessary data
    context = {
        'products': products_page,
        'categories': Category.objects.all(),
        'category': category,
        **get_common_context(request)
    }
    
    return render(request, 'GB_FARM/product_list.html', context)

@login_required
def add_to_cart(request, product_id):
    """Add product to cart"""
    if request.method == 'POST':
        try:
            product = get_product_detail(product_id)
            if product.stock <= 0:
                messages.error(request, 'Product is out of stock')
                return redirect('GB_FARM:product_detail', product_id=product_id)

            # Get quantity from form
            quantity = int(request.POST.get('quantity', 1))
            if quantity <= 0:
                quantity = 1
            elif quantity > product.stock:
                quantity = product.stock
                messages.warning(request, f'Quantity adjusted to available stock ({product.stock})')

            # Get selected weight for FREE WEIGHT products
            selected_weight = None
            adjusted_price = None
            if product.unit_category == 'FREE WEIGHT':
                selected_weight = request.POST.get('selected_weight', 'kg')
                adjusted_price = request.POST.get('adjusted_price')
                if adjusted_price:
                    adjusted_price = Decimal(adjusted_price)

            # Get or create cart item with selected weight
            cart_item, created = Cart.objects.get_or_create(
                user=request.user,
                product=product,
                selected_weight=selected_weight,
                defaults={
                    'quantity': quantity,
                    'price_override': adjusted_price
                }
            )
            
            if not created:
                cart_item.quantity += quantity
                # Update price override if it changed
                if adjusted_price and cart_item.price_override != adjusted_price:
                    cart_item.price_override = adjusted_price
                cart_item.save()

            weight_display = f" ({selected_weight})" if selected_weight else ""
            quantity_display = f" x{quantity}" if quantity > 1 else ""
            
            # Get updated cart count
            cart_count = Cart.objects.filter(user=request.user).count()
            
            # Check if this is an AJAX request
            is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
            if is_ajax:
                return JsonResponse({
                    'success': True,
                    'message': f'Product{weight_display}{quantity_display} added to cart',
                    'cart_count': cart_count
                })
            else:
                messages.success(request, f'Product{weight_display}{quantity_display} added to cart')
        except Exception as e:
            logger.error(f"Error adding product to cart: {str(e)}")
            messages.error(request, 'Error adding product to cart')
            
            # Check if this is an AJAX request
            is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
            if is_ajax:
                return JsonResponse({
                    'success': False,
                    'message': 'Error adding product to cart',
                })

    return redirect('GB_FARM:product_detail', product_id=product_id)

@login_required
def add_to_wishlist(request, product_id):
    """Add product to wishlist"""
    if request.method == 'POST':
        try:
            product = get_product_detail(product_id)
            Wishlist.objects.get_or_create(user=request.user, product=product)
            messages.success(request, 'Product added to wishlist')
        except Exception as e:
            logger.error(f"Error adding product to wishlist: {str(e)}")
            messages.error(request, 'Error adding product to wishlist')

    return redirect('GB_FARM:product_detail', product_id=product_id)

@login_required
def remove_from_wishlist(request, product_id):
    """Remove product from wishlist"""
    if request.method == 'POST':
        try:
            Wishlist.objects.filter(user=request.user, product_id=product_id).delete()
            messages.success(request, 'Product removed from wishlist')
        except Exception as e:
            logger.error(f"Error removing product from wishlist: {str(e)}")
            messages.error(request, 'Error removing product from wishlist')

    return redirect('GB_FARM:product_detail', product_id=product_id)

@login_required
def wishlist(request):
    """View wishlist"""
    wishlist_items = Wishlist.objects.filter(user=request.user).select_related('product')
    return render(request, 'GB_FARM/wishlist.html', {
        'wishlist_items': wishlist_items
    })

@login_required
def cart(request):
    """View cart"""
    try:
        if not request.user.is_authenticated:
            messages.error(request, 'Please login to view your cart')
            return redirect('GB_FARM:loginpage')
            
        cart_items = Cart.objects.filter(user=request.user).select_related('product')
        total = sum(item.total_price for item in cart_items)
        return render(request, 'GB_FARM/cart.html', {
            'cart_items': cart_items,
            'total': total
        })
    except Exception as e:
        logger.error(f"Error viewing cart: {str(e)}")
        messages.error(request, 'Error loading your cart')
        return redirect('GB_FARM:home')

@login_required
def checkout(request):
    """Checkout process"""
    try:
        if not request.user.is_authenticated:
            messages.error(request, 'Please login to checkout')
            return redirect('GB_FARM:loginpage')
            
        cart_items = Cart.objects.filter(user=request.user).select_related('product')
        if not cart_items:
            messages.error(request, 'Your cart is empty')
            return redirect('GB_FARM:cart')
            
        total = sum(item.total_price for item in cart_items)
        
        # Get active delivery zones
        zones = Zone.objects.filter(is_active=True)
        
        if request.method == 'POST':
            form = CheckoutForm(request.POST)
            if form.is_valid():
                order = Order.objects.create(
                    user=request.user,
                    total_amount=total,
                    shipping_address=form.cleaned_data['shipping_address'],
                    delivery_method=form.cleaned_data['delivery_method'],
                    customer_name=form.cleaned_data['full_name'],
                    phone_number=form.cleaned_data['phone_number'],
                    office_number=form.cleaned_data['office_number']
                )
                
                # Set delivery zone if delivery is selected
                if form.cleaned_data['delivery_method'] == 'delivery':
                    order.delivery_zone = form.cleaned_data['delivery_zone']
                    order.save()
                
                # Create order items
                for cart_item in cart_items:
                    OrderItem.objects.create(
                        order=order,
                        product=cart_item.product,
                        quantity=cart_item.quantity,
                        price=cart_item.product.price
                    )
                
                # Clear the cart
                cart_items.delete()
                
                # Create payment record
                Payment.objects.create(
                    order=order,
                    amount=order.final_total,
                    payment_method=form.cleaned_data['payment_method']
                )
                
                return redirect('GB_FARM:order_confirmation', order_id=order.id)
        else:
            # Pre-fill form with user data if available
            initial_data = {}
            if request.user.first_name or request.user.last_name:
                initial_data['full_name'] = f"{request.user.first_name} {request.user.last_name}".strip()
            if request.user.user_phone:
                initial_data['phone_number'] = request.user.user_phone
            
            form = CheckoutForm(initial=initial_data)
        
        return render(request, 'GB_FARM/checkout.html', {
            'form': form,
            'cart_items': cart_items,
            'total': total,
            'zones': zones
        })
        
    except Exception as e:
        logger.error(f"Error during checkout: {str(e)}")
        messages.error(request, 'An error occurred during checkout')
        return redirect('GB_FARM:cart')

@login_required
def order_confirmation(request, order_id):
    """Order confirmation page"""
    order = get_object_or_404(Order, id=order_id, user=request.user)
    return render(request, 'GB_FARM/order_confirmation.html', {
        'order': order
    })

@login_required
def order_history(request):
    """View order history"""
    orders = Order.objects.filter(user=request.user).order_by('-created_at').prefetch_related(
        'orderitem_set__product'
    )
    return render(request, 'GB_FARM/order_history.html', {
        'orders': orders
    })

@login_required
def add_review(request, product_id):
    """Add product review"""
    if request.method == 'POST':
        try:
            product = get_product_detail(product_id)
            rating = int(request.POST.get('rating'))
            comment = request.POST.get('comment')
            
            if not 1 <= rating <= 5:
                raise ValueError('Rating must be between 1 and 5')

            Review.objects.create(
                user=request.user,
                product=product,
                rating=rating,
                comment=comment
            )
            
            messages.success(request, 'Review added successfully')
        except Exception as e:
            logger.error(f"Error adding review: {str(e)}")
            messages.error(request, 'Error adding review')

    return redirect('GB_FARM:product_detail', product_id=product_id)

@login_required
def edit_review(request, product_id, review_id):
    """Edit a product review"""
    review = get_object_or_404(Review, id=review_id, user=request.user)
    product = get_object_or_404(Product, id=product_id)
    
    if request.method == 'POST':
        try:
            rating = int(request.POST.get('rating'))
            comment = request.POST.get('comment')
            
            if not 1 <= rating <= 5:
                raise ValueError('Rating must be between 1 and 5')

            review.rating = rating
            review.comment = comment
            review.save()
            
            messages.success(request, 'Review updated successfully')
            return redirect('GB_FARM:product_reviews', product_id=product_id)
            
        except Exception as e:
            logger.error(f"Error updating review: {str(e)}")
            messages.error(request, 'Error updating your review')
    
    return render(request, 'GB_FARM/edit_review.html', {
        'review': review,
        'product': product
    })

@login_required
def update_cart(request, item_id):
    """Update cart item quantity"""
    if request.method == 'POST':
        try:
            cart_item = get_object_or_404(Cart, id=item_id, user=request.user)
            quantity = int(request.POST.get('quantity', 1))
            
            # Check if this is an AJAX request
            is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
            
            # Validate quantity
            if quantity <= 0:
                cart_item.delete()
                if is_ajax:
                    return JsonResponse({
                        'success': True,
                        'message': 'Item removed from cart',
                        'removed': True,
                        'cart_total': sum(item.total_price for item in Cart.objects.filter(user=request.user))
                    })
                else:
                    messages.success(request, 'Item removed due to zero quantity')
                    return redirect('GB_FARM:cart')
            
            # Check stock availability
            if quantity > cart_item.product.stock:
                if is_ajax:
                    return JsonResponse({
                        'success': False,
                        'message': f'Only {cart_item.product.stock} units available in stock',
                        'max_quantity': cart_item.product.stock
                    })
                else:
                    messages.error(request, f'Only {cart_item.product.stock} units available in stock')
                    return redirect('GB_FARM:cart')
            
            # Update quantity
            cart_item.quantity = quantity
            cart_item.save()
            
            # Calculate new totals
            item_total = cart_item.total_price
            cart_total = sum(item.total_price for item in Cart.objects.filter(user=request.user))
            
            if is_ajax:
                return JsonResponse({
                    'success': True,
                    'message': 'Cart updated successfully',
                    'item_total': float(item_total),
                    'cart_total': float(cart_total),
                    'quantity': quantity
                })
            else:
                messages.success(request, 'Cart updated successfully')
        except Exception as e:
            logger.error(f"Error updating cart: {str(e)}")
            if is_ajax:
                return JsonResponse({
                    'success': False,
                    'message': 'Error updating cart'
                }, status=400)
            else:
                messages.error(request, 'Error updating cart')
    
    return redirect('GB_FARM:cart')

@login_required
def remove_from_cart(request, item_id):
    """Remove item from cart"""
    try:
        cart_item = get_object_or_404(Cart, id=item_id, user=request.user)
        cart_item.delete()
        
        # Check if this is an AJAX request
        is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
        
        if is_ajax:
            # Calculate the new cart total for AJAX response
            cart_total = sum(item.total_price for item in Cart.objects.filter(user=request.user))
            return JsonResponse({
                'success': True,
                'message': 'Item removed from cart',
                'cart_total': float(cart_total)
            })
        else:
            messages.success(request, 'Item removed from cart')
    except Exception as e:
        logger.error(f"Error removing item from cart: {str(e)}")
        
        if is_ajax:
            return JsonResponse({
                'success': False,
                'message': 'Error removing item from cart'
            }, status=400)
        else:
            messages.error(request, 'Error removing item from cart')
    
    return redirect('GB_FARM:cart')

@login_required
def clear_cart(request):
    """Clear all items from cart"""
    try:
        Cart.objects.filter(user=request.user).delete()
        messages.success(request, 'Cart cleared successfully')
    except Exception as e:
        logger.error(f"Error clearing cart: {str(e)}")
        messages.error(request, 'Error clearing cart')
    
    return redirect('GB_FARM:cart')

# API Views
@require_api_token
def api_product_list(request):
    """API endpoint for product list"""
    products = get_product_list()
    return JsonResponse({
        'products': [{
            'id': p.id,
            'name': p.name,
            'price': str(p.price),
            'stock': p.stock,
            'category': p.category.name if p.category else None,
            'rating': p.avg_rating,
            'review_count': p.review_count
        } for p in products]
    })

@require_api_token
def api_product_detail(request, product_id):
    """API endpoint for product detail"""
    product = get_product_detail(product_id)
    return JsonResponse({
        'id': product.id,
        'name': product.name,
        'price': str(product.price),
        'stock': product.stock,
        'category': product.category.name if product.category else None,
        'description': product.description,
        'rating': product.avg_rating,
        'review_count': product.review_count,
        'reviews': [{
            'user': r.user.username,
            'rating': r.rating,
            'comment': r.comment,
            'created_at': r.created_at.isoformat()
        } for r in product.reviews]
    })

# Authentication Views
def loginpage(request):
    """Handle user login"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(request, username=username, password=password)
        
        if user is not None:
            login(request, user)
            messages.success(request, 'Successfully logged in!')
            return redirect('GB_FARM:home')
        else:
            messages.error(request, 'Invalid username or password.')
    
    return render(request, 'GB_FARM/login.html')

def logoutuser(request):
    """Handle user logout"""
    logout(request)
    messages.success(request, 'Successfully logged out!')
    return redirect('GB_FARM:home')

def registerpage(request):
    """Handle user registration"""
    if request.method == 'POST':
        form = UserRegisterForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            messages.success(request, 'Account created successfully!')
            return redirect('GB_FARM:home')
        else:
            messages.error(request, 'Error creating account. Please try again.')
    else:
        form = UserRegisterForm()
    
    return render(request, 'GB_FARM/register.html', {'form': form})

def product_search(request):
    """Search products by name or description - now redirects to product_list"""
    query = request.GET.get('q', '')
    
    # Redirect to product_list with the search parameter
    return redirect(f"/products/?q={query}")

@login_required
def create_checkout_session(request, order_id=None):
    if request.method == 'POST':
        payment_method = request.POST.get('payment_method')
        
        try:
            # Get the order
            order = get_object_or_404(Order, id=order_id, user=request.user)
            cart_items = Cart.objects.filter(user=request.user)
            
            # Calculate total
            subtotal = sum(item.product.price * item.quantity for item in cart_items)
            delivery_fee = Decimal('5.00')
            total_with_delivery = subtotal + delivery_fee
            
            # Create payment record
            payment = Payment(
                order=order,
                amount=total_with_delivery,
                payment_method=payment_method,
                status='pending'
            )
            
            # Different flow based on payment method
            if payment_method == 'credit_card':
                # For demonstration, create a successful payment right away
                payment.status = 'completed'
                payment.transaction_id = f'TXN-{random.randint(1000000, 9999999)}'
                payment.save()
                
                # Update order status
                order.status = 'paid'
                order.save()
                
                # Clear the cart
                cart_items.delete()
                
                return redirect('GB_FARM:payment_success', order_id=order.id)
                
            elif payment_method == 'cash':
                # For cash on delivery
                payment.save()
                
                # Update order status
                order.status = 'processing'
                order.save()
                
                # Clear the cart
                cart_items.delete()
                
                return redirect('GB_FARM:payment_success', order_id=order.id)
                
            elif payment_method in ['wallet', 'instapay']:
                # For mobile wallet payments
                payment.save()
                
                # Update order status
                order.status = 'processing'
                order.save()
                
                # Clear the cart
                cart_items.delete()
                
                return redirect('GB_FARM:payment_success', order_id=order.id)
            
            else:
                messages.error(request, 'Invalid payment method')
                return redirect('GB_FARM:checkout')
                
        except Exception as e:
            messages.error(request, f'Error processing payment: {str(e)}')
            return redirect('GB_FARM:checkout')
    
    return redirect('GB_FARM:checkout')

@login_required
def payment_success(request, order_id):
    """Handle successful payment"""
    try:
        order = get_object_or_404(Order, id=order_id, user=request.user)
        payment = Payment.objects.filter(order=order).order_by('-timestamp').first()
        
        if not payment:
            # Create a default payment record if none exists
            payment = Payment.objects.create(
                order=order,
                amount=order.total_amount,
                payment_method='cash',
                status='completed'
            )
            
        return render(request, 'GB_FARM/payment_success.html', {
            'order': order,
            'payment': payment
        })
    except Exception as e:
        logger.error(f"Error showing payment success: {str(e)}")
        messages.error(request, 'Error loading payment details')
        return redirect('GB_FARM:order_history')

@login_required
def payment_cancel(request):
    """Handle cancelled payment"""
    messages.warning(request, 'Payment was cancelled')
    return redirect('GB_FARM:cart')

@login_required
def process_payment(request):
    """Process payment webhook"""
    # This will be implemented when integrating with a payment gateway
    pass

@login_required
def order_details(request, pk):
    """View order details"""
    order = get_object_or_404(Order, id=pk, user=request.user)
    order_items = OrderItem.objects.filter(order=order).select_related('product')
    
    # Check for unread messages from admin
    unread_messages_count = OrderMessage.objects.filter(
        order=order,
        sender__user_type='admin',
        is_read=False
    ).count()
    
    # Calculate subtotal
    subtotal = sum(item.quantity * item.price for item in order_items)
    
    # Determine shipping cost based on delivery method and zone
    shipping_cost = Decimal('0.00')
    if order.delivery_method == 'delivery' and order.delivery_zone:
        shipping_cost = order.delivery_zone.price
    elif hasattr(order, 'shipping_cost'): # Keep backward compatibility if field exists
         shipping_cost = order.shipping_cost

    # Calculate total
    total = subtotal + shipping_cost

    # Get the latest payment associated with the order
    payment = Payment.objects.filter(order=order).order_by('-timestamp').first()
    
    return render(request, 'GB_FARM/order_details.html', {
        'order': order,
        'order_items': order_items,
        'unread_messages_count': unread_messages_count,
        'subtotal': subtotal,
        'shipping_cost': shipping_cost,
        'total': total, # Use the calculated total
        'payment': payment, # Add payment to context
    })

@login_required
def track_order(request):
    """Track order status"""
    order_id = request.GET.get('order_id')
    if order_id:
        try:
            order = Order.objects.get(id=order_id, user=request.user)
            order_items = OrderItem.objects.filter(order=order).select_related('product') # Fetch order items and prefetch related product
            return render(request, 'GB_FARM/track_order.html', {
                'order': order,
                'order_items': order_items, # Pass order items to template
            })
        except Order.DoesNotExist:
            messages.error(request, 'Order not found')
    
    return render(request, 'GB_FARM/track_order.html')

@login_required
def product_review(request, product_id):
    """View and manage product reviews"""
    product = get_object_or_404(Product, id=product_id)
    reviews = Review.objects.filter(product=product).select_related('user').order_by('-created_at')
    
    if request.method == 'POST':
        try:
            rating = int(request.POST.get('rating'))
            comment = request.POST.get('comment')
            
            if not 1 <= rating <= 5:
                raise ValueError('Rating must be between 1 and 5')

            # Check if user has already reviewed this product
            existing_review = Review.objects.filter(user=request.user, product=product).first()
            if existing_review:
                existing_review.rating = rating
                existing_review.comment = comment
                existing_review.save()
                messages.success(request, 'Review updated successfully')
            else:
                Review.objects.create(
                    user=request.user,
                    product=product,
                    rating=rating,
                    comment=comment
                )
                messages.success(request, 'Review added successfully')
            
            return redirect('GB_FARM:product_reviews', product_id=product_id)
            
        except Exception as e:
            logger.error(f"Error handling review: {str(e)}")
            messages.error(request, 'Error processing your review')
    
    # Calculate average rating and rating distribution for the reviews.html template
    average_rating = reviews.aggregate(Avg('rating'))['rating__avg'] or 0
    
    # Count reviews by rating
    rating_counts = []
    rating_percentages = []
    for i in range(5, 0, -1):
        count = reviews.filter(rating=i).count()
        rating_counts.append(count)
        percentage = (count / reviews.count() * 100) if reviews.count() > 0 else 0
        rating_percentages.append(percentage)
    
    return render(request, 'GB_FARM/reviews.html', {
        'product': product,
        'reviews': reviews,
        'user_review': reviews.filter(user=request.user).first() if request.user.is_authenticated else None,
        'average_rating': average_rating,
        'rating_counts': rating_counts,
        'rating_percentages': rating_percentages
    })

@login_required
def delete_review(request, review_id):
    """Delete a product review"""
    review = get_object_or_404(Review, id=review_id, user=request.user)
    product_id = review.product.id
    
    if request.method == 'POST':
        try:
            review.delete()
            messages.success(request, 'Review deleted successfully')
        except Exception as e:
            logger.error(f"Error deleting review: {str(e)}")
            messages.error(request, 'Error deleting your review')
    
    return redirect('GB_FARM:product_reviews', product_id=product_id)

@login_required
def admin_order_dashboard(request):
    """Admin dashboard for managing orders"""
    if not request.user.is_staff:
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('GB_FARM:home')
    
    # Get filter parameters
    status = request.GET.get('status')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    # Base queryset
    orders = Order.objects.select_related('user').order_by('-created_at')
    
    # Apply filters
    if status:
        orders = orders.filter(status=status)
    if date_from:
        orders = orders.filter(created_at__gte=date_from)
    if date_to:
        orders = orders.filter(created_at__lte=date_to)
    
    # Get statistics
    total_orders = Order.objects.count()
    
    # Revenue calculations based on delivered orders only for consistency
    delivered_orders = Order.objects.filter(status='delivered')
    total_revenue = delivered_orders.aggregate(
        total=Sum('total_amount')
    )['total'] or 0
    
    delivered_orders_count = delivered_orders.count()
    
    # Calculate average order value based on delivered orders
    average_order_value = 0
    if delivered_orders_count > 0:
        average_order_value = total_revenue / delivered_orders_count
    
    # Store display value separately for total orders card
    display_total_orders = total_orders # Show all orders in the card
    
    status_counts = Order.objects.values('status').annotate(
        count=Count('id')
    ).order_by('status')
    
    # Get recent orders for the table
    recent_orders = orders[:10]
    
    context = {
        'orders': recent_orders,
        'total_orders': display_total_orders,
        'total_revenue': total_revenue,
        'average_order_value': average_order_value,
        'status_counts': status_counts,
        'status_filter': status,
        'date_from': date_from,
        'date_to': date_to,
        'STATUS_CHOICES': Order.STATUS_CHOICES,
    }
    
    return render(request, 'GB_FARM/admin_order_dashboard.html', context)

@login_required
def update_order_status(request, order_id):
    """Update order status"""
    if not request.user.is_staff:
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('GB_FARM:home')
    
    try:
        order = get_object_or_404(Order, id=order_id)
        
        if request.method == 'POST':
            new_status = request.POST.get('status')
            if new_status in dict(Order.STATUS_CHOICES):
                order.status = new_status
                order.save()
                messages.success(request, f'Order status updated to {order.get_status_display()}')
                
                # Check if this is an AJAX request
                is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
                if is_ajax:
                    return JsonResponse({
                        'success': True,
                        'message': f'Order status updated to {order.get_status_display()}'
                    })
            else:
                messages.error(request, 'Invalid status')
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'success': False,
                        'message': 'Invalid status'
                    })
        
        # Redirect back to the order detail page if coming from there
        referer = request.META.get('HTTP_REFERER', '')
        if 'orders/' + str(order_id) in referer:
            return redirect('GB_FARM:admin_order_detail', order_id=order_id)
        
        return redirect('GB_FARM:admin_order_dashboard')
        
    except Exception as e:
        logger.error(f"Error updating order status: {str(e)}")
        messages.error(request, 'Error updating order status')
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'message': 'Error updating order status'
            }, status=500)
        return redirect('GB_FARM:admin_order_dashboard')

@login_required
def admin_order_detail(request, order_id):
    """Admin interface to view order details"""
    # Check if user is admin
    if not request.user.user_type == 'admin':
        messages.error(request, 'You do not have permission to access this page')
        return redirect('GB_FARM:home')
    
    order = get_object_or_404(Order, id=order_id)
    order_items = OrderItem.objects.filter(order=order).select_related('product')
    
    # Calculate subtotal
    subtotal = sum(item.quantity * item.price for item in order_items)
    
    # Determine shipping cost based on delivery method and zone
    shipping_cost = Decimal('0.00')
    if order.delivery_method == 'delivery' and order.delivery_zone:
        shipping_cost = order.delivery_zone.price
    elif hasattr(order, 'shipping_cost'): # Keep backward compatibility if field exists
         shipping_cost = order.shipping_cost

    # Calculate total
    total = subtotal + shipping_cost

    # Check for any unread messages
    unread_messages_count = OrderMessage.objects.filter(
        order=order, 
        sender=order.user,  # Only count messages from the customer
        is_read=False
    ).count()
    
    # Get the latest payment associated with the order
    payment = Payment.objects.filter(order=order).order_by('-timestamp').first()
    
    return render(request, 'GB_FARM/admin_order_detail.html', {
        'order': order,
        'order_items': order_items,
        'unread_messages_count': unread_messages_count,
        'subtotal': subtotal,
        'shipping_cost': shipping_cost,
        'total': total, # Use the calculated total
        'payment': payment, # Add payment to context
    })

@login_required
def export_orders(request):
    """Export orders to CSV"""
    if not request.user.is_staff:
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('GB_FARM:home')
    
    try:
        import csv
        from django.http import HttpResponse
        from datetime import datetime
        
        # Get filter parameters
        status = request.GET.get('status')
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')
        
        # Base queryset
        orders = Order.objects.select_related('user').order_by('-created_at')
        
        # Apply filters
        if status:
            orders = orders.filter(status=status)
        if date_from:
            orders = orders.filter(created_at__gte=date_from)
        if date_to:
            orders = orders.filter(created_at__lte=date_to)
        
        # Create the HttpResponse object with CSV header
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="orders_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'
        
        # Create CSV writer
        writer = csv.writer(response)
        
        # Write header row
        writer.writerow([
            'Order ID',
            'Customer',
            'Email',
            'Phone',
            'Status',
            'Total Amount',
            'Created At',
            'Shipping Address'
        ])
        
        # Write data rows
        for order in orders:
            writer.writerow([
                order.id,
                order.user.username,
                order.user.email,
                order.user.user_phone if hasattr(order.user, 'user_phone') else '',
                order.get_status_display(),
                order.total_amount,
                order.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                order.shipping_address
            ])
        
        return response
        
    except Exception as e:
        logger.error(f"Error exporting orders: {str(e)}")
        messages.error(request, 'Error exporting orders')
        return redirect('GB_FARM:admin_order_dashboard')

@login_required
def admin_order_statistics(request):
    """Admin dashboard for order statistics"""
    if not request.user.is_staff:
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('GB_FARM:home')
    
    try:
        # Get date range parameters
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')
        
        # Base queryset
        orders = Order.objects.select_related('user')
        
        # Apply date filters if provided
        if date_from:
            orders = orders.filter(created_at__gte=date_from)
        if date_to:
            orders = orders.filter(created_at__lte=date_to)
        
        # Calculate statistics
        total_orders = orders.count()
        total_revenue = orders.filter(status__in=['processing', 'shipped', 'delivered']).aggregate(
            total=Sum('total_amount')
        )['total'] or 0
        
        # Status distribution
        status_counts = orders.values('status').annotate(
            count=Count('id')
        ).order_by('status')
        
        # Daily orders count
        daily_orders = orders.annotate(
            date=TruncDate('created_at')
        ).values('date').annotate(
            count=Count('id')
        ).order_by('date')
        
        # Daily revenue
        daily_revenue = orders.filter(
            status__in=['processing', 'shipped', 'delivered']
        ).annotate(
            date=TruncDate('created_at')
        ).values('date').annotate(
            total=Sum('total_amount')
        ).order_by('date')
        
        # Average order value
        avg_order_value = orders.filter(status__in=['processing', 'shipped', 'delivered']).aggregate(
            avg=Avg('total_amount')
        )['avg'] or 0
        
        # Most popular products
        popular_products = OrderItem.objects.filter(
            order__status__in=['processing', 'shipped', 'delivered']
        ).values(
            'product__name'
        ).annotate(
            total_quantity=Sum('quantity'),
            total_revenue=Sum(F('quantity') * F('price'))
        ).order_by('-total_quantity')[:10]
        
        context = {
            'total_orders': total_orders,
            'total_revenue': total_revenue,
            'status_counts': status_counts,
            'daily_orders': daily_orders,
            'daily_revenue': daily_revenue,
            'avg_order_value': avg_order_value,
            'popular_products': popular_products,
            'date_from': date_from,
            'date_to': date_to
        }
        
        return render(request, 'GB_FARM/admin_order_statistics.html', context)
        
    except Exception as e:
        logger.error(f"Error generating order statistics: {str(e)}")
        messages.error(request, 'Error generating order statistics')
        return redirect('GB_FARM:admin_order_dashboard')

@login_required
def admin_order_message(request, order_id):
    """Admin interface to send messages about an order"""
    # Check if user is admin
    if not request.user.user_type == 'admin':
        messages.error(request, 'You do not have permission to access this page')
        return redirect('GB_FARM:home')
    
    order = get_object_or_404(Order, id=order_id)
    order_items = OrderItem.objects.filter(order=order).select_related('product')
    order_messages = OrderMessage.objects.filter(order=order).order_by('-created_at')
    
    if request.method == 'POST':
        message_text = request.POST.get('message')
        if message_text:
            # Create the message
            OrderMessage.objects.create(
                order=order,
                sender=request.user,
                message=message_text
            )
            messages.success(request, 'Message sent successfully')
            return redirect('GB_FARM:admin_order_message', order_id=order_id)
        else:
            messages.error(request, 'Message cannot be empty')
    
    return render(request, 'GB_FARM/admin_order_message.html', {
        'order': order,
        'order_items': order_items,
        'order_messages': order_messages,
    })

@login_required
def order_messages(request, order_id):
    """View messages for a specific order"""
    order = get_object_or_404(Order, id=order_id, user=request.user)
    order_messages = OrderMessage.objects.filter(order=order).order_by('-created_at')
    
    # Mark messages as read
    order_messages.filter(is_read=False).update(is_read=True)
    
    # If customer wants to reply
    if request.method == 'POST':
        message_text = request.POST.get('message')
        if message_text:
            # Create the message
            OrderMessage.objects.create(
                order=order,
                sender=request.user,
                message=message_text
            )
            messages.success(request, 'Message sent successfully')
            return redirect('GB_FARM:order_messages', order_id=order_id)
        else:
            messages.error(request, 'Message cannot be empty')
    
    return render(request, 'GB_FARM/order_messages.html', {
        'order': order,
        'order_messages': order_messages,
    })

@login_required
def admin_dashboard(request):
    """Admin main dashboard view"""
    # Check if user is admin
    if not request.user.user_type == 'admin':
        messages.error(request, 'You do not have permission to access this page')
        return redirect('GB_FARM:home')
    
    # Get summary statistics
    total_orders = Order.objects.count()
    recent_orders = Order.objects.order_by('-created_at')[:5]
    total_products = Product.objects.count()
    total_customers = User.objects.filter(user_type='customer').count()
    
    # Get unread messages count
    unread_messages = OrderMessage.objects.filter(
        sender__user_type='customer',
        is_read=False
    ).count()
    
    # Get orders by status for quick stats
    pending_orders = Order.objects.filter(status='pending').count()
    processing_orders = Order.objects.filter(status='processing').count()
    shipped_orders = Order.objects.filter(status='shipped').count()
    delivered_orders = Order.objects.filter(status='delivered').count()
    
    context = {
        'total_orders': total_orders,
        'recent_orders': recent_orders,
        'total_products': total_products,
        'total_customers': total_customers,
        'unread_messages': unread_messages,
        'pending_orders': pending_orders,
        'processing_orders': processing_orders,
        'shipped_orders': shipped_orders,
        'delivered_orders': delivered_orders
    }
    
    return render(request, 'GB_FARM/admin_dashboard.html', context)

def Delivery_view(request):
    """Delivery view"""
    return render(request, 'GB_FARM/Delivery.html')

@login_required
@staff_member_required
def admin_zone_create(request):
    if request.method == 'POST':
        form = ZoneForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Zone created successfully.')
            return redirect('GB_FARM:admin_product_dashboard')
    else:
        form = ZoneForm()
    
    return render(request, 'GB_FARM/admin_zone_create.html', {
        'form': form
    })

@login_required
@staff_member_required
def admin_zone_list(request):
    """Admin interface to list all zones"""
    zones = Zone.objects.all().order_by('-created_at')
    return render(request, 'GB_FARM/admin_zone_list.html', {
        'zones': zones
    })

@login_required
@staff_member_required
def admin_zone_edit(request, zone_id):
    """Admin interface to edit a zone"""
    zone = get_object_or_404(Zone, id=zone_id)
            
    if request.method == 'POST':
        form = ZoneForm(request.POST, instance=zone)
        if form.is_valid():
            form.save()
            messages.success(request, 'Zone updated successfully.')
            return redirect('GB_FARM:admin_zone_list')
    else:
        form = ZoneForm(instance=zone)
    
    return render(request, 'GB_FARM/admin_zone_edit.html', {
        'form': form,
        'zone': zone
    })

@login_required
@staff_member_required
def admin_zone_delete(request, zone_id):
    """Admin interface to delete a zone"""
    zone = get_object_or_404(Zone, id=zone_id)
    
    if request.method == 'POST':
        try:
            # Check if zone is being used in any orders
            if zone.orders.exists():
                # Just mark it as inactive instead of deleting
                zone.is_active = False
                zone.save()
                messages.success(request, 'Zone marked as inactive (cannot be deleted because it has orders).')
            else:
                zone.delete()
                messages.success(request, 'Zone deleted successfully.')
        except Exception as e:
            logger.error(f"Error deleting zone: {str(e)}")
            messages.error(request, 'Error deleting zone.')
    
    return redirect('GB_FARM:admin_zone_list')

@require_POST
def stripe_webhook(request):
    """Handle Stripe webhook events"""
    try:
        import stripe
        from django.conf import settings
        
        stripe.api_key = settings.STRIPE_SECRET_KEY
        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
        
        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
            )
        except ValueError as e:
            logger.error(f"Invalid payload: {str(e)}")
            return HttpResponse(status=400)
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Invalid signature: {str(e)}")
            return HttpResponse(status=400)
        
        # Handle the event
        if event['type'] == 'checkout.session.completed':
            session = event['data']['object']
            order_id = session.get('metadata', {}).get('order_id')
            if order_id:
                try:
                    order = Order.objects.get(id=order_id)
                    order.status = 'processing'
                    order.save()
                    logger.info(f"Order {order_id} marked as processing")
                except Order.DoesNotExist:
                    logger.error(f"Order {order_id} not found")
        
        return HttpResponse(status=200)
        
    except Exception as e:
        logger.error(f"Error processing webhook: {str(e)}")
        return HttpResponse(status=500)

def about_view(request):
    """About Us page"""
    # Get the active about information from database
    about_info = About.objects.filter(is_active=True).first()

    # Get Corporate Social Responsibility information
    corporate_social_info = CorporateSocial.objects.first()

    context = {
        'about_info': about_info,
        'corporate_social_info': corporate_social_info,
        **get_common_context(request)
    }
    return render(request, 'GB_FARM/about.html', context)

def contact_view(request):
    """Contact Us page"""
    # Get active contact information
    contact_info = ContactInfo.objects.filter(is_active=True).first()
    
    context = {
        'contact_info': contact_info,
        **get_common_context(request)
    }
    return render(request, 'GB_FARM/contact.html', context)

@login_required
def admin_product_dashboard(request):
    """Admin dashboard for managing products"""
    # Check if user is admin
    if not request.user.user_type == 'admin':
        messages.error(request, 'You do not have permission to access this page')
        return redirect('GB_FARM:home')
    
    # Get filter parameters
    category_id = request.GET.get('category')
    status = request.GET.get('status')
    search_query = request.GET.get('q')
    
    # Base queryset
    products = Product.objects.select_related('category').order_by('-created_at')
    
    # Apply filters
    if category_id:
        products = products.filter(category_id=category_id)
    if status:
        products = products.filter(status=status)
    if search_query:
        products = products.filter(
            Q(name__icontains=search_query) | 
            Q(description__icontains=search_query)
        )
    
    # Paginate results
    paginator = Paginator(products, 10)  # 10 products per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get statistics
    total_products = Product.objects.count()
    out_of_stock = Product.objects.filter(stock=0).count()
    low_stock = Product.objects.filter(stock__gt=0, stock__lte=5).count()
    total_categories = Category.objects.count()
    
    # Get categories for filter dropdown
    categories = Category.objects.all()
    
    context = {
        'products': page_obj,
        'total_products': total_products,
        'out_of_stock': out_of_stock,
        'low_stock': low_stock,
        'total_categories': total_categories,
        'categories': categories,
        'category_id': category_id,
        'status': status,
        'search_query': search_query,
        'status_choices': Product.STATUS_CHOICES,
    }
    
    return render(request, 'GB_FARM/admin_product_dashboard.html', context)

@login_required
@user_passes_test(is_admin)
def admin_product_create(request):
    if request.method == 'POST':
        try:
            name = request.POST.get('name')
            price = request.POST.get('price')
            category_id = request.POST.get('category')
            description = request.POST.get('description')
            stock = request.POST.get('stock', 0)
            unit_category = request.POST.get('unit_category', 'PIECE')
            available_units_selected = request.POST.getlist('available_units')
            media_type = request.POST.get('media_type', 'image')
            status = request.POST.get('status', 'available')
            is_featured = request.POST.get('is_featured') == 'on'
            
            # Validate required fields
            if not name or not price:
                raise ValueError('Name and price are required')
            
            # Create the product
            product = Product(
                name=name,
                price=price,
                description=description,
                stock=stock,
                unit_category=unit_category,
                available_units=available_units_selected,
                status=status,
                is_featured=is_featured
            )
            
            # Set category if provided
            if category_id:
                product.category = get_object_or_404(Category, id=category_id)
            
            # Handle media upload based on type
            if media_type == 'image' and 'image' in request.FILES:
                product.image = request.FILES['image']
            elif media_type == 'video' and 'video' in request.FILES:
                product.video = request.FILES['video']
            
            product.save()
            messages.success(request, 'Product created successfully')
            return redirect('GB_FARM:admin_product_dashboard')
            
        except Exception as e:
            logger.error(f"Error creating product: {str(e)}")
            messages.error(request, f'Error creating product: {str(e)}')
    else:
        form = ProductForm()
        
    categories = Category.objects.all()
    context = {
        'form': form,
        'categories': categories,
        'unit_categories': Product.UNIT_CATEGORY,
        'all_unit_choices_json': json.dumps(ALL_UNIT_CHOICES),
        'current_available_units_json': json.dumps([])
    }
    return render(request, 'GB_FARM/admin_product_form.html', context)

@login_required
@user_passes_test(is_admin)
def admin_product_edit(request, product_id):
    """Admin interface to edit a product"""
    # Check if user is admin
    if not request.user.user_type == 'admin':
        messages.error(request, 'You do not have permission to access this page')
        return redirect('GB_FARM:home')
    
    product = get_object_or_404(Product, id=product_id)
    
    if request.method == 'POST':
        try:
            # Update product fields
            product.name = request.POST.get('name')
            product.price = request.POST.get('price')
            product.description = request.POST.get('description')
            product.stock = request.POST.get('stock', 0)
            product.unit_category = request.POST.get('unit_category', product.unit_category)
            product.status = request.POST.get('status', product.status)
            product.is_featured = request.POST.get('is_featured') == 'on'
            media_type = request.POST.get('media_type', 'image')
            
            # Update category if provided
            category_id = request.POST.get('category')
            if category_id:
                product.category = get_object_or_404(Category, id=category_id)
            
            # Handle media upload based on type
            if media_type == 'image' and 'image' in request.FILES:
                product.image = request.FILES['image']
                # Clear video if switching to image
                product.video = None
            elif media_type == 'video' and 'video' in request.FILES:
                product.video = request.FILES['video']
                # Clear image if switching to video
                product.image = None
            
            product.available_units = request.POST.getlist('available_units')
            
            product.save()
            messages.success(request, 'Product updated successfully')
            return redirect('GB_FARM:admin_product_dashboard')
            
        except Exception as e:
            logger.error(f"Error updating product: {str(e)}")
            messages.error(request, f'Error updating product: {str(e)}')
    
    # Get categories for form
    categories = Category.objects.all()
    form = ProductForm(instance=product)
    context = {
        'product': product,
        'form': form,
        'categories': categories,
        'unit_categories': Product.UNIT_CATEGORY,
        'all_unit_choices_json': json.dumps(ALL_UNIT_CHOICES),
        'current_available_units_json': json.dumps(product.available_units or [])
    }
    return render(request, 'GB_FARM/admin_product_form.html', context)

@login_required
def admin_product_delete(request, product_id):
    """Admin interface to delete a product"""
    # Check if user is admin
    if not request.user.user_type == 'admin':
        messages.error(request, 'You do not have permission to access this page')
        return redirect('GB_FARM:home')
    
    careers = Career.objects.filter(is_active=True).order_by('name')
    
    # Filter by status if provided
    status = request.GET.get('status')
    if status == 'active':
        careers = careers.filter(is_active=True)
    elif status == 'inactive':
        careers = careers.filter(is_active=False)
    
    # Filter by job type if provided
    job_type = request.GET.get('job_type')
    if job_type:
        careers = careers.filter(job_type=job_type)
    
    # Search query
    search_query = request.GET.get('q')
    if search_query:
        careers = careers.filter(
            Q(title__icontains=search_query) | 
            Q(department__icontains=search_query) |
            Q(location__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    # Paginate results
    paginator = Paginator(careers, 10)  # 10 careers per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'careers': page_obj,
        'total_careers': careers.count(),
        'active_careers': Career.objects.filter(is_active=True).count(),
        'inactive_careers': Career.objects.filter(is_active=False).count(),
        'job_types': Career.JOB_TYPE_CHOICES,
        'selected_status': status,
        'selected_job_type': job_type,
        'search_query': search_query,
    }
    
    return render(request, 'GB_FARM/internal/admin_career_dashboard.html', context)

@login_required
def admin_career_create(request):
    """Admin interface to create a new career opportunity"""
    # Check if user is admin
    if not request.user.user_type == 'admin':
        messages.error(request, 'You do not have permission to access this page')
        return redirect('GB_FARM:home')
    
    if request.method == 'POST':
        # Process form submission
        try:
            career = Career(
                title=request.POST.get('title'),
                title_ar=request.POST.get('title_ar'),
                department=request.POST.get('department'),
                department_ar=request.POST.get('department_ar'),
                location=request.POST.get('location'),
                location_ar=request.POST.get('location_ar'),
                job_type=request.POST.get('job_type'),
                experience_level=request.POST.get('experience_level'),
                description=request.POST.get('description'),
                description_ar=request.POST.get('description_ar'),
                requirements=request.POST.get('requirements'),
                requirements_ar=request.POST.get('requirements_ar'),
                responsibilities=request.POST.get('responsibilities'),
                responsibilities_ar=request.POST.get('responsibilities_ar'),
                benefits=request.POST.get('benefits'),
                benefits_ar=request.POST.get('benefits_ar'),
                is_active=request.POST.get('is_active') == 'on'
            )
            career.save()
            messages.success(request, 'Career opportunity created successfully')
            return redirect('GB_FARM:admin_career_dashboard')
        except Exception as e:
            messages.error(request, f'Error creating career opportunity: {str(e)}')
    
    context = {
        'job_types': Career.JOB_TYPE_CHOICES,
        'experience_levels': Career.EXPERIENCE_LEVEL_CHOICES
    }
    
    return render(request, 'GB_FARM/internal/admin_career_create.html', context)

@login_required
def admin_career_edit(request, id):
    """Admin interface to edit a career opportunity"""
    # Check if user is admin
    if not request.user.user_type == 'admin':
        messages.error(request, 'You do not have permission to access this page')
        return redirect('GB_FARM:home')
    
    career = get_object_or_404(Career, id=id)
    
    if request.method == 'POST':
        # Process form submission
        try:
            career.name = request.POST.get('name')
            career.name_ar = request.POST.get('name_ar')
            career.department = request.POST.get('department')
            career.department_ar = request.POST.get('department_ar')
            career.location = request.POST.get('location')
            career.location_ar = request.POST.get('location_ar')
            career.job_type = request.POST.get('job_type')
            career.experience_level = request.POST.get('experience_level')
            career.description = request.POST.get('description')
            career.description_ar = request.POST.get('description_ar')
            career.requirements = request.POST.get('requirements')
            career.requirements_ar = request.POST.get('requirements_ar')
            career.responsibilities = request.POST.get('responsibilities')
            career.responsibilities_ar = request.POST.get('responsibilities_ar')
            career.benefits = request.POST.get('benefits')
            career.benefits_ar = request.POST.get('benefits_ar')
            career.is_active = request.POST.get('is_active') == 'on'
            
            career.save()
            messages.success(request, 'Career opportunity updated successfully')
            return redirect('GB_FARM:admin_career_dashboard')
        except Exception as e:
            messages.error(request, f'Error updating career opportunity: {str(e)}')
    
    context = {
        'career': career,
        'job_types': Career.JOB_TYPE_CHOICES,
        'experience_levels': Career.EXPERIENCE_LEVEL_CHOICES
    }
    
    return render(request, 'GB_FARM/internal/admin_career_edit.html', context)

@login_required
def admin_career_delete(request, id):
    """Admin interface to delete a career opportunity"""
    # Check if user is admin
    if not request.user.user_type == 'admin':
        messages.error(request, 'You do not have permission to access this page')
        return redirect('GB_FARM:home')
    
    career = get_object_or_404(Career, id=id)
    
    if request.method == 'POST':
        try:
            career.delete()
            messages.success(request, 'Career opportunity deleted successfully')
            return redirect('GB_FARM:admin_career_dashboard')
        except Exception as e:
            messages.error(request, f'Error deleting career opportunity: {str(e)}')
    
    return render(request, 'GB_FARM/internal/admin_career_delete.html', {'career': career})

@require_POST
def submit_career_application(request, career_id):
    """Handle career application form submissions"""
    try:
        career = get_object_or_404(Career, id=career_id, is_active=True)
        
        full_name = request.POST.get('full_name')
        email = request.POST.get('email')
        phone_number = request.POST.get('phone_number')
        cover_letter = request.POST.get('cover_letter')
        years_of_experience = request.POST.get('years_of_experience')
        current_position = request.POST.get('current_position')
        linkedin_profile = request.POST.get('linkedin_profile')
        portfolio_website = request.POST.get('portfolio_website')
        salary_expectation = request.POST.get('salary_expectation')
        available_start_date = request.POST.get('available_start_date')
        
        # Get client IP
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip_address = x_forwarded_for.split(',')[0]
        else:
            ip_address = request.META.get('REMOTE_ADDR')
        
        # Create application
        application = CareerApplication(
            career=career,
            full_name=full_name,
            email=email,
            phone_number=phone_number,
            cover_letter=cover_letter,
            current_position=current_position,
            linkedin_profile=linkedin_profile,
            portfolio_website=portfolio_website,
            salary_expectation=salary_expectation,
            ip_address=ip_address
        )
        
        # Handle years of experience
        if years_of_experience:
            try:
                application.years_of_experience = int(years_of_experience)
            except ValueError:
                pass
        
        # Handle available start date
        if available_start_date:
            try:
                from datetime import datetime
                application.available_start_date = datetime.strptime(available_start_date, '%Y-%m-%d').date()
            except ValueError:
                pass
        
        # Handle CV file upload
        if 'cv_file' in request.FILES:
            application.cv_file = request.FILES['cv_file']
        
        application.save()
        
        # Return JSON response for AJAX or redirect for regular form
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': True, 'message': 'Application submitted successfully!'})
        else:
            messages.success(request, 'Your application has been submitted successfully! We will review it and get back to you soon.')
            return redirect('GB_FARM:external_career')
            
    except Exception as e:
        logger.error(f"Error in submit_career_application: {str(e)}")
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': False, 'message': 'An error occurred. Please try again.'})
        else:
            messages.error(request, 'An error occurred. Please try again.')
            return redirect('GB_FARM:external_career')

@login_required
@user_passes_test(is_admin)
def admin_career_applications_dashboard(request):
    """Admin dashboard for managing career applications"""
    applications = CareerApplication.objects.select_related('career').order_by('-created_at')
    
    # Filter by processing status
    status = request.GET.get('status')
    if status == 'processed':
        applications = applications.filter(is_processed=True)
    elif status == 'unprocessed':
        applications = applications.filter(is_processed=False)
    
    # Filter by career position
    career_id = request.GET.get('career')
    if career_id:
        applications = applications.filter(career_id=career_id)
    
    # Search query
    search_query = request.GET.get('q')
    if search_query:
        applications = applications.filter(
            Q(full_name__icontains=search_query) | 
            Q(email__icontains=search_query) |
            Q(career__name__icontains=search_query)
        )
    
    # Paginate results
    paginator = Paginator(applications, 20)  # 20 applications per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get careers for filter dropdown
    careers = Career.objects.filter(is_active=True).order_by('name')
    
    context = {
        'applications': page_obj,
        'total_applications': applications.count(),
        'processed_applications': CareerApplication.objects.filter(is_processed=True).count(),
        'unprocessed_applications': CareerApplication.objects.filter(is_processed=False).count(),
        'careers': careers,
        'selected_status': status,
        'selected_career': career_id,
        'search_query': search_query,
    }
    
    return render(request, 'GB_FARM/internal/admin_career_applications_dashboard.html', context)

@login_required
@user_passes_test(is_admin)
def admin_career_application_detail(request, application_id):
    """Admin view for detailed career application information"""
    application = get_object_or_404(CareerApplication, id=application_id)
    
    if request.method == 'POST':
        # Update processing status and notes
        application.is_processed = request.POST.get('is_processed') == 'on'
        application.notes = request.POST.get('notes', '')
        application.save()
        messages.success(request, 'Application updated successfully!')
        return redirect('GB_FARM:admin_career_application_detail', application_id=application_id)
    
    context = {
        'application': application,
    }
    
    return render(request, 'GB_FARM/internal/admin_career_application_detail.html', context)

def contact_us_message(request):
    if request.method == 'POST':
        form = ContactUsMessageForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Your message has been sent successfully!')
            return redirect('GB_FARM:home')  # Redirect to home or a thank you page
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = ContactUsMessageForm()

    context = {
        'form': form,
        **get_common_context(request)
    }
    return render(request, 'GB_FARM/contact_us_form.html', context)

def external_about_view(request):
    """External About Us page for unauthenticated users."""
    about_info = About.objects.first()
    about_us_video = AboutUsVideo.objects.first()
    flags = CountryFlag.objects.all().order_by('continent', 'country_name')
    grouped_flags = {
        'europe': [f for f in flags if f.continent == 'europe'],
        'africa': [f for f in flags if f.continent == 'africa'],
        'gulf': [f for f in flags if f.continent == 'gulf'],
        'asia': [f for f in flags if f.continent == 'asia'],
    }
    
    corporate_social_info = CorporateSocial.objects.first() # Retrieve the single instance
  
    context = get_common_context(request)
    context['about_info'] = about_info
    context['about_us_video'] = about_us_video
    context['grouped_flags'] = grouped_flags
    context['corporate_social_info'] = corporate_social_info # Add to context
    return render(request, 'Extrnal.html/about.html', context)

def external_career_view(request):
    """External Careers page for unauthenticated users."""
    if request.method == 'POST':
        try:
            # Get form data from the template
            full_name = request.POST.get('full_name', '').strip()
            email = request.POST.get('email', '').strip()
            phone_number = request.POST.get('phone_number', '').strip()
            cover_letter = request.POST.get('cover_letter', '').strip()
            subject = request.POST.get('subject', '').strip()  # From the form template
            years_of_experience = request.POST.get('years_of_experience', '').strip()
            current_position = request.POST.get('current_position', '').strip()
            linkedin_profile = request.POST.get('linkedin_profile', '').strip()
            portfolio_website = request.POST.get('portfolio_website', '').strip()
            salary_expectation = request.POST.get('salary_expectation', '').strip()
            available_start_date = request.POST.get('available_start_date', '').strip()
            
            # Validate required fields
            if not full_name or not email:
                messages.error(request, 'Name and email are required.')
                return redirect('GB_FARM:external_career')
            
            # Get the first active career or create a default one
            career = Career.objects.filter(is_active=True).first()
            if not career:
                # Create a default career if none exists
                career = Career.objects.create(
                    name="General Application",
                    department="General",
                    location="TBD",
                    job_type="full_time",
                    experience_level="entry",
                    description="General application for available positions",
                    requirements="As per position requirements",
                    responsibilities="As per position requirements"
                )
            
            # Get client IP
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip_address = x_forwarded_for.split(',')[0]
            else:
                ip_address = request.META.get('REMOTE_ADDR')
            
            # Combine subject and cover_letter if subject exists
            final_cover_letter = cover_letter
            if subject:
                final_cover_letter = f"Subject: {subject}\n\n{cover_letter}" if cover_letter else f"Subject: {subject}"
            
            # Create application
            application = CareerApplication(
                career=career,
                full_name=full_name,
                email=email,
                phone_number=phone_number or None,
                cover_letter=final_cover_letter or None,
                current_position=current_position or None,
                linkedin_profile=linkedin_profile or None,
                portfolio_website=portfolio_website or None,
                salary_expectation=salary_expectation or None,
                ip_address=ip_address
            )
            
            # Handle years of experience
            if years_of_experience:
                try:
                    application.years_of_experience = int(years_of_experience)
                except ValueError:
                    application.years_of_experience = None
            
            # Handle available start date
            if available_start_date:
                try:
                    from datetime import datetime
                    application.available_start_date = datetime.strptime(available_start_date, '%Y-%m-%d').date()
                except ValueError:
                    application.available_start_date = None
            
            # Handle CV file upload
            if 'cv_file' in request.FILES:
                cv_file = request.FILES['cv_file']
                # Validate file size (2MB limit)
                if cv_file.size > 2 * 1024 * 1024:
                    messages.error(request, 'CV file size must be less than 2MB.')
                    return redirect('GB_FARM:external_career')
                application.cv_file = cv_file
            
            application.save()
            messages.success(request, 'Your application has been submitted successfully! We will review it and get back to you soon.')
            return redirect('GB_FARM:external_career')
            
        except Exception as e:
            logger.error(f"Error in external_career_view POST: {str(e)}")
            messages.error(request, 'An error occurred while submitting your application. Please try again.')
            return redirect('GB_FARM:external_career')
    
    # Handle GET request
    careers = Career.objects.filter(is_active=True).order_by('-created_at')
    context = get_common_context(request)
    context['careers'] = careers
    return render(request, 'Extrnal.html/Career.html', context)

def external_contact_view(request):
    """External Contact Us page for unauthenticated users."""
    # Get active branches for map display
    branches = Branch.objects.filter(is_active=True).order_by('name')

    context = get_common_context(request)
    context['branches'] = branches
    return render(request, 'Extrnal.html/Contact_us.html', context)

def external_index_view(request):
    """External index/home page for unauthenticated users."""
    products = Product.objects.all().order_by('-created_at')[:8]
    featured_products = Product.objects.filter(is_featured=True)[:6]
    carousel_images = CarouselImage.objects.filter(is_active=True).order_by('order')
    
    context = {
        'products': products,
        'featured_products': featured_products,
        'carousel_images': carousel_images,
        **get_common_context(request)
    }
    return render(request, 'Extrnal.html/index.html', context)

def external_product_mago_view(request):
    """External Mangoes Product page for unauthenticated users."""
    varieties = MangoVariety.objects.filter(is_active=True).order_by('order', 'name')
    international_varieties = [v for v in varieties if v.variety_type == 'international']
    local_varieties = [v for v in varieties if v.variety_type == 'local']
    
    context = get_common_context(request)
    context.update({
        'international_varieties': international_varieties,
        'local_varieties': local_varieties
    })
    return render(request, 'Extrnal.html/product_mago.html', context)

def external_product_graps_view(request):
    """External Grapes Product page for unauthenticated users."""
    from .models import GrapeVariety
    varieties = GrapeVariety.objects.filter(is_active=True).order_by('order', 'name')
    grouped_varieties = {
        'white': [v for v in varieties if v.color == 'white'],
        'red': [v for v in varieties if v.color == 'red'],
        'black': [v for v in varieties if v.color == 'black'],
    }
    context = get_common_context(request)
    context['grape_varieties'] = grouped_varieties
    return render(request, 'Extrnal.html/product_graps.html', context)

def external_product_others_view(request):
    """External Other Products page for unauthenticated users."""
    other_products = OtherProduct.objects.filter(is_active=True).order_by('order')
    context = get_common_context(request)
    context['other_products'] = other_products
    return render(request, 'Extrnal.html/product_others.html', context)

def external_calendar_view(request):
    """External Product Availability Calendar page for unauthenticated users."""
    from .models import ProdCat
    import datetime

    current_year = int(request.GET.get('year', datetime.date.today().year))
    
    # Get all products from ProdCat model
    calendar_products = ProdCat.objects.all().order_by('month', 'date_from', 'name')
    
    months_data = []
    month_names = [
        'January', 'February', 'March', 'April', 'May', 'June', 
        'July', 'August', 'September', 'October', 'November', 'December'
    ]
    
    seasons_map = {
        'winter': [12, 1, 2],
        'spring': [3, 4, 5],
        'summer': [6, 7, 8],
        'autumn': [9, 10, 11]
    }
    
    def get_season_name(month_index):
        for season, months in seasons_map.items():
            if month_index in months:
                return season
        return ''

    for i in range(1, 13):
        month_activities = []
        # Get products for this month
        month_products = calendar_products.filter(month=i)
        for product in month_products:
            month_activities.append({
                'name': product.name,
                'date_from': product.date_from.strftime('%b %d'),
                'date_to': product.date_to.strftime('%b %d'),
                'date_range': f"{product.date_from.strftime('%b %d')} - {product.date_to.strftime('%b %d')}",
                'description': f"Available from {product.date_from.strftime('%B %d')} to {product.date_to.strftime('%B %d')}"
            })

        months_data.append({
            'month_index': i,
            'name': month_names[i-1],
            'season_name': get_season_name(i),
            'activities': month_activities,
            'is_current': i == datetime.date.today().month and current_year == datetime.date.today().year
        })

    context = {
        'current_year': current_year,
        'months_data': months_data,
        **get_common_context(request)
    }
        
    return render(request, 'Extrnal.html/product_calendar.html', context)

@require_POST
def submit_external_contact(request):
    """Handle external contact form submissions"""
    try:
        full_name = request.POST.get('full_name')
        email = request.POST.get('email')
        message = request.POST.get('message')
        source_page = request.POST.get('source_page', 'external_contact')
        
        # Additional form fields
        company_name = request.POST.get('company_name')
        phone_number = request.POST.get('tel')  # Note: tel field name from form
        business_type = request.POST.get('business_type')
        find_out_method = request.POST.get('find_out_method')
        subject = request.POST.get('subject')
        
        # Get client IP
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip_address = x_forwarded_for.split(',')[0]
        else:
            ip_address = request.META.get('REMOTE_ADDR')
        
        # Create submission
        submission = ExternalContactSubmission(
            full_name=full_name,
            email=email,
            message=message,
            source_page=source_page,
            ip_address=ip_address,
            company_name=company_name,
            phone_number=phone_number,
            business_type=business_type,
            find_out_method=find_out_method,
            subject=subject
        )
        submission.save()
        
        # Return JSON response for AJAX or redirect for regular form
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': True, 'message': 'Thank you for your message!'})
        else:
            messages.success(request, 'Thank you for your message! We will get back to you soon.')
            return redirect('GB_FARM:' + source_page)
            
    except Exception as e:
        logger.error(f"Error in submit_external_contact: {str(e)}")
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': False, 'message': 'An error occurred. Please try again.'})
        else:
            messages.error(request, 'An error occurred. Please try again.')
            return redirect('GB_FARM:external_contact')

@login_required
@user_passes_test(is_admin)
def admin_submissions_dashboard(request):
    """Admin dashboard for external contact submissions"""
    submissions = ExternalContactSubmission.objects.all().order_by('-created_at')
    
    # Filter by processing status
    status = request.GET.get('status')
    if status == 'processed':
        submissions = submissions.filter(is_processed=True)
    elif status == 'unprocessed':
        submissions = submissions.filter(is_processed=False)
    
    # Search query
    search_query = request.GET.get('q')
    if search_query:
        submissions = submissions.filter(
            Q(full_name__icontains=search_query) | 
            Q(email__icontains=search_query) |
            Q(message__icontains=search_query)
        )
    
    # Paginate results
    paginator = Paginator(submissions, 20)  # 20 submissions per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'submissions': page_obj,
        'total_submissions': submissions.count(),
        'processed_submissions': ExternalContactSubmission.objects.filter(is_processed=True).count(),
        'unprocessed_submissions': ExternalContactSubmission.objects.filter(is_processed=False).count(),
        'selected_status': status,
        'search_query': search_query,
    }
    
    return render(request, 'GB_FARM/internal/admin_submissions_dashboard.html', context)

@login_required
@user_passes_test(is_admin)
def mark_submission_processed(request, submission_id):
    """Mark an external contact submission as processed"""
    submission = get_object_or_404(ExternalContactSubmission, id=submission_id)
    
    if request.method == 'POST':
        try:
            submission.is_processed = True
            submission.save()
            messages.success(request, 'Submission marked as processed successfully')
            return redirect('GB_FARM:admin_submissions_dashboard')
        except Exception as e:
            messages.error(request, f'Error updating submission: {str(e)}')
    
    return render(request, 'GB_FARM/internal/mark_submission_processed.html', {'submission': submission})

@require_POST
@csrf_protect
def subscribe_newsletter(request):
    """Handle newsletter subscription form submissions"""
    try:
        email = request.POST.get('email')
        source_page = request.POST.get('source_page', 'external_index')
        
        if not email:
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'success': False, 'message': 'Email is required'})
            else:
                messages.error(request, 'Email is required')
                return redirect('GB_FARM:' + source_page)
        
        # Check if already subscribed
        existing = NewsletterSubscription.objects.filter(email=email).first()
        if existing:
            if not existing.is_active:
                # Reactivate
                existing.is_active = True
                existing.save()
                message = 'Your subscription has been reactivated!'
            else:
                message = 'You are already subscribed to our newsletter!'
        else:
            # Create new subscription
            subscription = NewsletterSubscription(
                email=email,
                source_page=source_page
            )
            subscription.save()
            message = 'Thank you for subscribing to our newsletter!'
        
        # Return response based on request type
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': True, 'message': message})
        else:
            messages.success(request, message)
            return redirect('GB_FARM:' + source_page)
            
    except Exception as e:
        logger.error(f"Error in subscribe_newsletter: {str(e)}")
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'success': False, 'message': 'An error occurred. Please try again.'})
        else:
            messages.error(request, 'An error occurred. Please try again.')
            return redirect('GB_FARM:external_index')

@login_required
@user_passes_test(is_admin)
def admin_newsletter_dashboard(request):
    """Admin dashboard for newsletter subscriptions"""
    subscriptions = NewsletterSubscription.objects.all().order_by('-created_at')
    
    # Filter by status
    status = request.GET.get('status')
    if status == 'active':
        subscriptions = subscriptions.filter(is_active=True)
    elif status == 'inactive':
        subscriptions = subscriptions.filter(is_active=False)
    
    # Search query
    search_query = request.GET.get('q')
    if search_query:
        subscriptions = subscriptions.filter(email__icontains=search_query)
    
    # Paginate results
    paginator = Paginator(subscriptions, 20)  # 20 subscriptions per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'subscriptions': page_obj,
        'total_subscriptions': subscriptions.count(),
        'active_subscriptions': NewsletterSubscription.objects.filter(is_active=True).count(),
        'inactive_subscriptions': NewsletterSubscription.objects.filter(is_active=False).count(),
        'selected_status': status,
        'search_query': search_query,
    }
    
    return render(request, 'GB_FARM/internal/admin_newsletter_dashboard.html', context)

@login_required
@user_passes_test(is_admin)
def admin_calendar_dashboard(request):
    """Admin dashboard for managing calendar products"""
    
    # Handle POST request for adding new product
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'add':
            name = request.POST.get('name')
            month = request.POST.get('month')
            date_from = request.POST.get('date_from')
            date_to = request.POST.get('date_to')
            
            if name and month and date_from and date_to:
                try:
                    # Validate month range
                    month_int = int(month)
                    if not 1 <= month_int <= 12:
                        raise ValueError('Month must be between 1 and 12')
                    
                    # Parse and validate dates
                    from datetime import datetime
                    try:
                        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                    except ValueError:
                        raise ValueError('Invalid date format. Please use YYYY-MM-DD format')
                    
                    # Check date logic
                    if date_from_obj > date_to_obj:
                        raise ValueError('From Date cannot be later than To Date')
                    
                    # Create the product
                    product = ProdCat(
                        name=name.strip(),
                        month=month_int,
                        date_from=date_from_obj,
                        date_to=date_to_obj
                    )
                    product.full_clean()  # Run model validation
                    product.save()
                    
                    messages.success(request, f'Product "{name}" added successfully!')
                    return redirect('GB_FARM:admin_calendar_dashboard')
                    
                except ValueError as e:
                    messages.error(request, f'Validation error: {str(e)}')
                except Exception as e:
                    logger.error(f"Error adding calendar product: {str(e)}")
                    messages.error(request, f'Error adding product: {str(e)}')
            else:
                messages.error(request, 'All fields are required.')
        
        elif action == 'edit':
            product_id = request.POST.get('product_id')
            name = request.POST.get('name')
            month = request.POST.get('month')
            date_from = request.POST.get('date_from')
            date_to = request.POST.get('date_to')
            
            if product_id and name and month and date_from and date_to:
                try:
                    product = ProdCat.objects.get(id=product_id)
                    
                    # Validate month range
                    month_int = int(month)
                    if not 1 <= month_int <= 12:
                        raise ValueError('Month must be between 1 and 12')
                    
                    # Parse and validate dates
                    from datetime import datetime
                    try:
                        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                    except ValueError:
                        raise ValueError('Invalid date format. Please use YYYY-MM-DD format')
                    
                    # Check date logic
                    if date_from_obj > date_to_obj:
                        raise ValueError('From Date cannot be later than To Date')
                    
                    # Update the product
                    product.name = name.strip()
                    product.month = month_int
                    product.date_from = date_from_obj
                    product.date_to = date_to_obj
                    product.full_clean()  # Run model validation
                    product.save()
                    
                    messages.success(request, f'Product "{name}" updated successfully!')
                    return redirect('GB_FARM:admin_calendar_dashboard')
                    
                except ProdCat.DoesNotExist:
                    messages.error(request, 'Product not found.')
                except ValueError as e:
                    messages.error(request, f'Validation error: {str(e)}')
                except Exception as e:
                    logger.error(f"Error updating calendar product: {str(e)}")
                    messages.error(request, f'Error updating product: {str(e)}')
            else:
                messages.error(request, 'All fields are required.')
        
        elif action == 'delete':
            product_id = request.POST.get('product_id')
            if product_id:
                try:
                    product = ProdCat.objects.get(id=product_id)
                    product_name = product.name
                    product.delete()
                    messages.success(request, f'Product "{product_name}" deleted successfully!')
                    return redirect('GB_FARM:admin_calendar_dashboard')
                except ProdCat.DoesNotExist:
                    messages.error(request, 'Product not found.')
                except Exception as e:
                    logger.error(f"Error deleting calendar product: {str(e)}")
                    messages.error(request, f'Error deleting product: {str(e)}')
    
    calendar_products = ProdCat.objects.all().order_by('month', 'date_from', 'name')
    
    # Filter by month if provided
    month = request.GET.get('month')
    if month:
        calendar_products = calendar_products.filter(month=month)
    
    # Search query
    search_query = request.GET.get('q')
    if search_query:
        calendar_products = calendar_products.filter(name__icontains=search_query)
    
    # Paginate results
    paginator = Paginator(calendar_products, 20)  # 20 products per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get month choices for filter
    month_choices = ProdCat.MONTH_CHOICES
    
    context = {
        'calendar_products': page_obj,
        'total_products': calendar_products.count(),
        'month_choices': month_choices,
        'selected_month': month,
        'search_query': search_query,
    }
    
    return render(request, 'GB_FARM/admin_calendar_dashboard.html', context)

@login_required
@user_passes_test(is_admin)
def admin_grape_varieties_dashboard(request):
    """Admin dashboard for managing grape varieties"""
    from .models import GrapeVariety
    from django.core.paginator import Paginator
    from django.db.models import Q
    
    # Handle POST request for adding/editing/deleting grape varieties
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'add':
            name = request.POST.get('name')
            color = request.POST.get('color')
            date_from_day = request.POST.get('date_from_day')
            date_from_month = request.POST.get('date_from_month')
            date_to_day = request.POST.get('date_to_day')
            date_to_month = request.POST.get('date_to_month')
            weight_from = request.POST.get('weight_from')
            weight_to = request.POST.get('weight_to')
            weight_unit = request.POST.get('weight_unit', 'gm')
            order = request.POST.get('order', 0)
            is_active = request.POST.get('is_active') == 'on'
            
            if name and color and date_from_day and date_from_month and date_to_day and date_to_month and weight_from and weight_to:
                try:
                    variety = GrapeVariety(
                        name=name.strip(),
                        color=color,
                        date_from_day=int(date_from_day),
                        date_from_month=date_from_month,
                        date_to_day=int(date_to_day),
                        date_to_month=date_to_month,
                        weight_from=int(weight_from),
                        weight_to=int(weight_to),
                        weight_unit=weight_unit,
                        order=int(order) if order else 0,
                        is_active=is_active
                    )
                    
                    variety.full_clean()  # Run model validation
                    variety.save()
                    
                    messages.success(request, f'Grape variety "{name}" added successfully!')
                    return redirect('GB_FARM:admin_grape_varieties_dashboard')
                    
                except ValueError as e:
                    messages.error(request, f'Validation error: {str(e)}')
                except Exception as e:
                    logger.error(f"Error adding grape variety: {str(e)}")
                    messages.error(request, f'Error adding grape variety: {str(e)}')
            else:
                messages.error(request, 'All required fields must be filled.')
        
        elif action == 'edit':
            variety_id = request.POST.get('variety_id')
            name = request.POST.get('name')
            color = request.POST.get('color')
            date_from_day = request.POST.get('date_from_day')
            date_from_month = request.POST.get('date_from_month')
            date_to_day = request.POST.get('date_to_day')
            date_to_month = request.POST.get('date_to_month')
            weight_from = request.POST.get('weight_from')
            weight_to = request.POST.get('weight_to')
            weight_unit = request.POST.get('weight_unit', 'gm')
            order = request.POST.get('order', 0)
            is_active = request.POST.get('is_active') == 'on'
            
            if variety_id and name and color:
                try:
                    variety = GrapeVariety.objects.get(id=variety_id)
                    
                    # Update the variety
                    variety.name = name.strip()
                    variety.color = color
                    variety.date_from_day = int(date_from_day)
                    variety.date_from_month = date_from_month
                    variety.date_to_day = int(date_to_day)
                    variety.date_to_month = date_to_month
                    variety.weight_from = int(weight_from)
                    variety.weight_to = int(weight_to)
                    variety.weight_unit = weight_unit
                    variety.order = int(order) if order else 0
                    variety.is_active = is_active
                    
                    variety.full_clean()  # Run model validation
                    variety.save()
                    
                    messages.success(request, f'Grape variety "{name}" updated successfully!')
                    return redirect('GB_FARM:admin_grape_varieties_dashboard')
                    
                except GrapeVariety.DoesNotExist:
                    messages.error(request, 'Grape variety not found.')
                except ValueError as e:
                    messages.error(request, f'Validation error: {str(e)}')
                except Exception as e:
                    logger.error(f"Error updating grape variety: {str(e)}")
                    messages.error(request, f'Error updating grape variety: {str(e)}')
            else:
                messages.error(request, 'All required fields must be filled.')
        
        elif action == 'delete':
            variety_id = request.POST.get('variety_id')
            if variety_id:
                try:
                    variety = GrapeVariety.objects.get(id=variety_id)
                    variety_name = variety.name
                    variety.delete()
                    messages.success(request, f'Grape variety "{variety_name}" deleted successfully!')
                    return redirect('GB_FARM:admin_grape_varieties_dashboard')
                except GrapeVariety.DoesNotExist:
                    messages.error(request, 'Grape variety not found.')
                except Exception as e:
                    logger.error(f"Error deleting grape variety: {str(e)}")
                    messages.error(request, f'Error deleting grape variety: {str(e)}')
    
    grape_varieties = GrapeVariety.objects.all().order_by('color', 'order', 'name')
    
    # Filter by color if provided
    color = request.GET.get('color')
    if color:
        grape_varieties = grape_varieties.filter(color=color)
    
    # Filter by status if provided
    status = request.GET.get('status')
    if status == 'active':
        grape_varieties = grape_varieties.filter(is_active=True)
    elif status == 'inactive':
        grape_varieties = grape_varieties.filter(is_active=False)
    
    # Search query
    search_query = request.GET.get('q')
    if search_query:
        grape_varieties = grape_varieties.filter(name__icontains=search_query)
    
    # Paginate results
    paginator = Paginator(grape_varieties, 20)  # 20 varieties per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get color choices for filter
    color_choices = GrapeVariety.COLOR_CHOICES
    weight_unit_choices = GrapeVariety.WEIGHT_UNIT_CHOICES
    
    context = {
        'grape_varieties': page_obj,
        'total_varieties': grape_varieties.count(),
        'color_choices': color_choices,
        'weight_unit_choices': weight_unit_choices,
        'selected_color': color,
        'selected_status': status,
        'search_query': search_query,
        'active_varieties': GrapeVariety.objects.filter(is_active=True).count(),
        'inactive_varieties': GrapeVariety.objects.filter(is_active=False).count(),
        'white_varieties': GrapeVariety.objects.filter(color='white').count(),
        'red_varieties': GrapeVariety.objects.filter(color='red').count(),
        'black_varieties': GrapeVariety.objects.filter(color='black').count(),
    }
    
    return render(request, 'GB_FARM/admin_grape_varieties_dashboard.html', context)

@login_required
@user_passes_test(is_admin)
def admin_mango_varieties_dashboard(request):
    """Admin dashboard for managing mango varieties"""
    from .models import MangoVariety
    from django.core.paginator import Paginator
    from django.db.models import Q
    
    # Handle POST request for adding/editing/deleting mango varieties
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'add':
            name = request.POST.get('name')
            variety_type = request.POST.get('variety_type')
            date_from_day = request.POST.get('date_from_day')
            date_from_month = request.POST.get('date_from_month')
            date_to_day = request.POST.get('date_to_day')
            date_to_month = request.POST.get('date_to_month')
            weight_from = request.POST.get('weight_from')
            weight_to = request.POST.get('weight_to')
            weight_unit = request.POST.get('weight_unit', 'gm')
            order = request.POST.get('order', 0)
            is_active = request.POST.get('is_active') == 'on'
            
            if name and variety_type and date_from_day and date_from_month and date_to_day and date_to_month and weight_from and weight_to:
                try:
                    variety = MangoVariety(
                        name=name.strip(),
                        variety_type=variety_type,
                        date_from_day=int(date_from_day),
                        date_from_month=date_from_month,
                        date_to_day=int(date_to_day),
                        date_to_month=date_to_month,
                        weight_from=int(weight_from),
                        weight_to=int(weight_to),
                        weight_unit=weight_unit,
                        order=int(order) if order else 0,
                        is_active=is_active
                    )
                    
                    variety.full_clean()  # Run model validation
                    variety.save()
                    
                    messages.success(request, f'Mango variety "{name}" added successfully!')
                    return redirect('GB_FARM:admin_mango_varieties_dashboard')
                    
                except ValueError as e:
                    messages.error(request, f'Validation error: {str(e)}')
                except Exception as e:
                    logger.error(f"Error adding mango variety: {str(e)}")
                    messages.error(request, f'Error adding mango variety: {str(e)}')
            else:
                messages.error(request, 'All required fields must be filled.')
        
        elif action == 'edit':
            variety_id = request.POST.get('variety_id')
            name = request.POST.get('name')
            variety_type = request.POST.get('variety_type')
            date_from_day = request.POST.get('date_from_day')
            date_from_month = request.POST.get('date_from_month')
            date_to_day = request.POST.get('date_to_day')
            date_to_month = request.POST.get('date_to_month')
            weight_from = request.POST.get('weight_from')
            weight_to = request.POST.get('weight_to')
            weight_unit = request.POST.get('weight_unit', 'gm')
            order = request.POST.get('order', 0)
            is_active = request.POST.get('is_active') == 'on'
            
            if variety_id and name and variety_type:
                try:
                    variety = MangoVariety.objects.get(id=variety_id)
                    
                    # Update the variety
                    variety.name = name.strip()
                    variety.variety_type = variety_type
                    variety.date_from_day = int(date_from_day)
                    variety.date_from_month = date_from_month
                    variety.date_to_day = int(date_to_day)
                    variety.date_to_month = date_to_month
                    variety.weight_from = int(weight_from)
                    variety.weight_to = int(weight_to)
                    variety.weight_unit = weight_unit
                    variety.order = int(order) if order else 0
                    variety.is_active = is_active
                    
                    variety.full_clean()  # Run model validation
                    variety.save()
                    
                    messages.success(request, f'Mango variety "{name}" updated successfully!')
                    return redirect('GB_FARM:admin_mango_varieties_dashboard')
                    
                except MangoVariety.DoesNotExist:
                    messages.error(request, 'Mango variety not found.')
                except ValueError as e:
                    messages.error(request, f'Validation error: {str(e)}')
                except Exception as e:
                    logger.error(f"Error updating mango variety: {str(e)}")
                    messages.error(request, f'Error updating mango variety: {str(e)}')
            else:
                messages.error(request, 'All required fields must be filled.')
        
        elif action == 'delete':
            variety_id = request.POST.get('variety_id')
            if variety_id:
                try:
                    variety = MangoVariety.objects.get(id=variety_id)
                    variety_name = variety.name
                    variety.delete()
                    messages.success(request, f'Mango variety "{variety_name}" deleted successfully!')
                    return redirect('GB_FARM:admin_mango_varieties_dashboard')
                except MangoVariety.DoesNotExist:
                    messages.error(request, 'Mango variety not found.')
                except Exception as e:
                    logger.error(f"Error deleting mango variety: {str(e)}")
                    messages.error(request, f'Error deleting mango variety: {str(e)}')
    
    mango_varieties = MangoVariety.objects.all().order_by('variety_type', 'order', 'name')
    
    # Filter by variety type if provided
    variety_type = request.GET.get('variety_type')
    if variety_type:
        mango_varieties = mango_varieties.filter(variety_type=variety_type)
    
    # Filter by status if provided
    status = request.GET.get('status')
    if status == 'active':
        mango_varieties = mango_varieties.filter(is_active=True)
    elif status == 'inactive':
        mango_varieties = mango_varieties.filter(is_active=False)
    
    # Search query
    search_query = request.GET.get('q')
    if search_query:
        mango_varieties = mango_varieties.filter(name__icontains=search_query)
    
    # Paginate results
    paginator = Paginator(mango_varieties, 20)  # 20 varieties per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get variety type choices for filter
    variety_type_choices = MangoVariety.VARIETY_TYPE_CHOICES
    weight_unit_choices = MangoVariety.WEIGHT_UNIT_CHOICES
    
    context = {
        'mango_varieties': page_obj,
        'total_varieties': mango_varieties.count(),
        'variety_type_choices': variety_type_choices,
        'weight_unit_choices': weight_unit_choices,
        'selected_variety_type': variety_type,
        'selected_status': status,
        'search_query': search_query,
        'active_varieties': MangoVariety.objects.filter(is_active=True).count(),
        'inactive_varieties': MangoVariety.objects.filter(is_active=False).count(),
        'international_varieties': MangoVariety.objects.filter(variety_type='international').count(),
        'local_varieties': MangoVariety.objects.filter(variety_type='local').count(),
    }
    
    return render(request, 'GB_FARM/admin_mango_varieties_dashboard.html', context)

@login_required
@user_passes_test(is_admin)
def toggle_subscription_status(request, subscription_id):
    """Toggle newsletter subscription active status"""
    subscription = get_object_or_404(NewsletterSubscription, id=subscription_id)
    
    if request.method == 'POST':
        try:
            subscription.is_active = not subscription.is_active
            subscription.save()
            
            status_text = "activated" if subscription.is_active else "deactivated"
            messages.success(request, f'Subscription {status_text} successfully!')
            
            # Return JSON response for AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'message': f'Subscription {status_text} successfully!',
                    'is_active': subscription.is_active
                })
                
        except Exception as e:
            logger.error(f"Error toggling subscription status: {str(e)}")
            messages.error(request, 'Error updating subscription status')
            
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': False,
                    'message': 'Error updating subscription status'
                })
    
    return redirect('GB_FARM:admin_newsletter_dashboard')

@login_required
def generate_invoice_pdf(request, order_id):
    """Generate a PDF invoice for an order"""
    # Check if user is staff (admin) or the order owner
    order = get_object_or_404(Order, id=order_id)

    if not request.user.is_staff and order.user != request.user:
        logger.warning(f"Unauthorized invoice access attempt - Order #{order_id} by User: {request.user.username} (ID: {request.user.id})")
        messages.error(request, 'You do not have permission to access this invoice')
        return redirect('GB_FARM:home')

    # Additional check if we want only admin users to access invoice regardless of ownership
    if not request.user.user_type == 'admin' and not request.user.is_staff:
        logger.warning(f"Non-admin invoice access attempt - Order #{order_id} by User: {request.user.username} (ID: {request.user.id})")
        messages.error(request, 'Only administrators can generate invoices')
        return redirect('GB_FARM:home')

    try:
        # Log the invoice generation attempt
        user_type = "Admin" if request.user.is_staff else "Customer"
        logger.info(f"Invoice generation started - Order #{order_id} | User: {request.user.username} ({user_type}) | IP: {request.META.get('REMOTE_ADDR', 'unknown')}")

        # Import necessary libraries for PDF generation
        from io import BytesIO
        from django.http import HttpResponse
        from django.template.loader import get_template
        from xhtml2pdf import pisa
        import time

        start_time = time.time()

        # Get order information
        order_items = OrderItem.objects.filter(order=order).select_related('product')

        # Calculate subtotal
        subtotal = sum(item.quantity * item.price for item in order_items)

        # Get shipping cost (assuming it's stored in the order or calculate it)
        shipping_cost = order.shipping_cost if hasattr(order, 'shipping_cost') else Decimal('0.00')

        # Calculate total
        total = subtotal + shipping_cost

        # Get payment information
        payment = Payment.objects.filter(order=order).first()

        # Generate invoice number
        invoice_number = f"INV-{order.id}-{timezone.now().strftime('%Y%m%d')}"

        # Record invoice generation in database for audit trail
        try:
            from django.db import models

            # Create invoice record in the database if not already exists
            invoice_record, created = InvoiceLog.objects.get_or_create(
                order=order,
                generated_by=request.user,
                defaults={
                    'invoice_number': invoice_number,
                    'total_amount': total,
                    'ip_address': request.META.get('REMOTE_ADDR', 'unknown'),
                    'user_agent': request.META.get('HTTP_USER_AGENT', 'unknown'),
                }
            )

            if not created:
                # Update existing record's access count and timestamp
                invoice_record.access_count += 1
                invoice_record.last_accessed = timezone.now()
                invoice_record.save()

            logger.info(f"Invoice record {'created' if created else 'updated'} in database - Invoice #{invoice_number}")
        except Exception as db_error:
            # Don't fail the PDF generation if database logging fails
            logger.error(f"Failed to log invoice generation in database: {str(db_error)}")

        # Prepare context for the template
        context = {
            'order': order,
            'order_items': order_items,
            'subtotal': subtotal,
            'shipping_cost': shipping_cost,
            'total': total,
            'payment': payment,
            'invoice_date': timezone.now(),
            'invoice_number': invoice_number
        }

        # Render the template
        template = get_template('GB_FARM/invoice_pdf.html')
        html = template.render(context)

        # Create PDF
        result = BytesIO()
        pdf = pisa.pisaDocument(BytesIO(html.encode("UTF-8")), result)

        process_time = time.time() - start_time

        if not pdf.err:
            # Generate response with PDF
            response = HttpResponse(result.getvalue(), content_type='application/pdf')
            filename = f"GB_Farm_Invoice_{order.id}.pdf"
            response['Content-Disposition'] = f'inline; filename="{filename}"'

            # Log successful generation
            logger.info(f"Invoice generated successfully - Order #{order_id} | Invoice #{invoice_number} | Processing time: {process_time:.2f}s")
            return response
        else:
            # Log PDF generation error
            logger.error(f"PDF generation error - Order #{order_id}: {pdf.err}")
            return HttpResponse("Error generating the PDF", status=500)

    except Exception as e:
        logger.error(f"Error generating invoice PDF - Order #{order_id}: {str(e)}", exc_info=True)
        messages.error(request, 'Error generating invoice PDF')
        if request.user.is_staff:
            return redirect('GB_FARM:admin_order_detail', order_id=order.id)
        else:
            return redirect('GB_FARM:order_details', pk=order.id)

@login_required
def admin_invoice_logs(request):
    """Admin view for invoice logs"""
    # Check if user is admin
    if not request.user.user_type == 'admin':
        messages.error(request, 'You do not have permission to access this page')
        return redirect('GB_FARM:home')

    # Get all invoice logs with related order and user information
    invoice_logs = InvoiceLog.objects.select_related('order', 'generated_by').order_by('-created_at')

    # Add pagination
    paginator = Paginator(invoice_logs, 25)  # Show 25 logs per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'invoice_logs': page_obj,
        'total_logs': invoice_logs.count(),
    }

    return render(request, 'GB_FARM/admin_invoice_logs.html', context)

@login_required
@user_passes_test(is_admin)
def admin_customer_dashboard(request):
    """Admin dashboard for managing customers"""
    customers = User.objects.filter(user_type='customer').order_by('-date_joined')

    # Add search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        customers = customers.filter(
            Q(username__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query)
        )

    # Add pagination
    paginator = Paginator(customers, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'customers': page_obj,
        'search_query': search_query,
        'total_customers': customers.count(),
    }

    return render(request, 'GB_FARM/admin_customer_dashboard.html', context)

@login_required
@user_passes_test(is_admin)
def admin_customer_detail(request, pk):
    """Admin view for customer details"""
    customer = get_object_or_404(User, pk=pk, user_type='customer')

    # Get customer's orders
    orders = Order.objects.filter(user=customer).order_by('-created_at')

    # Get customer's reviews
    reviews = Review.objects.filter(user=customer).order_by('-created_at')

    # Calculate customer statistics
    total_orders = orders.count()
    total_spent = orders.aggregate(total=Sum('total_amount'))['total'] or 0

    context = {
        'customer': customer,
        'orders': orders[:10],  # Show last 10 orders
        'reviews': reviews[:5],  # Show last 5 reviews
        'total_orders': total_orders,
        'total_spent': total_spent,
    }

    return render(request, 'GB_FARM/admin_customer_detail.html', context)

@login_required
@user_passes_test(is_admin)
def admin_analytics_dashboard(request):
    """Admin analytics dashboard"""
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Get orders in date range
    orders = Order.objects.filter(created_at__date__range=[start_date, end_date])

    # Calculate analytics
    total_orders = orders.count()
    total_revenue = orders.aggregate(total=Sum('total_amount'))['total'] or 0
    avg_order_value = total_revenue / total_orders if total_orders > 0 else 0

    # Get top products
    top_products = OrderItem.objects.filter(
        order__created_at__date__range=[start_date, end_date]
    ).values('product__name').annotate(
        total_sold=Sum('quantity'),
        total_revenue=Sum(F('quantity') * F('price'))
    ).order_by('-total_sold')[:10]

    # Get daily sales data
    daily_sales = orders.extra(
        select={'day': 'date(created_at)'}
    ).values('day').annotate(
        total_orders=Count('id'),
        total_revenue=Sum('total_amount')
    ).order_by('day')

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'total_orders': total_orders,
        'total_revenue': total_revenue,
        'avg_order_value': avg_order_value,
        'top_products': top_products,
        'daily_sales': daily_sales,
    }

    return render(request, 'GB_FARM/admin_analytics_dashboard.html', context)

@login_required
def profile_view(request):
    """User profile view"""
    try:
        profile = UserProfile.objects.get(user=request.user)
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(user=request.user)

    context = {
        'profile': profile,
        **get_common_context(request)
    }

    return render(request, 'GB_FARM/profile.html', context)

@login_required
def edit_profile(request):
    """Edit user profile"""
    try:
        profile = UserProfile.objects.get(user=request.user)
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(user=request.user)

    if request.method == 'POST':
        user_form = UserUpdateForm(request.POST, instance=request.user)
        profile_form = UserProfileForm(request.POST, request.FILES, instance=profile)

        if user_form.is_valid() and profile_form.is_valid():
            user_form.save()
            profile_form.save()
            messages.success(request, 'Your profile has been updated successfully!')
            return redirect('GB_FARM:profile')
    else:
        user_form = UserUpdateForm(instance=request.user)
        profile_form = UserProfileForm(instance=profile)

    context = {
        'user_form': user_form,
        'profile_form': profile_form,
        **get_common_context(request)
    }

    return render(request, 'GB_FARM/edit_profile.html', context)

@login_required
def change_password(request):
    """Change user password"""
    if request.method == 'POST':
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            update_session_auth_hash(request, user)  # Important!
            messages.success(request, 'Your password was successfully updated!')
            return redirect('GB_FARM:profile')
        else:
            messages.error(request, 'Please correct the error below.')
    else:
        form = PasswordChangeForm(request.user)

    context = {
        'form': form,
        **get_common_context(request)
    }

    return render(request, 'GB_FARM/change_password.html', context)

def career_list(request):
    """List all available careers"""
    careers = Career.objects.filter(is_active=True).order_by('-created_at')

    context = {
        'careers': careers,
        **get_common_context(request)
    }

    return render(request, 'GB_FARM/career_list.html', context)

def career_detail(request, id):
    """Career detail view"""
    career = get_object_or_404(Career, id=id, is_active=True)

    context = {
        'career': career,
        **get_common_context(request)
    }

    return render(request, 'GB_FARM/career_detail.html', context)

@login_required
@user_passes_test(is_admin)
def admin_career_dashboard(request):
    """Admin dashboard for managing careers"""
    careers = Career.objects.all().order_by('-created_at')

    # Add search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        careers = careers.filter(
            Q(title__icontains=search_query) |
            Q(department__icontains=search_query) |
            Q(location__icontains=search_query)
        )

    # Add pagination
    paginator = Paginator(careers, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'careers': page_obj,
        'search_query': search_query,
        'total_careers': careers.count(),
    }

    return render(request, 'GB_FARM/admin_career_dashboard.html', context)

from django.shortcuts import render
from django.db.models import Prefetch
from .models import NewCatGroup, NewProduct, NewVariety

# ✅ عرض جميع المنتجات في مجموعة "Others"
def external_all_products(request):
    """Display all category groups and their products"""
    groups = NewCatGroup.objects.prefetch_related(
        'descriptions',
        Prefetch(
            'products',
            queryset=NewProduct.objects.filter(is_active=True).prefetch_related(
                Prefetch('varieties', queryset=NewVariety.objects.filter(is_active=True))
            )
        )
    ).order_by('order', 'title')

    context = {
        "groups": groups,
    }
    return render(request, "Extrnal.html/EX_all_prouduct.html", context)


# ✅ Custom Error Handling Views
def custom_400(request, exception=None):
    return render(request, 'Extrnal.html/400.html', {}, status=400)

def custom_403(request, exception=None):
    return render(request, 'Extrnal.html/403.html', {}, status=403)

def custom_404(request, exception=None):
    return render(request, 'Extrnal.html/404.html', {}, status=404)

def custom_500(request, exception=None):
    return render(request, 'Extrnal.html/500.html', {}, status=500)

# ✅ Test Error Views (for development testing)
def test_400_error(request):
    """Test view to trigger 400 error"""
    from django.http import HttpResponseBadRequest
    return custom_400(request)

def test_403_error(request):
    """Test view to trigger 403 error"""
    from django.core.exceptions import PermissionDenied
    return custom_403(request)

def test_404_error(request):
    """Test view to trigger 404 error"""
    from django.http import Http404
    return custom_404(request)

def test_500_error(request):
    """Test view to trigger 500 error"""
    return custom_500(request)


# ✅ عرض كل المجموعات والمنتجات الجديدة
def external_others(request):
    groups = NewCatGroup.objects.prefetch_related(
        Prefetch(
            'products',
            queryset=NewProduct.objects.prefetch_related(
                Prefetch('varieties', queryset=NewVariety.objects.all())
            )
        )
    ).all()
    
    context = {
        "groups": groups,
    }
    return render(request, "Extrnal.html/EX_all_prouduct.html", context)
