# Generated by Django 4.2.20 on 2025-06-25 16:57

from django.db import migrations, models
import imagekit.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0044_alter_careerapplication_options'),
    ]

    operations = [
        migrations.CreateModel(
            name='Branch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='Branch Name')),
                ('name_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Name')),
                ('branch_type', models.CharField(choices=[('main', 'Main Office'), ('branch', 'Branch Office'), ('warehouse', 'Warehouse'), ('farm', 'Farm Location'), ('distribution', 'Distribution Center')], default='branch', max_length=20)),
                ('address', models.TextField(verbose_name='Full Address')),
                ('address_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Address')),
                ('city', models.CharField(max_length=100)),
                ('city_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Arabic City')),
                ('country', models.CharField(default='Egypt', max_length=100)),
                ('country_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Arabic Country')),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('working_hours', models.CharField(blank=True, help_text='e.g., Mon-Fri: 9:00 AM - 6:00 PM', max_length=200, null=True)),
                ('working_hours_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Working Hours')),
                ('latitude', models.DecimalField(decimal_places=8, help_text='Latitude coordinate (e.g., 30.0444)', max_digits=10)),
                ('longitude', models.DecimalField(decimal_places=8, help_text='Longitude coordinate (e.g., 31.2357)', max_digits=11)),
                ('description', models.TextField(blank=True, help_text='Brief description of the branch', null=True)),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('is_active', models.BooleanField(default=True, help_text='Show this branch on the map')),
                ('is_main_branch', models.BooleanField(default=False, help_text='Mark as main/headquarters branch')),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order on maps')),
                ('image', imagekit.models.fields.ProcessedImageField(blank=True, help_text='Branch photo', null=True, upload_to='branches/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Branch Location',
                'verbose_name_plural': 'Branch Locations',
                'ordering': ['order', 'name'],
                'indexes': [models.Index(fields=['is_active'], name='website_bra_is_acti_65e78b_idx'), models.Index(fields=['branch_type'], name='website_bra_branch__5c39e4_idx'), models.Index(fields=['is_main_branch'], name='website_bra_is_main_601bd4_idx'), models.Index(fields=['latitude', 'longitude'], name='website_bra_latitud_04276d_idx')],
            },
        ),
    ]
