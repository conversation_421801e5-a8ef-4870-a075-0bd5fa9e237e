# Generated by Django 4.2.20 on 2025-07-20 22:19

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import imagekit.models.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.Char<PERSON>ield(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('user_type', models.CharField(choices=[('admin', 'admin'), ('customer', 'customer')], default='customer', max_length=10)),
                ('user_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to.', related_name='website_user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='website_user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='About',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Title')),
                ('story_title', models.CharField(max_length=200)),
                ('story_title_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Story Title')),
                ('story_content', models.TextField()),
                ('story_content_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Story Content')),
                ('operations_title', models.CharField(max_length=200)),
                ('operations_title_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Operations Title')),
                ('operations_content', models.TextField()),
                ('operations_content_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Operations Content')),
                ('values_title', models.CharField(max_length=200)),
                ('values_title_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Values Title')),
                ('values_content', models.TextField()),
                ('values_content_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Values Content')),
                ('phone', models.CharField(max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'About Information',
                'verbose_name_plural': 'About Information',
            },
        ),
        migrations.CreateModel(
            name='AboutUsVideo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='About Us', max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Title')),
                ('description', models.TextField(default='GB Farms was established in 1991 by Dr. Raouf Ghabbour, founder and CEO of GB Auto, a publicly traded company leading the MENA region in automotive manufacturing and distribution, and financial solutions with a workforce of more than 23,000 employees.')),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('video', models.FileField(blank=True, help_text='Upload video file for About Us section', null=True, upload_to='website_videos/')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'About Us Video Section',
                'verbose_name_plural': 'About Us Video Section',
            },
        ),
        migrations.CreateModel(
            name='Career',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Title')),
                ('department', models.CharField(max_length=100)),
                ('department_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Arabic Department')),
                ('location', models.CharField(max_length=100)),
                ('location_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Arabic Location')),
                ('job_type', models.CharField(choices=[('full_time', 'Full Time'), ('part_time', 'Part Time'), ('contract', 'Contract'), ('internship', 'Internship')], max_length=20)),
                ('experience_level', models.CharField(choices=[('entry', 'Entry Level'), ('mid', 'Mid Level'), ('senior', 'Senior Level'), ('lead', 'Lead Level'), ('manager', 'Manager Level')], max_length=20)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('requirements', models.TextField()),
                ('requirements_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Requirements')),
                ('responsibilities', models.TextField()),
                ('responsibilities_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Responsibilities')),
                ('benefits', models.TextField(blank=True, null=True)),
                ('benefits_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Benefits')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Career',
                'verbose_name_plural': 'Careers',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CarouselImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Title')),
                ('description', models.TextField(blank=True, null=True)),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('image', imagekit.models.fields.ProcessedImageField(upload_to='carousel/')),
                ('button_text', models.CharField(blank=True, max_length=50, null=True)),
                ('button_text_ar', models.CharField(blank=True, max_length=50, null=True, verbose_name='Arabic Button Text')),
                ('button_link', models.URLField(blank=True, null=True)),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Carousel Image',
                'verbose_name_plural': 'Carousel Images',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('name_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Arabic Name')),
                ('slug', models.SlugField(unique=True)),
                ('description', models.TextField(blank=True)),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('image', imagekit.models.fields.ProcessedImageField(blank=True, null=True, upload_to='categories/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Categories',
            },
        ),
        migrations.CreateModel(
            name='Configuration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_on', models.DateTimeField(auto_now=True, verbose_name='Updated On')),
                ('leasing_short_description', models.TextField(blank=True, help_text='Short description for Leasing in header', null=True, verbose_name='Leasing Short Description')),
                ('factoring_short_description', models.TextField(blank=True, help_text='Short description for Factoring in header', null=True, verbose_name='Factoring Short Description')),
                ('company_overview_description', models.TextField(blank=True, null=True, verbose_name='Company Overview Description')),
                ('company_overview_image1', models.ImageField(blank=True, null=True, upload_to='company_overview/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'ico', 'svg', 'gif'])], verbose_name='Company Overview IMG1')),
                ('company_overview_image2', models.ImageField(blank=True, null=True, upload_to='company_overview/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'ico', 'svg', 'gif'])], verbose_name='Company Overview IMG2')),
                ('governance_description', models.TextField(blank=True, null=True, verbose_name='Governance Description')),
                ('about_us_short_description', models.TextField(blank=True, help_text='Short description for About Us in header', null=True, verbose_name='About Us Short Description')),
                ('linkedin_url', models.URLField(blank=True, null=True, verbose_name='LinkedIn URL')),
                ('emails', models.TextField(blank=True, help_text='Emails For Requests', null=True, verbose_name='Emails')),
                ('home_SEO_description', models.TextField(blank=True, help_text='Home page SEO Description', null=True, verbose_name='Home SEO Description')),
                ('home_SEO_key_words', models.TextField(blank=True, help_text='Home page SEO Key Words', null=True, verbose_name='Home SEO Key Words')),
                ('about_SEO_description', models.TextField(blank=True, help_text='About page SEO Description', null=True, verbose_name='About SEO Description')),
                ('about_SEO_key_words', models.TextField(blank=True, help_text='About page SEO Key Words', null=True, verbose_name='About SEO Key Words')),
                ('contact_SEO_description', models.TextField(blank=True, help_text='Contact page SEO Description', null=True, verbose_name='Contact SEO Description')),
                ('contact_SEO_key_words', models.TextField(blank=True, help_text='Contact page SEO Key Words', null=True, verbose_name='Contact SEO Key Words')),
                ('news_SEO_description', models.TextField(blank=True, help_text='News page SEO Description', null=True, verbose_name='News SEO Description')),
                ('news_SEO_key_words', models.TextField(blank=True, help_text='News page SEO Key Words', null=True, verbose_name='News SEO Key Words')),
                ('careers_SEO_description', models.TextField(blank=True, help_text='Careers page SEO Description', null=True, verbose_name='Careers SEO Description')),
                ('careers_SEO_key_words', models.TextField(blank=True, help_text='Careers page SEO Key Words', null=True, verbose_name='Careers SEO Key Words')),
                ('leasing_benefits_SEO_description', models.TextField(blank=True, help_text='Leasing benefits page SEO Description', null=True, verbose_name='Leasing Benefits SEO Description')),
                ('leasing_benefits_SEO_key_words', models.TextField(blank=True, help_text='Leasing benefits page SEO Key Words', null=True, verbose_name='Leasing Benefits SEO Key Words')),
                ('factoring_benefits_SEO_description', models.TextField(blank=True, help_text='Factoring benefits page SEO Description', null=True, verbose_name='Factoring Benefits SEO Description')),
                ('factoring_benefits_SEO_key_words', models.TextField(blank=True, help_text='Factoring benefits page SEO Key Words', null=True, verbose_name='Factoring Benefits SEO Key Words')),
            ],
            options={
                'verbose_name': 'Configuration',
                'verbose_name_plural': 'Configuration',
            },
        ),
        migrations.CreateModel(
            name='ContactInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='Contact Us', max_length=100)),
                ('title_ar', models.CharField(blank=True, default='اتصل بنا', max_length=100, null=True, verbose_name='Arabic Title')),
                ('address', models.CharField(max_length=255)),
                ('address_ar', models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Address')),
                ('phone', models.CharField(max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('working_hours', models.CharField(blank=True, max_length=100, null=True)),
                ('working_hours_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Arabic Working Hours')),
                ('latitude', models.DecimalField(blank=True, decimal_places=8, default=30.080062373661846, max_digits=10, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=8, default=31.04347892147481, max_digits=10, null=True)),
                ('map_embed', models.TextField(blank=True, help_text='Google Maps embed code', null=True)),
                ('additional_info', models.TextField(blank=True, null=True)),
                ('additional_info_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Additional Info')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Contact Information',
                'verbose_name_plural': 'Contact Information',
            },
        ),
        migrations.CreateModel(
            name='ContactUsMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('company_name', models.CharField(blank=True, max_length=255, null=True)),
                ('email', models.EmailField(max_length=254)),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('business_type', models.CharField(blank=True, choices=[('supermarket', 'Supermarkets'), ('wholesaler', 'Wholesaler'), ('distributor', 'Distributor')], max_length=50, null=True)),
                ('find_out_source', models.CharField(blank=True, choices=[('internet', 'Internet'), ('exhibitions', 'Exhibitions'), ('search_engine', 'Search Engine')], max_length=50, null=True)),
                ('subject', models.CharField(blank=True, max_length=255, null=True)),
                ('message', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Contact Us Message',
                'verbose_name_plural': 'Contact Us Messages',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CorporateSocial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Title')),
                ('subtitle', models.CharField(blank=True, max_length=200, null=True)),
                ('subtitle_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Subtitle')),
                ('description_part1', models.TextField(blank=True, null=True)),
                ('description_part1_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description Part 1')),
                ('description_part2', models.TextField(blank=True, null=True)),
                ('description_part2_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description Part 2')),
                ('image', imagekit.models.fields.ProcessedImageField(blank=True, null=True, upload_to='corporate_social/')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Corporate Social Responsibility',
                'verbose_name_plural': 'Corporate Social Responsibility',
            },
        ),
        migrations.CreateModel(
            name='CountryFlag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('continent', models.CharField(choices=[('europe', 'Europe'), ('africa', 'Africa'), ('gulf', 'Gulf'), ('asia', 'Asia')], max_length=20)),
                ('country_name', models.CharField(max_length=100)),
                ('flag_image', imagekit.models.fields.ProcessedImageField(blank=True, null=True, upload_to='flags/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Country Flag',
                'verbose_name_plural': 'Country Flags',
                'ordering': ['continent', 'country_name'],
            },
        ),
        migrations.CreateModel(
            name='ExCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
            ],
            options={
                'verbose_name_plural': 'External Categories',
            },
        ),
        migrations.CreateModel(
            name='ExProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=150, verbose_name='Product Title')),
                ('color', models.CharField(default='#000000', max_length=7, verbose_name='Product Color')),
                ('img', models.ImageField(blank=True, null=True, upload_to='products/', verbose_name='Product Image')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'External Product',
                'verbose_name_plural': 'External Products',
                'ordering': ['title'],
            },
        ),
        migrations.CreateModel(
            name='ExternalFooter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.CharField(help_text='e.g. 16 Hussein Wassel St, Maadi, Dokki, Egypt', max_length=255)),
                ('address_ar', models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Address')),
                ('phone', models.CharField(help_text='e.g. +2035392341 / +20109931930', max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('copyright_text', models.CharField(default='ALL RIGHTS RESERVED © GB FARMS 2023', max_length=255)),
                ('facebook_url', models.URLField(blank=True, null=True)),
                ('instagram_url', models.URLField(blank=True, null=True)),
                ('youtube_url', models.URLField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'External Footer',
                'verbose_name_plural': 'External Footer',
            },
        ),
        migrations.CreateModel(
            name='ExVariety',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Variety Name')),
                ('color', models.CharField(default='#000000', max_length=7, verbose_name='Variety Color')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('weight_from', models.PositiveIntegerField(verbose_name='Min Weight')),
                ('weight_to', models.PositiveIntegerField(verbose_name='Max Weight')),
                ('weight_unit', models.CharField(choices=[('gm', 'Grams'), ('kg', 'Kilograms')], default='kg', max_length=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'External Variety',
                'verbose_name_plural': 'External Varieties',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Footer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.CharField(max_length=255)),
                ('address_ar', models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Address')),
                ('phone', models.CharField(max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('facebook_link', models.URLField(blank=True, null=True)),
                ('instagram_link', models.URLField(blank=True, null=True)),
                ('twitter_link', models.URLField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Footer',
                'verbose_name_plural': 'Footer',
            },
        ),
        migrations.CreateModel(
            name='GrapeVariety',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('color', models.CharField(choices=[('white', 'White Seedless'), ('red', 'Red Seedless'), ('black', 'Black Seedless')], max_length=10)),
                ('date_from_day', models.PositiveIntegerField(help_text='Day (1-31)')),
                ('date_from_month', models.CharField(help_text='Month abbreviation (Jan, Feb, etc.)', max_length=3)),
                ('date_to_day', models.PositiveIntegerField(help_text='Day (1-31)')),
                ('date_to_month', models.CharField(help_text='Month abbreviation (Jan, Feb, etc.)', max_length=3)),
                ('weight_from', models.PositiveIntegerField(help_text='Minimum weight')),
                ('weight_to', models.PositiveIntegerField(help_text='Maximum weight')),
                ('weight_unit', models.CharField(choices=[('gm', 'Grams'), ('kg', 'Kilograms')], default='gm', max_length=2)),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Grape Variety',
                'verbose_name_plural': 'Grape Varieties',
                'ordering': ['color', 'order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='MangoVariety',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('variety_type', models.CharField(choices=[('international', 'International Varieties'), ('local', 'Local Varieties')], max_length=15)),
                ('date_from_day', models.PositiveIntegerField(help_text='Day (1-31)')),
                ('date_from_month', models.CharField(help_text='Month abbreviation (Jan, Feb, etc.)', max_length=3)),
                ('date_to_day', models.PositiveIntegerField(help_text='Day (1-31)')),
                ('date_to_month', models.CharField(help_text='Month abbreviation (Jan, Feb, etc.)', max_length=3)),
                ('weight_from', models.PositiveIntegerField(help_text='Minimum weight')),
                ('weight_to', models.PositiveIntegerField(help_text='Maximum weight')),
                ('weight_unit', models.CharField(choices=[('gm', 'Grams'), ('kg', 'Kilograms')], default='gm', max_length=2)),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Mango Variety',
                'verbose_name_plural': 'Mango Varieties',
                'ordering': ['variety_type', 'order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('shipped', 'Shipped'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('shipping_address', models.TextField(blank=True, null=True)),
                ('delivery_method', models.CharField(choices=[('delivery', ' Delivery'), ('collect', 'Collect from Store')], default='delivery', max_length=20)),
                ('customer_name', models.CharField(default='', max_length=100)),
                ('phone_number', models.CharField(default='', max_length=15)),
                ('office_number', models.CharField(blank=True, max_length=10, null=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OtherProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('date_from_day', models.PositiveIntegerField(help_text='Day (1-31)')),
                ('date_from_month', models.CharField(help_text='Month abbreviation (Jan, Feb, etc.)', max_length=3)),
                ('date_to_day', models.PositiveIntegerField(help_text='Day (1-31)')),
                ('date_to_month', models.CharField(help_text='Month abbreviation (Jan, Feb, etc.)', max_length=3)),
                ('weight_from', models.PositiveIntegerField(help_text='Minimum weight')),
                ('weight_to', models.PositiveIntegerField(help_text='Maximum weight')),
                ('weight_unit', models.CharField(choices=[('gm', 'Grams'), ('kg', 'Kilograms')], default='gm', max_length=2)),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Other Product',
                'verbose_name_plural': 'Other Products',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('name_ar', models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Name')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('stock', models.PositiveIntegerField(default=0)),
                ('unit_category', models.CharField(choices=[('FREE WEIGHT', 'Free Weight'), ('PIECE', 'Piece'), ('LITER', 'Liter')], default='PIECE', max_length=20)),
                ('available_units', models.JSONField(blank=True, default=list)),
                ('status', models.CharField(choices=[('available', 'Available'), ('unavailable', 'Unavailable')], default='available', max_length=20)),
                ('is_featured', models.BooleanField(default=False)),
                ('image', imagekit.models.fields.ProcessedImageField(blank=True, null=True, upload_to='products/')),
                ('video', models.FileField(blank=True, null=True, upload_to='products/')),
                ('thumbnail', imagekit.models.fields.ProcessedImageField(blank=True, null=True, upload_to='products/thumbnails/')),
                ('description', models.TextField(blank=True, null=True)),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='products', to='website.category')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductSeason',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Product Name')),
                ('name_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Arabic Product Name')),
                ('start_month', models.PositiveIntegerField(choices=[(1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'), (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'), (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')], verbose_name='From Month')),
                ('end_month', models.PositiveIntegerField(choices=[(1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'), (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'), (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')], verbose_name='To Month')),
                ('description', models.TextField(blank=True, null=True)),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('order', models.PositiveIntegerField(default=0)),
            ],
            options={
                'verbose_name': 'Product Season',
                'verbose_name_plural': 'Product Seasons',
                'ordering': ['order', 'start_month', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ProductType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('name_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Arabic Name')),
                ('slug', models.SlugField(unique=True)),
                ('color', models.CharField(default='#22c55e', help_text='Hex color code for calendar display', max_length=7)),
                ('description', models.TextField(blank=True, null=True)),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('is_active', models.BooleanField(default=True)),
                ('order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Product Type',
                'verbose_name_plural': 'Product Types',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='WebsiteVideo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('title_ar', models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Title')),
                ('video', models.FileField(upload_to='website_videos/')),
                ('is_active', models.BooleanField(default=True)),
                ('opacity', models.FloatField(default=0.3, help_text='Opacity value between 0 and 1')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Zone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('name_ar', models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Name')),
                ('price', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('default_shipping_address', models.TextField(blank=True, null=True)),
                ('profile_picture', imagekit.models.fields.ProcessedImageField(blank=True, null=True, upload_to='profile_pictures/')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Stock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_records', to='website.product')),
            ],
        ),
        migrations.CreateModel(
            name='ProdCat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Product Name')),
                ('month', models.PositiveIntegerField(choices=[(1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'), (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'), (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')], verbose_name='Month')),
                ('date_from', models.DateField(verbose_name='From Date')),
                ('date_to', models.DateField(verbose_name='To Date')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Product Calendar',
                'verbose_name_plural': 'Product Calendars',
                'ordering': ['month', 'date_from', 'name'],
                'indexes': [models.Index(fields=['month'], name='website_pro_month_c75e0c_idx'), models.Index(fields=['date_from', 'date_to'], name='website_pro_date_fr_747995_idx'), models.Index(fields=['name'], name='website_pro_name_1ae0c8_idx'), models.Index(fields=['created_at'], name='website_pro_created_f28731_idx')],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='EGP', editable=False, max_length=3)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=10)),
                ('payment_method', models.CharField(choices=[('credit_card', 'Credit Card'), ('cash', 'Cash'), ('instapay', 'Instapay')], default='cash', max_length=15)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('card_last4', models.CharField(blank=True, max_length=4, null=True)),
                ('card_brand', models.CharField(blank=True, max_length=20, null=True)),
                ('transaction_id', models.CharField(blank=True, max_length=100, null=True)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='website.order')),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='OrderMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField()),
                ('message_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Message')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_read', models.BooleanField(default=False)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='website.order')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_messages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField()),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.order')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.product')),
            ],
        ),
        migrations.AddField(
            model_name='order',
            name='delivery_zone',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='orders', to='website.zone'),
        ),
        migrations.AddField(
            model_name='order',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='Offer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('discount_percentage', models.FloatField(default=0)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('description', models.TextField(blank=True, null=True)),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.product')),
            ],
        ),
        migrations.CreateModel(
            name='NewsletterSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('source_page', models.CharField(choices=[('external_index', 'Home Page'), ('external_about', 'About Page'), ('external_mangoes', 'Mangoes Page'), ('external_grapes', 'Grapes Page'), ('external_others', 'Others Page'), ('external_contact', 'Contact Page')], default='external_index', max_length=30)),
                ('ip_address', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Newsletter Subscription',
                'verbose_name_plural': 'Newsletter Subscriptions',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['created_at'], name='website_new_created_a090bd_idx'), models.Index(fields=['source_page'], name='website_new_source__ef1256_idx'), models.Index(fields=['is_active'], name='website_new_is_acti_cc7e5b_idx')],
            },
        ),
        migrations.CreateModel(
            name='InvoiceLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_accessed', models.DateTimeField(auto_now=True)),
                ('access_count', models.PositiveIntegerField(default=1)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('ip_address', models.CharField(blank=True, max_length=50, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('generated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='generated_invoices', to=settings.AUTH_USER_MODEL)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoice_logs', to='website.order')),
            ],
            options={
                'verbose_name': 'Invoice Log',
                'verbose_name_plural': 'Invoice Logs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ExternalContactSubmission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('message', models.TextField()),
                ('source_page', models.CharField(choices=[('external_index', 'Home Page'), ('external_about', 'About Page'), ('external_mangoes', 'Mangoes Page'), ('external_grapes', 'Grapes Page'), ('external_others', 'Others Page'), ('external_contact', 'Contact Page')], default='external_index', max_length=30)),
                ('ip_address', models.CharField(blank=True, max_length=50, null=True)),
                ('company_name', models.CharField(blank=True, max_length=255, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('business_type', models.CharField(blank=True, choices=[('supermarkets', 'Supermarkets'), ('wholesaler', 'Wholesaler'), ('distributor', 'Distributor')], max_length=50, null=True)),
                ('find_out_method', models.CharField(blank=True, choices=[('internet', 'Internet'), ('exhibitions', 'Exhibitions'), ('search_engine', 'Search Engine')], max_length=50, null=True)),
                ('subject', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_processed', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'External Contact Submission',
                'verbose_name_plural': 'External Contact Submissions',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['created_at'], name='website_ext_created_651911_idx'), models.Index(fields=['source_page'], name='website_ext_source__b98904_idx'), models.Index(fields=['is_processed'], name='website_ext_is_proc_db5640_idx')],
            },
        ),
        migrations.CreateModel(
            name='ExProductVariety',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('exproduct', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.exproduct')),
                ('exvariety', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.exvariety')),
            ],
            options={
                'verbose_name': 'Product Variety Link',
                'verbose_name_plural': 'Product Variety Links',
            },
        ),
        migrations.AddField(
            model_name='exproduct',
            name='varieties',
            field=models.ManyToManyField(related_name='products', through='website.ExProductVariety', to='website.exvariety'),
        ),
        migrations.CreateModel(
            name='ExCategoryProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('excategory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.excategory')),
                ('exproduct', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.exproduct')),
            ],
            options={
                'verbose_name': 'Category Product Link',
                'verbose_name_plural': 'Category Product Links',
            },
        ),
        migrations.AddField(
            model_name='excategory',
            name='products',
            field=models.ManyToManyField(related_name='categories', through='website.ExCategoryProduct', to='website.exproduct'),
        ),
        migrations.CreateModel(
            name='Delivery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('name_ar', models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Name')),
                ('delivery_type', models.CharField(choices=[('Delivery', 'Delivery'), ('pickup', 'Store Pickup')], default='home', max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('zones', models.ManyToManyField(related_name='deliveries', to='website.zone')),
            ],
            options={
                'verbose_name': 'Delivery',
                'verbose_name_plural': 'Deliveries',
            },
        ),
        migrations.CreateModel(
            name='Cart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('selected_weight', models.CharField(blank=True, max_length=10, null=True)),
                ('price_override', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.product')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='CareerApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=200)),
                ('email', models.EmailField(max_length=254)),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('cv_file', models.FileField(blank=True, null=True, upload_to='career_cvs/')),
                ('cover_letter', models.TextField(blank=True, null=True)),
                ('years_of_experience', models.IntegerField(blank=True, null=True)),
                ('current_position', models.CharField(blank=True, max_length=200, null=True)),
                ('linkedin_profile', models.URLField(blank=True, null=True)),
                ('portfolio_website', models.URLField(blank=True, null=True)),
                ('salary_expectation', models.CharField(blank=True, max_length=100, null=True)),
                ('available_start_date', models.DateField(blank=True, null=True)),
                ('is_processed', models.BooleanField(default=False)),
                ('notes', models.TextField(blank=True, help_text='Admin notes', null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('career', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='website.career')),
            ],
            options={
                'verbose_name': 'Career Application',
                'verbose_name_plural': 'Career Applications',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Branch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='Branch Name')),
                ('name_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Branch Name')),
                ('address', models.TextField(verbose_name='Full Address')),
                ('address_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Address')),
                ('latitude', models.DecimalField(decimal_places=8, help_text='Latitude coordinate (e.g., 30.037 for Cairo)', max_digits=10)),
                ('longitude', models.DecimalField(decimal_places=8, help_text='Longitude coordinate (e.g., 31.207 for Cairo)', max_digits=11)),
                ('is_active', models.BooleanField(default=True)),
                ('is_headquarters', models.BooleanField(default=False, help_text='Mark as main headquarters')),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order on map')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Branch',
                'verbose_name_plural': 'Branches',
                'ordering': ['order', 'name'],
                'indexes': [models.Index(fields=['is_active'], name='website_bra_is_acti_65e78b_idx'), models.Index(fields=['is_headquarters'], name='website_bra_is_head_165cac_idx'), models.Index(fields=['order'], name='website_bra_order_aba501_idx')],
            },
        ),
        migrations.CreateModel(
            name='APIToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(max_length=64, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_used_at', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='api_tokens', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Wishlist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.product')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wishlist', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-added_at'],
                'unique_together': {('user', 'product')},
            },
        ),
        migrations.CreateModel(
            name='Review',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField()),
                ('comment', models.TextField()),
                ('comment_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Comment')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='website.product')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['user'], name='website_rev_user_id_a69d5d_idx'), models.Index(fields=['product'], name='website_rev_product_208a7c_idx'), models.Index(fields=['rating'], name='website_rev_rating_3b3faf_idx')],
            },
        ),
        migrations.CreateModel(
            name='ProductAvailability',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.PositiveIntegerField()),
                ('month', models.PositiveIntegerField(choices=[(1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'), (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'), (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')])),
                ('status', models.CharField(choices=[('available', 'Available'), ('limited', 'Limited Availability'), ('none', 'Not Available'), ('peak', 'Peak Season')], max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Additional notes about availability', null=True)),
                ('notes_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availabilities', to='website.producttype')),
            ],
            options={
                'verbose_name': 'Product Availability',
                'verbose_name_plural': 'Product Availabilities',
                'ordering': ['year', 'month', 'product_type'],
                'indexes': [models.Index(fields=['product_type', 'year', 'month'], name='website_pro_product_e2815f_idx'), models.Index(fields=['status'], name='website_pro_status_c9d5f0_idx'), models.Index(fields=['year'], name='website_pro_year_354454_idx')],
                'unique_together': {('product_type', 'year', 'month')},
            },
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['name'], name='website_pro_name_4cb352_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category'], name='website_pro_categor_b9f658_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['status'], name='website_pro_status_3749e8_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['is_featured'], name='website_pro_is_feat_ec077c_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['created_at'], name='website_pro_created_f439c6_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['price'], name='website_pro_price_ba0a94_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['stock'], name='website_pro_stock_8b56fc_idx'),
        ),
        migrations.AddIndex(
            model_name='ordermessage',
            index=models.Index(fields=['order'], name='website_ord_order_i_dc781c_idx'),
        ),
        migrations.AddIndex(
            model_name='ordermessage',
            index=models.Index(fields=['sender'], name='website_ord_sender__adddec_idx'),
        ),
        migrations.AddIndex(
            model_name='ordermessage',
            index=models.Index(fields=['created_at'], name='website_ord_created_95584f_idx'),
        ),
        migrations.AddIndex(
            model_name='ordermessage',
            index=models.Index(fields=['is_read'], name='website_ord_is_read_c9c236_idx'),
        ),
        migrations.AddIndex(
            model_name='orderitem',
            index=models.Index(fields=['order'], name='website_ord_order_i_39b6d2_idx'),
        ),
        migrations.AddIndex(
            model_name='orderitem',
            index=models.Index(fields=['product'], name='website_ord_product_64ed22_idx'),
        ),
        migrations.AddIndex(
            model_name='invoicelog',
            index=models.Index(fields=['order'], name='website_inv_order_i_6573a7_idx'),
        ),
        migrations.AddIndex(
            model_name='invoicelog',
            index=models.Index(fields=['invoice_number'], name='website_inv_invoice_653028_idx'),
        ),
        migrations.AddIndex(
            model_name='invoicelog',
            index=models.Index(fields=['generated_by'], name='website_inv_generat_18017f_idx'),
        ),
        migrations.AddIndex(
            model_name='invoicelog',
            index=models.Index(fields=['created_at'], name='website_inv_created_c22e1c_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='exproductvariety',
            unique_together={('exproduct', 'exvariety')},
        ),
        migrations.AlterUniqueTogether(
            name='excategoryproduct',
            unique_together={('excategory', 'exproduct')},
        ),
        migrations.AlterUniqueTogether(
            name='cart',
            unique_together={('user', 'product', 'selected_weight')},
        ),
        migrations.AddIndex(
            model_name='apitoken',
            index=models.Index(fields=['user'], name='website_api_user_id_550b87_idx'),
        ),
        migrations.AddIndex(
            model_name='apitoken',
            index=models.Index(fields=['token'], name='website_api_token_144695_idx'),
        ),
        migrations.AddIndex(
            model_name='apitoken',
            index=models.Index(fields=['is_active'], name='website_api_is_acti_fd5ca6_idx'),
        ),
        migrations.AddIndex(
            model_name='apitoken',
            index=models.Index(fields=['expires_at'], name='website_api_expires_e52436_idx'),
        ),
    ]
