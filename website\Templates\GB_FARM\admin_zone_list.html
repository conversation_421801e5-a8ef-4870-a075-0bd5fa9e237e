{% extends 'GB_FARM/admin_base.html' %}

{% block title %}Manage Delivery Zones{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Admin Dashboard</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:admin_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i> Orders
                    </a>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-box me-2"></i> Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i> Customers
                    </a>
                    <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Delivery Zones</h4>
                    <a href="{% url 'GB_FARM:admin_zone_create' %}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i> Add New Zone
                    </a>
                </div>
                <div class="card-body">
                    {% if messages %}
                    <div class="messages mb-4">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            {{ message }}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Zone Name</th>
                                    <th>Delivery Price</th>
                                    <th>Status</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for zone in zones %}
                                <tr>
                                    <td>{{ zone.name }}</td>
                                    <td>EGP {{ zone.price }}</td>
                                    <td>
                                        <span class="badge {% if zone.is_active %}bg-success{% else %}bg-danger{% endif %}">
                                            {% if zone.is_active %}Active{% else %}Inactive{% endif %}
                                        </span>
                                    </td>
                                    <td>{{ zone.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'GB_FARM:admin_zone_edit' zone_id=zone.id %}" class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ zone.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteModal{{ zone.id }}" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">Delete Zone</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>Are you sure you want to delete the zone "{{ zone.name }}"?</p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <form action="{% url 'GB_FARM:admin_zone_delete' zone_id=zone.id %}" method="post" class="d-inline">
                                                            {% csrf_token %}
                                                            <button type="submit" class="btn btn-danger">Delete</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">No zones found.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 