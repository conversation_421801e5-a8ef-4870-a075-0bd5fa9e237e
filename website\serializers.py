from rest_framework import serializers
from .models import Product, Category, Order, OrderItem, Review, User, Delivery, Zone
from django.contrib.auth import get_user_model

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = get_user_model()
        fields = ('id', 'username', 'email', 'first_name', 'last_name')
        read_only_fields = ('id',)

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = '__all__'

class ProductSerializer(serializers.ModelSerializer):
    category = CategorySerializer(read_only=True)
    category_id = serializers.IntegerField(write_only=True)
    average_rating = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = '__all__'

    def get_average_rating(self, obj):
        reviews = obj.reviews.all()
        if reviews:
            return sum(review.rating for review in reviews) / len(reviews)
        return 0

class OrderItemSerializer(serializers.ModelSerializer):
    product = ProductSerializer(read_only=True)
    product_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = OrderItem
        fields = '__all__'

class ZoneSerializer(serializers.ModelSerializer):
    class Meta:
        model = Zone
        fields = ('id', 'name', 'price', 'is_active')

class DeliverySerializer(serializers.ModelSerializer):
    zones = ZoneSerializer(many=True, read_only=True)
    zone_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )

    class Meta:
        model = Delivery
        fields = ('id', 'name', 'delivery_type', 'zones', 'zone_ids', 'is_active')

    def create(self, validated_data):
        zone_ids = validated_data.pop('zone_ids', [])
        delivery = Delivery.objects.create(**validated_data)
        if zone_ids:
            delivery.zones.set(Zone.objects.filter(id__in=zone_ids))
        return delivery

    def update(self, instance, validated_data):
        zone_ids = validated_data.pop('zone_ids', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        if zone_ids is not None:
            instance.zones.set(Zone.objects.filter(id__in=zone_ids))
        return instance

class OrderSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True, read_only=True)
    user = UserSerializer(read_only=True)
    user_id = serializers.IntegerField(write_only=True)
    delivery_zone = ZoneSerializer(read_only=True)
    delivery_zone_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    delivery_cost = serializers.DecimalField(read_only=True, max_digits=10, decimal_places=2)
    final_total = serializers.DecimalField(read_only=True, max_digits=10, decimal_places=2)

    class Meta:
        model = Order
        fields = ('id', 'user', 'user_id', 'total_amount', 'status', 'created_at', 
                 'updated_at', 'shipping_address', 'delivery_method', 'delivery_zone',
                 'delivery_zone_id', 'customer_name', 'phone_number', 'office_number',
                 'items', 'delivery_cost', 'final_total')

    def create(self, validated_data):
        delivery_zone_id = validated_data.pop('delivery_zone_id', None)
        order = super().create(validated_data)
        if delivery_zone_id:
            order.delivery_zone_id = delivery_zone_id
            order.save()
        return order

    def update(self, instance, validated_data):
        delivery_zone_id = validated_data.pop('delivery_zone_id', None)
        order = super().update(instance, validated_data)
        if delivery_zone_id is not None:
            order.delivery_zone_id = delivery_zone_id
            order.save()
        return order

class ReviewSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    user_id = serializers.IntegerField(write_only=True)
    product = ProductSerializer(read_only=True)
    product_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = Review
        fields = '__all__' 
