# Generated by Django 4.2.20 on 2025-07-21 05:30

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0066_newproduct_created_at_newproduct_is_active_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='newvariety',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='newvariety',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='newvariety',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
    ]
