# Generated by Django 4.2.20 on 2025-06-23 14:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0029_alter_externalproductvariety_unique_together_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CountryFlag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('country_name', models.CharField(max_length=100)),
                ('country_name_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Arabic Country Name')),
                ('flag_image', models.ImageField(help_text='Upload a flag image for the country', upload_to='flags/')),
                ('continent', models.CharField(choices=[('europe', 'Europe'), ('africa', 'Africa'), ('gulf', 'Gulf'), ('asia', 'Asia'), ('americas', 'Americas'), ('oceania', 'Oceania')], default='europe', max_length=20)),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this flag should be displayed.')),
                ('order', models.PositiveIntegerField(default=0, help_text='Order in which flags appear within their continent.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Country Flag',
                'verbose_name_plural': 'Country Flags',
                'ordering': ['continent', 'order', 'country_name'],
                'unique_together': {('country_name', 'continent')},
            },
        ),
    ]
