{% load static %}

<!-- Header -->
<header class="absolute top-0 left-0 w-full z-50 bg-gray-900 bg-opacity-70 backdrop-blur-sm">
    <div class="h-16 md:h-24">
        <nav class="flex justify-between items-center mx-4 md:mx-20 h-full">
            <!-- Logo -->
            <div class="text-white">
                <a href="{% url 'GB_FARM:external_index' %}" class="text-green-400">
                    <img src="{% static 'images/image 300.png' %}" alt="GB Farms Logo" class="h-12 md:h-16 w-auto">
                </a>
            </div>

            <!-- Menu Items -->
            <div class="flex items-center space-x-3 md:space-x-4">
                <span class="text-green-400 text-sm md:text-base">EN</span>
                <button class="text-green-400 focus:outline-none p-1">
                    <svg class="w-5 h-5 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>
                <button id="menu-button" class="text-green-400 focus:outline-none p-1">
                    <svg class="w-5 h-5 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </nav>
    </div>
</header>

<!-- Side Menu -->
<div id="side-menu"
    class="bg-white bg-opacity-100 fixed top-20 md:top-32 right-0 rounded-3xl w-40 md:w-48 transform translate-x-full transition-transform duration-300 ease-in-out z-50  overflow-y-auto h-[80vh]">
    <div class="p-6 md:p-8 h-fit flex flex-col">
        <div class="flex justify-end">
            <button id="close-menu" class="text-green-400 p-1">
                <svg class="w-3 h-3 md:w-4 md:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                    </path>
                </svg>
            </button>
        </div>
        <div class=" text-green-800 text-xl md:text-2xl mt-4 md:mt-6 flex flex-col space-y-4 md:space-y-8 text-center">
            <a href="{% url 'GB_FARM:ex_all_product' %}" class="border-b border-green-400  hover:text-green-400 hover:font-bold text-base md:text-lg hover:text-lg md:hover:text-xl pb-4 md:pb-6">Products</a>
            <a href="{% url 'GB_FARM:external_about' %}" class="border-b border-green-400  hover:text-green-400 hover:font-bold text-base md:text-lg hover:text-lg md:hover:text-xl pb-4 md:pb-6">About Us</a>
            <a href="{% url 'GB_FARM:external_index' %}" class="border-b border-green-400  hover:text-green-400 hover:font-bold text-base md:text-lg hover:text-lg md:hover:text-xl pb-4 md:pb-6">Home</a>
            <a href="{% url 'GB_FARM:external_calendar' %}" class="border-b border-green-400  hover:text-green-400 hover:font-bold text-base md:text-lg hover:text-lg md:hover:text-xl pb-4 md:pb-6">Calendar</a>
            <a href="{% url 'GB_FARM:external_career' %}" class="border-b border-green-400  hover:text-green-400 hover:font-bold text-base md:text-lg hover:text-lg md:hover:text-xl pb-4 md:pb-6">Careers</a>
            <a href="{% url 'GB_FARM:external_contact' %}" class="hover:text-green-400 text-base md:text-lg hover:text-lg md:hover:text-xl pb-4 md:pb-6">Contact Us</a>
        </div>
    </div>
</div>

<!-- Overlay for mobile menu -->
<div id="menu-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden transition-opacity duration-300"></div>

<!-- Navigation JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Menu toggle functionality
        const menuButton = document.getElementById('menu-button');
        const closeButton = document.getElementById('close-menu');
        const sideMenu = document.getElementById('side-menu');
        const menuOverlay = document.getElementById('menu-overlay');

        function openMenu() {
            if (sideMenu && menuOverlay) {
                sideMenu.classList.remove('translate-x-full');
                sideMenu.classList.add('right-4', 'md:right-10');
                menuOverlay.classList.remove('hidden');
            }
        }

        function closeMenu() {
            if (sideMenu && menuOverlay) {
                sideMenu.classList.add('translate-x-full');
                sideMenu.classList.remove('right-4', 'md:right-10');
                menuOverlay.classList.add('hidden');
            }
        }

        if (menuButton) {
            menuButton.addEventListener('click', openMenu);
        }

        if (closeButton) {
            closeButton.addEventListener('click', closeMenu);
        }

        if (menuOverlay) {
            menuOverlay.addEventListener('click', closeMenu);
        }

        // Close menu when pressing Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMenu();
            }
        });

        // Close menu when clicking on navigation links
        const menuLinks = document.querySelectorAll('#side-menu a');
        menuLinks.forEach(link => {
            link.addEventListener('click', closeMenu);
        });
    });
</script>







