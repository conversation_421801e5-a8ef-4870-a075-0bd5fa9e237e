<!DOCTYPE html>
{% load static %}
{% load i18n %}
<html lang="{{ request.LANGUAGE_CODE }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}GB Farms{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="{% url 'GB_FARM:home' %}">
                <img src="{% static 'img/logo.svg' %}" alt="GB Farms" height="30" class="d-inline-block align-top">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarMain">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}" 
                           href="{% url 'GB_FARM:home' %}">
                            <i class="bi bi-house me-1"></i> {% trans "Home" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'product_list' %}active{% endif %}" 
                           href="{% url 'GB_FARM:product_list' %}">
                            <i class="bi bi-shop me-1"></i> {% trans "Products" %}
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-grid-3x3-gap me-1"></i> {% trans "Categories" %}
                        </a>
                        <ul class="dropdown-menu">
                            {% for category in categories %}
                            <li>
                                <a class="dropdown-item" href="{% url 'GB_FARM:category_products' category_id=category.id %}">
                                    {{ category.name }}
                                </a>
                            </li>
                            {% empty %}
                            <li><span class="dropdown-item text-muted">{% trans "No categories available" %}</span></li>
                            {% endfor %}
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'about' %}active{% endif %}" 
                           href="{% url 'GB_FARM:about' %}">
                            <i class="bi bi-info-circle me-1"></i> {% trans "About Us" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'contact' %}active{% endif %}" 
                           href="{% url 'GB_FARM:contact' %}">
                            <i class="bi bi-envelope me-1"></i> {% trans "Contact" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'career' in request.resolver_match.url_name %}active{% endif %}" 
                           href="{% url 'GB_FARM:career_list' %}">
                            <i class="bi bi-briefcase me-1"></i> {% trans "Careers" %}
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav ms-auto">
                    <!-- Cart -->
                    <li class="nav-item">
                        <a class="nav-link position-relative {% if request.resolver_match.url_name == 'cart' %}active{% endif %}" 
                           href="{% url 'GB_FARM:cart' %}">
                            <i class="bi bi-cart me-1"></i> {% trans "Cart" %}
                            {% if cart_count > 0 %}
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                {{ cart_count }}
                            </span>
                            {% endif %}
                        </a>
                    </li>
                    
                    <!-- Wishlist -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'wishlist' %}active{% endif %}" 
                           href="{% url 'GB_FARM:wishlist' %}">
                            <i class="bi bi-heart me-1"></i> {% trans "Wishlist" %}
                        </a>
                    </li>
                    
                    <!-- Language Selector -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-globe me-1"></i> {{ current_language|upper }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            {% for lang_code, lang_name in available_languages %}
                            <li>
                                <form action="{% url 'set_language' %}" method="post" class="d-inline">
                                    {% csrf_token %}
                                    <input type="hidden" name="language" value="{{ lang_code }}">
                                    <input type="hidden" name="next" value="{{ request.path }}">
                                    <button type="submit" class="dropdown-item {% if current_language == lang_code %}active{% endif %}">
                                        {{ lang_name }}
                                    </button>
                                </form>
                            </li>
                            {% endfor %}
                        </ul>
                    </li>
                    
                    <!-- User Account -->
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i> {{ user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{% url 'GB_FARM:profile' %}">{% trans "My Profile" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'GB_FARM:order_history' %}">{% trans "Order History" %}</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'GB_FARM:logoutuser' %}">{% trans "Logout" %}</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'GB_FARM:loginpage' %}">
                            <i class="bi bi-box-arrow-in-right me-1"></i> {% trans "Login" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'GB_FARM:registerpage' %}">
                            <i class="bi bi-person-plus me-1"></i> {% trans "Register" %}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="py-4">
        <div class="container">
            <!-- Alerts -->
            {% if messages %}
            <div class="messages mb-4">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            <!-- Page Content -->
            {% block content %}{% endblock %}
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-4">
                    <h5 class="mb-3">{% trans "GB Farms" %}</h5>
                    {% if footer %}
                    <p>{{ footer.address }}</p>
                    <p><i class="bi bi-telephone-fill me-2"></i> {{ footer.phone }}</p>
                    <p><i class="bi bi-envelope-fill me-2"></i> {{ footer.email }}</p>
                    {% endif %}
                </div>
                <div class="col-md-4">
                    <h5 class="mb-3">{% trans "Quick Links" %}</h5>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'GB_FARM:home' %}" class="text-decoration-none text-light">{% trans "Home" %}</a></li>
                        <li><a href="{% url 'GB_FARM:product_list' %}" class="text-decoration-none text-light">{% trans "Products" %}</a></li>
                        <li><a href="{% url 'GB_FARM:about' %}" class="text-decoration-none text-light">{% trans "About Us" %}</a></li>
                        <li><a href="{% url 'GB_FARM:contact' %}" class="text-decoration-none text-light">{% trans "Contact" %}</a></li>
                        <li><a href="{% url 'GB_FARM:career_list' %}" class="text-decoration-none text-light">{% trans "Careers" %}</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5 class="mb-3">{% trans "Follow Us" %}</h5>
                    <div class="social-icons">
                        {% if footer.facebook_link %}
                        <a href="{{ footer.facebook_link }}" class="text-light me-3" target="_blank"><i class="bi bi-facebook fs-4"></i></a>
                        {% endif %}
                        {% if footer.instagram_link %}
                        <a href="{{ footer.instagram_link }}" class="text-light me-3" target="_blank"><i class="bi bi-instagram fs-4"></i></a>
                        {% endif %}
                        {% if footer.twitter_link %}
                        <a href="{{ footer.twitter_link }}" class="text-light me-3" target="_blank"><i class="bi bi-twitter fs-4"></i></a>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row mt-4 pt-4 border-top">
                <div class="col-md-6">
                    <p class="mb-0">&copy; {% now "Y" %} GB Farms. {% trans "All rights reserved." %}</p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html> 