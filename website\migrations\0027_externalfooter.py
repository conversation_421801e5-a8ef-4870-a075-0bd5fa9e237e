# Generated by Django 4.2.20 on 2025-06-23 13:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0026_alter_prodcat_options_remove_prodcat_description_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExternalFooter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.CharField(blank=True, help_text='e.g., 16 <PERSON>, Maadi, Dokki, Egypt', max_length=255)),
                ('address_ar', models.CharField(blank=True, max_length=255, verbose_name='Address (Arabic)')),
                ('phone_number', models.CharField(blank=True, help_text='e.g., +2035392341 / +20109931930', max_length=50)),
                ('email', models.EmailField(blank=True, help_text='e.g., <EMAIL>', max_length=254)),
                ('newsletter_title', models.CharField(blank=True, default='KEEP UP TO DATE WITH INSPIRING STORIES, LAUNCHES AND EVENTS FROM GB FARMS', max_length=200)),
                ('newsletter_title_ar', models.CharField(blank=True, max_length=200, verbose_name='Newsletter Title (Arabic)')),
                ('newsletter_description', models.TextField(blank=True, default='*By Signing Up For This Mail, You Are Agreeing To News, Offers, And Information From GB Farms.')),
                ('newsletter_description_ar', models.TextField(blank=True, verbose_name='Newsletter Description (Arabic)')),
                ('copyright_text', models.CharField(blank=True, default='ALL RIGHTS RESERVED © GB FARMS 2023', max_length=100)),
                ('instagram_url', models.URLField(blank=True, help_text='Full URL to your Instagram profile', null=True)),
                ('youtube_url', models.URLField(blank=True, help_text='Full URL to your YouTube channel', null=True)),
                ('facebook_url', models.URLField(blank=True, help_text='Full URL to your Facebook page', null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'External Footer',
                'verbose_name_plural': 'External Footer',
            },
        ),
    ]
