# Generated by Django 4.2.20 on 2025-06-09 13:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0021_externalcontactsubmission'),
    ]

    operations = [
        migrations.CreateModel(
            name='NewsletterSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('source_page', models.CharField(choices=[('external_index', 'Home Page'), ('external_about', 'About Page'), ('external_mangoes', 'Mangoes Page'), ('external_grapes', 'Grapes Page'), ('external_others', 'others Page'), ('external_contact', 'Contact Page')], default='external_index', max_length=30)),
                ('ip_address', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateT<PERSON><PERSON><PERSON>(auto_now_add=True)),
                ('is_active', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
            ],
            options={
                'verbose_name': 'Newsletter Subscription',
                'verbose_name_plural': 'Newsletter Subscriptions',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['created_at'], name='website_new_created_a090bd_idx'), models.Index(fields=['source_page'], name='website_new_source__ef1256_idx'), models.Index(fields=['is_active'], name='website_new_is_acti_cc7e5b_idx')],
            },
        ),
    ]
