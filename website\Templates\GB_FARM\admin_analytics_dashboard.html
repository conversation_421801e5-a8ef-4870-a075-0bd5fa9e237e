{% extends 'GB_FARM/base.html' %}
{% load static %}

{% block title %}Admin Analytics Dashboard{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card shadow-sm mb-4">
                <div class="card-header text-black">
                    <h5 class="mb-0">Admin Dashboard</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:admin_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i> Orders
                    </a>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-box me-2"></i> Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i> Customers
                    </a>
                    <a href="{% url 'GB_FARM:admin_calendar_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-calendar-alt me-2"></i> Calendar Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                    <a href="{% url 'GB_FARM:admin_invoice_logs' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-file-invoice me-2"></i> Invoice Logs
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Date Filter -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                        </div>
                        <div class="col-md-4">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter me-1"></i> Apply
                            </button>
                            <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-sync-alt me-1"></i> Reset
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Key Metrics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body text-center">
                            <h6 class="text-muted">Total Orders</h6>
                            <h2 class="mb-0">{{ total_orders }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body text-center">
                            <h6 class="text-muted">Successful Orders</h6>
                            <h2 class="mb-0">{{ successful_orders }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body text-center">
                            <h6 class="text-muted">Total Revenue</h6>
                            <h2 class="mb-0">EGP {{ total_revenue|floatformat:2 }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card shadow-sm h-100">
                        <div class="card-body text-center">
                            <h6 class="text-muted">Avg. Order Value</h6>
                            <h2 class="mb-0">EGP {{ avg_order_value|floatformat:2 }}</h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Daily Orders</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="dailyOrdersChart" height="250"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Daily Revenue</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="dailyRevenueChart" height="250"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Order Status Distribution</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="orderStatusChart" height="250"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">New Customers</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="newCustomersChart" height="250"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Products and Categories Tables -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Top Products by Sales</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Product</th>
                                            <th>Units Sold</th>
                                            <th>Revenue</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for product in top_products %}
                                        <tr>
                                            <td>{{ product.product__name }}</td>
                                            <td>{{ product.units_sold }}</td>
                                            <td>EGP {{ product.revenue|floatformat:2 }}</td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="3" class="text-center py-4">No product data available</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Top Categories by Sales</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Category</th>
                                            <th>Units Sold</th>
                                            <th>Revenue</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for category in top_categories %}
                                        <tr>
                                            <td>{{ category.product__category__name|default:"Uncategorized" }}</td>
                                            <td>{{ category.units_sold }}</td>
                                            <td>EGP {{ category.revenue|floatformat:2 }}</td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="3" class="text-center py-4">No category data available</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Newsletter Subscriptions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Newsletter Subscriptions</h5>
                            <div>
                                <span class="badge bg-secondary rounded-pill">{{ newsletter_subscriptions_count }} Total</span>
                                <span class="badge bg-success rounded-pill ms-2">{{ active_subscriptions }} Active</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card shadow-sm h-100">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Daily Subscriptions</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="dailySubscriptionsChart" height="250"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card shadow-sm h-100">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Subscriptions by Source</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="subscriptionSourceChart" height="250"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Email</th>
                                            <th>Source Page</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for subscription in newsletter_subscriptions %}
                                        <tr>
                                            <td>{{ subscription.created_at|date:"M d, Y H:i" }}</td>
                                            <td>{{ subscription.email }}</td>
                                            <td><span class="badge {% if subscription.source_page == 'external_index' %}bg-primary{% elif subscription.source_page == 'external_others' %}bg-danger{% elif subscription.source_page == 'external_grapes' %}bg-success{% elif subscription.source_page == 'external_mangoes' %}bg-warning{% else %}bg-info{% endif %}">{{ subscription.get_source_page_display }}</span></td>
                                            <td>
                                                {% if subscription.is_active %}
                                                <span class="badge bg-success">Active</span>
                                                {% else %}
                                                <span class="badge bg-danger">Inactive</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="4" class="text-center py-4">No newsletter subscriptions available</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            
                            {% if newsletter_subscriptions.has_other_pages %}
                            <div class="d-flex justify-content-center mt-4">
                                <nav aria-label="Newsletter subscriptions pagination">
                                    <ul class="pagination">
                                        {% if newsletter_subscriptions.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?newsletter_page=1{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" aria-label="First">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?newsletter_page={{ newsletter_subscriptions.previous_page_number }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                        {% endif %}
                                        
                                        {% for i in newsletter_subscriptions.paginator.page_range %}
                                            {% if newsletter_subscriptions.number == i %}
                                            <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                                            {% elif i > newsletter_subscriptions.number|add:'-3' and i < newsletter_subscriptions.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?newsletter_page={{ i }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">{{ i }}</a>
                                            </li>
                                            {% endif %}
                                        {% endfor %}
                                        
                                        {% if newsletter_subscriptions.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?newsletter_page={{ newsletter_subscriptions.next_page_number }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?newsletter_page={{ newsletter_subscriptions.paginator.num_pages }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" aria-label="Last">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                        {% endif %}
                                    </ul>
                                </nav>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- External Contact Form Submissions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Recent Contact Form Submissions</h5>
                            <div>
                                <span class="badge bg-secondary rounded-pill">{{ contact_submissions_count }} Total</span>
                                <span class="badge bg-warning rounded-pill ms-2">{{ unprocessed_submissions }} Unprocessed</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Source Page</th>
                                            <th>Message</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for submission in contact_submissions %}
                                        <tr>
                                            <td>{{ submission.created_at|date:"M d, Y H:i" }}</td>
                                            <td>{{ submission.full_name }}</td>
                                            <td>{{ submission.email }}</td>
                                            <td><span class="badge {% if submission.source_page == 'external_index' %}bg-primary{% elif submission.source_page == 'external_others' %}bg-danger{% elif submission.source_page == 'external_grapes' %}bg-success{% elif submission.source_page == 'external_mangoes' %}bg-warning{% else %}bg-info{% endif %}">{{ submission.get_source_page_display }}</span></td>
                                            <td>{{ submission.message|truncatechars:30 }}</td>
                                            <td>
                                                {% if submission.is_processed %}
                                                <span class="badge bg-success">Processed</span>
                                                {% else %}
                                                <span class="badge bg-warning">Pending</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="#" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#viewSubmission{{ submission.id }}">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'GB_FARM:mark_submission_processed' submission.id %}" class="btn btn-sm btn-outline-success {% if submission.is_processed %}disabled{% endif %}">
                                                        <i class="fas fa-check"></i>
                                                    </a>
                                                </div>
                                                
                                                <!-- View Submission Modal -->
                                                <div class="modal fade" id="viewSubmission{{ submission.id }}" tabindex="-1" aria-labelledby="viewSubmissionLabel{{ submission.id }}" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="viewSubmissionLabel{{ submission.id }}">Contact Form Submission</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <div class="mb-3">
                                                                    <label class="form-label fw-bold">Submitted On:</label>
                                                                    <p>{{ submission.created_at|date:"F d, Y H:i:s" }}</p>
                                                                </div>
                                                                <div class="mb-3">
                                                                    <label class="form-label fw-bold">Name:</label>
                                                                    <p>{{ submission.full_name }}</p>
                                                                </div>
                                                                <div class="mb-3">
                                                                    <label class="form-label fw-bold">Email:</label>
                                                                    <p><a href="mailto:{{ submission.email }}">{{ submission.email }}</a></p>
                                                                </div>
                                                                <div class="mb-3">
                                                                    <label class="form-label fw-bold">Source Page:</label>
                                                                    <p>{{ submission.get_source_page_display }}</p>
                                                                </div>
                                                                <div class="mb-3">
                                                                    <label class="form-label fw-bold">IP Address:</label>
                                                                    <p>{{ submission.ip_address|default:"Not recorded" }}</p>
                                                                </div>
                                                                <div class="mb-3">
                                                                    <label class="form-label fw-bold">Message:</label>
                                                                    <div class="p-3 bg-light rounded">{{ submission.message|linebreaks }}</div>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                                <a href="{% url 'GB_FARM:mark_submission_processed' submission.id %}" class="btn btn-success {% if submission.is_processed %}disabled{% endif %}">
                                                                    Mark as Processed
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="7" class="text-center py-4">No contact form submissions available</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            
                            {% if contact_submissions.has_other_pages %}
                            <div class="d-flex justify-content-center mt-4">
                                <nav aria-label="Contact submissions pagination">
                                    <ul class="pagination">
                                        {% if contact_submissions.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?contact_page=1{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" aria-label="First">
                                                <span aria-hidden="true">&laquo;&laquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?contact_page={{ contact_submissions.previous_page_number }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                        {% endif %}
                                        
                                        {% for i in contact_submissions.paginator.page_range %}
                                            {% if contact_submissions.number == i %}
                                            <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                                            {% elif i > contact_submissions.number|add:'-3' and i < contact_submissions.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?contact_page={{ i }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">{{ i }}</a>
                                            </li>
                                            {% endif %}
                                        {% endfor %}
                                        
                                        {% if contact_submissions.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?contact_page={{ contact_submissions.next_page_number }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?contact_page={{ contact_submissions.paginator.num_pages }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" aria-label="Last">
                                                <span aria-hidden="true">&raquo;&raquo;</span>
                                            </a>
                                        </li>
                                        {% endif %}
                                    </ul>
                                </nav>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Daily Orders Chart
        const dailyOrdersCtx = document.getElementById('dailyOrdersChart').getContext('2d');
        const dailyOrdersChart = new Chart(dailyOrdersCtx, {
            type: 'line',
            data: {
                labels: [{% for order in daily_orders %}'{{ order.date|date:"M d" }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
                datasets: [{
                    label: 'Orders',
                    data: [{% for order in daily_orders %}{{ order.count }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // Daily Revenue Chart
        const dailyRevenueCtx = document.getElementById('dailyRevenueChart').getContext('2d');
        const dailyRevenueChart = new Chart(dailyRevenueCtx, {
            type: 'line',
            data: {
                labels: [{% for item in daily_revenue %}'{{ item.date|date:"M d" }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
                datasets: [{
                    label: 'Revenue (EGP)',
                    data: [{% for item in daily_revenue %}{{ item.total|default:0 }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 2,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Order Status Chart
        const orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
        const orderStatusChart = new Chart(orderStatusCtx, {
            type: 'doughnut',
            data: {
                labels: [{% for status in status_data %}'{{ status.status|title }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
                datasets: [{
                    data: [{% for status in status_data %}{{ status.count }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });

        // New Customers Chart
        const newCustomersCtx = document.getElementById('newCustomersChart').getContext('2d');
        const newCustomersChart = new Chart(newCustomersCtx, {
            type: 'bar',
            data: {
                labels: [{% for item in new_customers %}'{{ item.date|date:"M d" }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
                datasets: [{
                    label: 'New Customers',
                    data: [{% for item in new_customers %}{{ item.count }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                    backgroundColor: 'rgba(153, 102, 255, 0.7)',
                    borderColor: 'rgba(153, 102, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
        
        // Daily Subscriptions Chart
        const dailySubscriptionsCtx = document.getElementById('dailySubscriptionsChart').getContext('2d');
        const dailySubscriptionsChart = new Chart(dailySubscriptionsCtx, {
            type: 'line',
            data: {
                labels: [{% for item in daily_subscriptions %}'{{ item.date|date:"M d" }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
                datasets: [{
                    label: 'Subscriptions',
                    data: [{% for item in daily_subscriptions %}{{ item.count }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                    backgroundColor: 'rgba(255, 159, 64, 0.2)',
                    borderColor: 'rgba(255, 159, 64, 1)',
                    borderWidth: 2,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
        
        // Subscription Source Chart
        const subscriptionSourceCtx = document.getElementById('subscriptionSourceChart').getContext('2d');
        const subscriptionSourceChart = new Chart(subscriptionSourceCtx, {
            type: 'pie',
            data: {
                labels: [{% for source in newsletter_by_source %}'{{ source.source_page|title }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
                datasets: [{
                    data: [{% for source in newsletter_by_source %}{{ source.count }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
    });
</script>
{% endblock %} 