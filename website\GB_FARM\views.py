from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.core.paginator import Paginator
from website.models import Product, Category, Cart, Wishlist, Review, CarouselImage, WebsiteVideo, About, CountryFlag
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.utils import timezone

def get_common_context():
    """Helper function to get common context for all views"""
    # Try to get the active website video, if none is found, try to get any website video
    website_video = WebsiteVideo.objects.filter(is_active=True).first()
    if not website_video:
        website_video = WebsiteVideo.objects.first()
        
    return {
        'carousel_images': CarouselImage.objects.all(),
        'categories': Category.objects.all(),
        'website_video': website_video,
    }

def home(request):
    context = get_common_context()
    context.update({
        'featured_products': Product.objects.filter(is_featured=True, status='available')[:6],
    })
    return render(request, 'GB_FARM/home.html', context)

def product_list(request):
    context = get_common_context()
    products = Product.objects.all()  # Get all products, not just available ones
    category_id = request.GET.get('category')
    search_query = request.GET.get('q')

    if category_id:
        products = products.filter(category_id=category_id)
        context['category'] = get_object_or_404(Category, id=category_id)

    if search_query:
        products = products.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )
        context['search_query'] = search_query

    paginator = Paginator(products, 12)
    page = request.GET.get('page')
    products = paginator.get_page(page)

    context.update({
        'products': products,
        'search_query': search_query,
    })
    return render(request, 'GB_FARM/product_list.html', context)

def category_products(request, category_id):
    context = get_common_context()
    category = get_object_or_404(Category, id=category_id)
    products = Product.objects.filter(category=category)  # Remove status filter
    
    paginator = Paginator(products, 12)
    page = request.GET.get('page')
    products = paginator.get_page(page)

    context.update({
        'category': category,
        'products': products,
    })
    return render(request, 'GB_FARM/product_list.html', context)

def product_detail(request, product_id):
    context = get_common_context()
    product = get_object_or_404(Product, id=product_id)
    reviews = Review.objects.filter(product=product).order_by('-created_at')
    user_review = None
    
    if request.user.is_authenticated:
        user_review = Review.objects.filter(product=product, user=request.user).first()
    
    context.update({
        'product': product,
        'reviews': reviews,
        'user_review': user_review
    })
    return render(request, 'GB_FARM/product_detail.html', context)

def product_search(request):
    context = get_common_context()
    products = Product.objects.all()
    search_query = request.GET.get('q')
    category_id = request.GET.get('category')
    min_price = request.GET.get('min_price')
    max_price = request.GET.get('max_price')
    status = request.GET.get('status', 'available')
    sort_by = request.GET.get('sort', '')

    if search_query:
        products = products.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )
        context['search_query'] = search_query

    if category_id:
        products = products.filter(category_id=category_id)
        context['category'] = get_object_or_404(Category, id=category_id)

    if min_price:
        products = products.filter(price__gte=float(min_price))
        context['min_price'] = min_price

    if max_price:
        products = products.filter(price__lte=float(max_price))
        context['max_price'] = max_price

    if status:
        products = products.filter(status=status)
        context['status'] = status

    if sort_by:
        if sort_by == 'price_asc':
            products = products.order_by('price')
        elif sort_by == 'price_desc':
            products = products.order_by('-price')
        elif sort_by == 'name_asc':
            products = products.order_by('name')
        elif sort_by == 'name_desc':
            products = products.order_by('-name')
        context['sort_by'] = sort_by

    paginator = Paginator(products, 12)
    page = request.GET.get('page')
    products = paginator.get_page(page)

    context.update({
        'products': products,
    })
    return render(request, 'GB_FARM/product_search.html', context)

@login_required
def add_to_cart(request, product_id):
    if request.method == 'POST':
        product = get_object_or_404(Product, id=product_id)
        quantity = int(request.POST.get('quantity', 1))
        
        cart_item, created = Cart.objects.get_or_create(
            user=request.user,
            product=product,
            defaults={'quantity': quantity}
        )
        
        if not created:
            cart_item.quantity += quantity
            cart_item.save()
        
        messages.success(request, f'{product.name} added to cart successfully!')
        return redirect('GB_FARM:product_detail', product_id=product_id)
    
    return redirect('GB_FARM:product_list')

@login_required
def add_to_wishlist(request, product_id):
    if request.method == 'POST':
        product = get_object_or_404(Product, id=product_id)
        Wishlist.objects.get_or_create(user=request.user, product=product)
        messages.success(request, f'{product.name} added to wishlist successfully!')
        return redirect('GB_FARM:product_detail', product_id=product_id)
    
    return redirect('GB_FARM:product_list')

@login_required
def remove_from_wishlist(request, product_id):
    if request.method == 'POST':
        product = get_object_or_404(Product, id=product_id)
        Wishlist.objects.filter(user=request.user, product=product).delete()
        messages.success(request, f'{product.name} removed from wishlist successfully!')
        return redirect('GB_FARM:product_detail', product_id=product_id)
    
    return redirect('GB_FARM:product_list')

@login_required
def add_review(request, product_id):
    if request.method == 'POST':
        product = get_object_or_404(Product, id=product_id)
        rating = int(request.POST.get('rating'))
        comment = request.POST.get('comment')
        
        review, created = Review.objects.get_or_create(
            user=request.user,
            product=product,
            defaults={'rating': rating, 'comment': comment}
        )
        
        if not created:
            review.rating = rating
            review.comment = comment
            review.save()
        
        messages.success(request, 'Review submitted successfully!')
        return redirect('GB_FARM:product_detail', product_id=product_id)
    
    return redirect('GB_FARM:product_list')

@login_required
def delete_review(request, review_id):
    if request.method == 'POST':
        review = get_object_or_404(Review, id=review_id, user=request.user)
        product_id = review.product.id
        review.delete()
        messages.success(request, 'Review deleted successfully!')
        return redirect('GB_FARM:product_detail', product_id=product_id)
    
    return redirect('GB_FARM:product_list')

def about(request):
    context = get_common_context()
    about_content = About.objects.first()

    # Fetch country flags grouped by continent
    country_flags = CountryFlag.objects.all().order_by('continent', 'country_name')
    grouped_flags = {
        'europe': [],
        'africa': [],
        'gulf': [],
        'asia': [],
    }
    for flag in country_flags:
        grouped_flags[flag.continent].append(flag)

    context.update({
        'about_content': about_content,
        'grouped_flags': grouped_flags,
    })
    return render(request, 'Extrnal.html/about.html', context) 





