# Generated by Django 4.2.20 on 2025-07-05 19:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0051_remove_corporatesocial_short_description_part1_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='corporatesocial',
            options={'verbose_name': 'Corporate Social Responsibility', 'verbose_name_plural': 'Corporate Social Responsibilities'},
        ),
        migrations.AddField(
            model_name='corporatesocial',
            name='subtitle',
            field=models.CharField(default='Corporate Social Responsibility', max_length=200, verbose_name='Subtitle'),
        ),
        migrations.AddField(
            model_name='corporatesocial',
            name='subtitle_ar',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Subtitle'),
        ),
        migrations.AlterField(
            model_name='corporatesocial',
            name='description_part1',
            field=models.TextField(verbose_name='Description Part 1'),
        ),
        migrations.AlterField(
            model_name='corporatesocial',
            name='description_part2',
            field=models.TextField(verbose_name='Description Part 2'),
        ),
        migrations.AlterField(
            model_name='corporatesocial',
            name='image',
            field=models.ImageField(blank=True, help_text='Image for the Corporate Social Responsibility section', null=True, upload_to='csr_images/'),
        ),
        migrations.AlterField(
            model_name='corporatesocial',
            name='title',
            field=models.CharField(default='CSR', max_length=200, verbose_name='Title'),
        ),
    ]
