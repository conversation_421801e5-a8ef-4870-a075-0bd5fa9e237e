{% extends "GB_FARM/admin_base.html" %}
{% load static %}

{% block title %}Grape Varieties Management - Admin Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Grape Varieties Management</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addVarietyModal">
            <i class="fas fa-plus"></i> Add New Variety
        </button>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Varieties</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_varieties }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-grape-cluster fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Varieties</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_varieties }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Inactive Varieties</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ inactive_varieties }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-pause-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
        </div>
        <div class="card-body">
            <form method="GET" class="form-inline">
                <div class="form-group mr-3 mb-2">
                    <label for="color" class="sr-only">Color</label>
                    <select name="color" id="color" class="form-control">
                        <option value="">All Colors</option>
                        {% for value, label in color_choices %}
                            <option value="{{ value }}" {% if selected_color == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group mr-3 mb-2">
                    <label for="status" class="sr-only">Status</label>
                    <select name="status" id="status" class="form-control">
                        <option value="">All Status</option>
                        <option value="active" {% if selected_status == 'active' %}selected{% endif %}>Active</option>
                        <option value="inactive" {% if selected_status == 'inactive' %}selected{% endif %}>Inactive</option>
                    </select>
                </div>

                <div class="form-group mr-3 mb-2">
                    <label for="q" class="sr-only">Search</label>
                    <input type="text" name="q" id="q" class="form-control" placeholder="Search varieties..." value="{{ search_query }}">
                </div>

                <button type="submit" class="btn btn-primary mb-2 mr-2">Filter</button>
                <a href="{% url 'GB_FARM:admin_grape_varieties_dashboard' %}" class="btn btn-secondary mb-2">Clear</a>
            </form>
        </div>
    </div>

    <!-- Grape Varieties Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Grape Varieties</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Color</th>
                            <th>Order</th>
                            <th>Status</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for variety in grape_varieties %}
                        <tr>
                            <td>{{ variety.name }}</td>
                            <td>
                                <span class="badge badge-{% if variety.color == 'white' %}light{% elif variety.color == 'red' %}danger{% else %}dark{% endif %}">
                                    {{ variety.get_color_display }}
                                </span>
                            </td>
                            <td>{{ variety.order }}</td>
                            <td>
                                {% if variety.is_active %}
                                    <span class="badge badge-success">Active</span>
                                {% else %}
                                    <span class="badge badge-secondary">Inactive</span>
                                {% endif %}
                            </td>
                            <td>{{ variety.created_at|date:"M d, Y H:i" }}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary" 
                                        onclick="editVariety({{ variety.id }}, '{{ variety.name }}', '{{ variety.color }}', {{ variety.order }}, {{ variety.is_active|yesno:'true,false' }})">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button type="button" class="btn btn-sm btn-danger" 
                                        onclick="deleteVariety({{ variety.id }}, '{{ variety.name }}')">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">No grape varieties found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if grape_varieties.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if grape_varieties.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ grape_varieties.previous_page_number }}{% if selected_color %}&color={{ selected_color }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}

                    {% for num in grape_varieties.paginator.page_range %}
                        {% if grape_varieties.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > grape_varieties.number|add:'-3' and num < grape_varieties.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if selected_color %}&color={{ selected_color }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if grape_varieties.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ grape_varieties.next_page_number }}{% if selected_color %}&color={{ selected_color }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<!-- Add Variety Modal -->
<div class="modal fade" id="addVarietyModal" tabindex="-1" role="dialog" aria-labelledby="addVarietyModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addVarietyModalLabel">Add New Grape Variety</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST">
                {% csrf_token %}
                <input type="hidden" name="action" value="add">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="add_name">Name</label>
                        <input type="text" class="form-control" id="add_name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="add_color">Color</label>
                        <select class="form-control" id="add_color" name="color" required>
                            <option value="">Select Color</option>
                            {% for value, label in color_choices %}
                                <option value="{{ value }}">{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="add_order">Order</label>
                        <input type="number" class="form-control" id="add_order" name="order" value="0" min="0">
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="add_is_active" name="is_active" checked>
                        <label class="form-check-label" for="add_is_active">
                            Active
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Variety</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Variety Modal -->
<div class="modal fade" id="editVarietyModal" tabindex="-1" role="dialog" aria-labelledby="editVarietyModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editVarietyModalLabel">Edit Grape Variety</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST">
                {% csrf_token %}
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="variety_id" id="edit_variety_id">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="edit_name">Name</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_color">Color</label>
                        <select class="form-control" id="edit_color" name="color" required>
                            {% for value, label in color_choices %}
                                <option value="{{ value }}">{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="edit_order">Order</label>
                        <input type="number" class="form-control" id="edit_order" name="order" min="0">
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="edit_is_active" name="is_active">
                        <label class="form-check-label" for="edit_is_active">
                            Active
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Variety</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Variety Modal -->
<div class="modal fade" id="deleteVarietyModal" tabindex="-1" role="dialog" aria-labelledby="deleteVarietyModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteVarietyModalLabel">Delete Grape Variety</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST">
                {% csrf_token %}
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="variety_id" id="delete_variety_id">
                <div class="modal-body">
                    <p>Are you sure you want to delete the grape variety "<span id="delete_variety_name"></span>"?</p>
                    <p class="text-danger"><strong>This action cannot be undone.</strong></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editVariety(id, name, color, order, isActive) {
    document.getElementById('edit_variety_id').value = id;
    document.getElementById('edit_name').value = name;
    document.getElementById('edit_color').value = color;
    document.getElementById('edit_order').value = order;
    document.getElementById('edit_is_active').checked = isActive;
    
    var editModal = new bootstrap.Modal(document.getElementById('editVarietyModal'));
    editModal.show();
}

function deleteVariety(id, name) {
    document.getElementById('delete_variety_id').value = id;
    document.getElementById('delete_variety_name').textContent = name;
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteVarietyModal'));
    deleteModal.show();
}
</script>
{% endblock %} 