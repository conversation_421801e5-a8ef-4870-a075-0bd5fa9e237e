# Generated by Django 4.2.20 on 2025-07-21 01:22

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0063_alter_excategoryproduct_options'),
    ]

    operations = [
        migrations.AddField(
            model_name='excategory',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='excategory',
            name='description1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='excategory',
            name='description2',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='excategory',
            name='description3',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='excategory',
            name='description4',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='excategory',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='exproductvariety',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='exproductvariety',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
    ]
