{% extends "GB_FARM/external/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Careers" %} - GB Farms{% endblock %}

{% block extra_css %}
<style>
    .career-card {
        transition: transform 0.3s, box-shadow 0.3s;
    }
    .career-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .job-badge {
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="bg-primary text-white py-5 mb-5 rounded">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-3">{% trans "Join Our Team" %}</h1>
                <p class="lead mb-4">{% trans "Explore exciting career opportunities at GB Farms and grow with us." %}</p>
            </div>
        </div>
    </div>
</section>

<!-- Filter Section -->
<section class="mb-5">
    <div class="container">
        <div class="card shadow-sm">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-5">
                        <label for="job_type" class="form-label">{% trans "Job Type" %}</label>
                        <select name="job_type" id="job_type" class="form-select">
                            <option value="">{% trans "All Job Types" %}</option>
                            {% for job_type in job_types %}
                                <option value="{{ job_type }}" {% if selected_job_type == job_type %}selected{% endif %}>
                                    {% if job_type == 'full_time' %}{% trans "Full Time" %}
                                    {% elif job_type == 'part_time' %}{% trans "Part Time" %}
                                    {% elif job_type == 'contract' %}{% trans "Contract" %}
                                    {% elif job_type == 'internship' %}{% trans "Internship" %}
                                    {% endif %}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label for="experience_level" class="form-label">{% trans "Experience Level" %}</label>
                        <select name="experience_level" id="experience_level" class="form-select">
                            <option value="">{% trans "All Experience Levels" %}</option>
                            {% for level in experience_levels %}
                                <option value="{{ level }}" {% if selected_experience_level == level %}selected{% endif %}>
                                    {% if level == 'entry' %}{% trans "Entry Level" %}
                                    {% elif level == 'mid' %}{% trans "Mid Level" %}
                                    {% elif level == 'senior' %}{% trans "Senior Level" %}
                                    {% elif level == 'lead' %}{% trans "Lead Level" %}
                                    {% elif level == 'manager' %}{% trans "Manager Level" %}
                                    {% endif %}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-filter"></i> {% trans "Filter" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Careers List Section -->
<section class="mb-5">
    <div class="container">
        <div class="row g-4">
            {% for career in careers %}
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow-sm career-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <h5 class="card-title">{{ career.title }}</h5>
                            <div>
                                {% if career.job_type == 'full_time' %}
                                <span class="badge bg-primary job-badge">{% trans "Full Time" %}</span>
                                {% elif career.job_type == 'part_time' %}
                                <span class="badge bg-info job-badge">{% trans "Part Time" %}</span>
                                {% elif career.job_type == 'contract' %}
                                <span class="badge bg-warning job-badge">{% trans "Contract" %}</span>
                                {% elif career.job_type == 'internship' %}
                                <span class="badge bg-secondary job-badge">{% trans "Internship" %}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="text-muted small">
                                <i class="bi bi-building me-1"></i> {{ career.department }}
                            </div>
                            <div class="text-muted small">
                                <i class="bi bi-geo-alt me-1"></i> {{ career.location }}
                            </div>
                            <div class="text-muted small">
                                <i class="bi bi-calendar3 me-1"></i> {% trans "Posted" %}: {{ career.created_at|date:"M d, Y" }}
                            </div>
                        </div>
                        <p class="card-text">{{ career.description|truncatewords:20 }}</p>
                    </div>
                    <div class="card-footer bg-white border-top-0">
                        <a href="{% url 'GB_FARM:career_detail' id=career.id %}" class="btn btn-outline-primary w-100">
                            {% trans "View Details" %}
                        </a>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="text-center py-5">
                    <img src="{% static 'img/empty-state.svg' %}" alt="No careers found" class="mb-3" style="max-width: 200px;">
                    <h4>{% trans "No positions available right now" %}</h4>
                    <p class="text-muted">{% trans "Please check back later for new openings." %}</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Why Join Us Section -->
<section class="mb-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="mb-4">{% trans "Why Join GB Farms?" %}</h2>
                <div class="mb-4">
                    <h5><i class="bi bi-graph-up-arrow text-success me-2"></i> {% trans "Growth Opportunities" %}</h5>
                    <p>{% trans "Develop your skills and advance your career with our supportive environment and continuous learning programs." %}</p>
                </div>
                <div class="mb-4">
                    <h5><i class="bi bi-people text-primary me-2"></i> {% trans "Collaborative Culture" %}</h5>
                    <p>{% trans "Work with passionate professionals in a team-oriented atmosphere where your ideas are valued." %}</p>
                </div>
                <div class="mb-4">
                    <h5><i class="bi bi-heart text-danger me-2"></i> {% trans "Work-Life Balance" %}</h5>
                    <p>{% trans "Enjoy flexible working arrangements designed to help you maintain a healthy work-life balance." %}</p>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="ratio ratio-16x9 rounded overflow-hidden shadow">
                    <img src="{% static 'img/team.jpg' %}" alt="GB Farms Team" class="img-fluid" onerror="this.src='https://via.placeholder.com/800x450?text=GB+Farms+Team'">
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %} 