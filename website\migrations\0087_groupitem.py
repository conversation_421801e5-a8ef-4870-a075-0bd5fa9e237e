# Generated by Django 4.2.20 on 2025-07-31 22:15

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0086_increase_coordinate_precision'),
    ]

    operations = [
        migrations.CreateModel(
            name='GroupItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(blank=True, null=True, upload_to='group_items/', verbose_name='Group Item Image ')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='group_items', to='website.newcatgroup')),
            ],
            options={
                'verbose_name': 'Group Item',
                'verbose_name_plural': 'Group Items',
                'db_table': 'group_item',
            },
        ),
    ]
