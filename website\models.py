
from django.contrib.auth.models import AbstractUser, PermissionsMixin
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.utils.text import slugify
from django.core.validators import MinValueValidator, FileExtensionValidator
from django.core.cache import cache
from imagekit.models import ProcessedImageField
from imagekit.processors import ResizeToFill, ResizeToFit
import os
from datetime import datetime, timedelta
import logging
import secrets
from django_prose_editor.fields import ProseEditorField
from django.db import models
from django.conf import settings

logger = logging.getLogger(__name__)

# Define unit choices centrally for reuse in views/templates
ALL_UNIT_CHOICES = {
    'FREE WEIGHT': [
        ('kg', 'Kilogram'),
        ('500g', '500g'),
        ('250g', '250g'),
    ],
    'PIECE': [
        ('piece', 'Piece'),
    ],
    'LITER': [
        ('liter', 'Liter'),
        ('500ml', '500ml'),
        # Add other liquid units if needed
    ],
    # Add other categories like KILOGRAM if they have distinct units
    'KILOGRAM': [
        ('kg', 'Kilogram'),
        ('500g', '500g'),
        ('250g', '250g'),
    ],
    '500G': [ # Map specific categories if needed
        ('500g', '500g'),
    ],
     '250G': [
        ('250g', '250g'),
    ],
}

class SingletonModel(models.Model):
    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        self.pk = 1
        super(SingletonModel, self).save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        pass

    @classmethod
    def load(cls):
        obj, created = cls.objects.get_or_create(pk=1)
        return obj

class User(AbstractUser):
    user_types = [
        ('admin', 'admin'),
        ('customer', 'customer')
    ]
    user_type = models.CharField(max_length=10, choices=user_types, default='customer')
    user_phone = models.CharField(max_length=20, blank=True, null=True)
    
    # Add related_name to avoid clashes with auth.User
    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text='The groups this user belongs to.',
        related_name='website_user_set',
        related_query_name='user',
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name='website_user_set',
        related_query_name='user',
    )

    def __str__(self):
        return self.username


class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    default_shipping_address = models.TextField(blank=True, null=True)
    profile_picture = ProcessedImageField(
        upload_to='profile_pictures/',
        processors=[ResizeToFill(300, 300)],
        format='JPEG',
        options={'quality': 90},
        blank=True,
        null=True
    )
    
    def __str__(self):
        return f"{self.user.username}'s profile"


# Create UserProfile automatically when a User is created
@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)


class Category(models.Model):
    name = models.CharField(max_length=100)
    name_ar = models.CharField(max_length=100, blank=True, null=True, verbose_name="Arabic Name")
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Description")
    image = ProcessedImageField(
        upload_to='categories/',
        processors=[ResizeToFill(800, 600)],
        format='JPEG',
        options={'quality': 90},
        blank=True,
        null=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.name


class Product(models.Model):
    STATUS_CHOICES = [
        ('available', 'Available'),
        ('unavailable', 'Unavailable'),
    ]
    UNIT_CATEGORY = [
        ('FREE WEIGHT', 'Free Weight'),
        ('PIECE', 'Piece'),
        ('LITER', 'Liter'),
    ]

    name = models.CharField(max_length=255)
    name_ar = models.CharField(max_length=255, blank=True, null=True, verbose_name="Arabic Name")
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='products', null=True, blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    stock = models.PositiveIntegerField(default=0)
    unit_category = models.CharField(max_length=20, choices=UNIT_CATEGORY, default='PIECE')
    available_units = models.JSONField(default=list, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='available')
    is_featured = models.BooleanField(default=False)

    image = ProcessedImageField(
        upload_to='products/',
        processors=[ResizeToFill(800, 800)],
        format='JPEG',
        options={'quality': 90},
        blank=True,
        null=True
    )
    video = models.FileField(upload_to='products/', blank=True, null=True)
    thumbnail = ProcessedImageField(
        upload_to='products/thumbnails/',
        processors=[ResizeToFill(800, 800)],
        format='JPEG',
        options={'quality': 80},
        blank=True,
        null=True
    )

    description = models.TextField(blank=True, null=True)
    description_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Description")
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)

    class Meta:
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['category']),
            models.Index(fields=['status']),
            models.Index(fields=['is_featured']),
            models.Index(fields=['created_at']),
            models.Index(fields=['price']),
            models.Index(fields=['stock']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.thumbnail and self.image:
            # Generate thumbnail from main image
            self.thumbnail = self.image
        super().save(*args, **kwargs)

    def get_available_units_display(self):
        """Returns a list of display names for the available units."""
        all_units_flat = {val: disp for cat_units in ALL_UNIT_CHOICES.values() for val, disp in cat_units}
        return [all_units_flat.get(unit_val, unit_val) for unit_val in self.available_units]

    def get_available_units_tuples(self):
        """Returns a list of (value, display) tuples for available units."""
        all_units_flat = {val: disp for cat_units in ALL_UNIT_CHOICES.values() for val, disp in cat_units}
        return [(unit_val, all_units_flat.get(unit_val, unit_val)) for unit_val in self.available_units]


class Offer(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    discount_percentage = models.FloatField(default=0)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    description = models.TextField(blank=True, null=True)
    description_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Description")
    
    def __str__(self):
        return f"{self.product.name} - {self.discount_percentage}% off"


class Cart(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)
    selected_weight = models.CharField(max_length=10, blank=True, null=True)
    price_override = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    added_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        weight_info = f" ({self.selected_weight})" if self.selected_weight else ""
        return f"{self.user.username} - {self.product.name}{weight_info} ({self.quantity})"
    
    class Meta:
        unique_together = ('user', 'product', 'selected_weight')
    
    def clean(self):
        if self.quantity > self.product.stock:
            raise ValidationError(f'Only {self.product.stock} units available in stock.')
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)
    
    @property
    def total_price(self):
        price = self.price_override if self.price_override is not None else self.product.price
        return price * self.quantity


class Wishlist(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='wishlist')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'product')
        ordering = ['-added_at']

    def __str__(self):
        return f"{self.user.username}'s wishlist - {self.product.name}"


class Order(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    ]

    DELIVERY_CHOICES = [
        ('delivery', ' Delivery'),
        ('collect', 'Collect from Store'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    shipping_address = models.TextField(blank=True, null=True)
    delivery_method = models.CharField(max_length=20, choices=DELIVERY_CHOICES, default='delivery')
    delivery_zone = models.ForeignKey('Zone', on_delete=models.SET_NULL, null=True, blank=True, related_name='orders')
    
    # Customer Information
    customer_name = models.CharField(max_length=100, default='')
    phone_number = models.CharField(max_length=15, default='')
    office_number = models.CharField(max_length=10, blank=True, null=True)

    def __str__(self):
        return f"Order #{self.id} - {self.user.username}"

    class Meta:
        ordering = ['-created_at']

    @property
    def delivery_cost(self):
        if self.delivery_method == 'collect':
            return 0
        return self.delivery_zone.price if self.delivery_zone else 0

    @property
    def final_total(self):
        return self.total_amount + self.delivery_cost
        
    @property
    def status_color(self):
        status_colors = {
            'pending': 'warning',
            'processing': 'info',
            'shipped': 'primary',
            'delivered': 'warning',  # Changed to yellow color (warning)
            'cancelled': 'danger'
        }
        return status_colors.get(self.status, 'secondary')
        
    @property
    def payment_status_color(self):
        # For the payment status in timeline
        if self.status in ['processing', 'shipped', 'delivered']:
            return 'success'
        return 'secondary'
        
    @property
    def processing_status_color(self):
        # For the processing status in timeline
        if self.status in ['processing', 'shipped', 'delivered']:
            return 'success'
        elif self.status == 'pending':
            return 'warning'
        return 'secondary'
        
    @property
    def shipping_status_color(self):
        # For the shipping status in timeline
        if self.status in ['shipped', 'delivered']:
            return 'success'
        return 'secondary'
        
    @property
    def delivery_status_color(self):
        # For the delivery status in timeline
        if self.status == 'delivered':
            return 'warning'  # Changed to yellow color
        return 'secondary'
        
    @property
    def payment_status(self):
        if self.status in ['processing', 'shipped', 'delivered']:
            return 'Completed'
        return 'Pending'
        
    @property
    def payment_status_icon(self):
        if self.status in ['processing', 'shipped', 'delivered']:
            return 'check'
        return 'clock'
        
    @property
    def processing_status_icon(self):
        if self.status in ['processing', 'shipped', 'delivered']:
            return 'check'
        return 'clock'
        
    @property
    def shipping_status_icon(self):
        if self.status in ['shipped', 'delivered']:
            return 'check'
        return 'clock'
        
    @property
    def delivery_status_icon(self):
        if self.status == 'delivered':
            return 'check'
        return 'clock'


class OrderItem(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    
    def __str__(self):
        return f"{self.product.name} ({self.quantity}) in Order #{self.order.id}"

    class Meta:
        indexes = [
            models.Index(fields=['order']),
            models.Index(fields=['product']),
        ]


class Payment(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    PAYMENT_METHODS = [
        ('credit_card', 'Credit Card'),
        ('cash', 'Cash'),
        ('instapay', 'Instapay'),
    ]
    
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='payments')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='EGP', editable=False)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending')
    payment_method = models.CharField(max_length=15, choices=PAYMENT_METHODS, default='cash')
    timestamp = models.DateTimeField(auto_now_add=True)
    card_last4 = models.CharField(max_length=4, blank=True, null=True)
    card_brand = models.CharField(max_length=20, blank=True, null=True)
    transaction_id = models.CharField(max_length=100, blank=True, null=True)

    def __str__(self):
        return f"{self.amount} {self.currency} - {self.get_status_display()} - {self.payment_method}"

    class Meta:
        ordering = ['-timestamp']

class Review(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='reviews')
    rating = models.IntegerField()
    comment = models.TextField()
    comment_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Comment")
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.user.username}'s review for {self.product.name}"
    
    class Meta:
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['product']),
            models.Index(fields=['rating']),
        ]

class Stock(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='stock_records')
    quantity = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.product.name} - {self.quantity}"

class APIToken(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='api_tokens')
    token = models.CharField(max_length=64, unique=True)
    name = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    last_used_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.user.username}'s token: {self.name}"

    def save(self, *args, **kwargs):
        if not self.token:
            self.token = secrets.token_urlsafe(32)
        super().save(*args, **kwargs)

    def is_valid(self):
        if not self.is_active:
            return False
        if self.expires_at and timezone.now() > self.expires_at:
            return False
        return True

    def update_last_used(self):
        self.last_used_at = timezone.now()
        self.save(update_fields=['last_used_at'])

    class Meta:
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['token']),
            models.Index(fields=['is_active']),
            models.Index(fields=['expires_at']),
        ]

class CarouselImage(models.Model):
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True, null=True, verbose_name="Arabic Title")
    description = models.TextField(blank=True, null=True)
    description_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Description")
    image = ProcessedImageField(
        upload_to='carousel/',
        processors=[ResizeToFit(1920, 1080)],
        format='JPEG',
        options={'quality': 90}
    )
    button_text = models.CharField(max_length=50, blank=True, null=True)
    button_text_ar = models.CharField(max_length=50, blank=True, null=True, verbose_name="Arabic Button Text")
    button_link = models.URLField(blank=True, null=True)
    order = models.PositiveIntegerField(default=0, help_text="Display order")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order']
        verbose_name = "Carousel Image"
        verbose_name_plural = "Carousel Images"

    def __str__(self):
        return self.title

class WebsiteVideo(models.Model):
    title = models.CharField(max_length=255)
    title_ar = models.CharField(max_length=255, blank=True, null=True, verbose_name="Arabic Title")
    video = models.FileField(upload_to='website_videos/')
    is_active = models.BooleanField(default=True)
    opacity = models.FloatField(default=0.3, help_text="Opacity value between 0 and 1")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.opacity:
            self.opacity = 0.3
        super().save(*args, **kwargs)

class OrderMessage(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages')
    message = models.TextField()
    message_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Message")
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)
    
    def __str__(self):
        return f"Message for Order #{self.order.id} from {self.sender.username}"
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['order']),
            models.Index(fields=['sender']),
            models.Index(fields=['created_at']),
            models.Index(fields=['is_read']),
        ]

class Footer(models.Model):
    address = models.CharField(max_length=255)
    address_ar = models.CharField(max_length=255, blank=True, null=True, verbose_name="Arabic Address")
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    facebook_link = models.URLField(blank=True, null=True)
    instagram_link = models.URLField(blank=True, null=True)
    twitter_link = models.URLField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return "Footer Content"

    class Meta:
        verbose_name = "Footer"
        verbose_name_plural = "Footer"

class ExternalFooter(SingletonModel):
    address = models.CharField(max_length=255, help_text="e.g. 16 Hussein Wassel St, Maadi, Dokki, Egypt")
    address_ar = models.CharField(max_length=255, blank=True, null=True, verbose_name="Arabic Address")
    phone = models.CharField(max_length=100, help_text="e.g. +2035392341 / +20109931930")
    email = models.EmailField()
    copyright_text = models.CharField(max_length=255, default="ALL RIGHTS RESERVED © GB FARMS 2023")
    facebook_url = models.URLField(blank=True, null=True)
    instagram_url = models.URLField(blank=True, null=True)
    youtube_url = models.URLField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "External Footer"
        verbose_name_plural = "External Footer"

    def __str__(self):
        return "External Footer Content"

class Zone(models.Model):
    name = models.CharField(max_length=255)
    name_ar = models.CharField(max_length=255, blank=True, null=True, verbose_name="Arabic Name")
    price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.price} EGP"

class Delivery(models.Model):
    DELIVERY_TYPE_CHOICES = [
        ('Delivery', 'Delivery'),
        ('pickup', 'Store Pickup')
    ]
    
    name = models.CharField(max_length=255)
    name_ar = models.CharField(max_length=255, blank=True, null=True, verbose_name="Arabic Name")
    delivery_type = models.CharField(max_length=10, choices=DELIVERY_TYPE_CHOICES, default='home')
    zones = models.ManyToManyField(Zone, related_name='deliveries')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Delivery"
        verbose_name_plural = "Deliveries"

class ContactInfo(models.Model):
    title = models.CharField(max_length=100, default="Contact Us")
    title_ar = models.CharField(max_length=100, blank=True, null=True, default="اتصل بنا", verbose_name="Arabic Title")
    address = models.CharField(max_length=255)
    address_ar = models.CharField(max_length=255, blank=True, null=True, verbose_name="Arabic Address")
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    working_hours = models.CharField(max_length=100, blank=True, null=True)
    working_hours_ar = models.CharField(max_length=100, blank=True, null=True, verbose_name="Arabic Working Hours")
    latitude = models.DecimalField(max_digits=10, decimal_places=8, default=30.080062373661846, null=True, blank=True)
    longitude = models.DecimalField(max_digits=10, decimal_places=8, default=31.04347892147481, null=True, blank=True)
    map_embed = models.TextField(blank=True, null=True, help_text="Google Maps embed code")
    additional_info = models.TextField(blank=True, null=True)
    additional_info_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Additional Info")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Contact Information"
        verbose_name_plural = "Contact Information"

    def __str__(self):
        return self.title

class InvoiceLog(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='invoice_logs')
    invoice_number = models.CharField(max_length=50)
    generated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='generated_invoices')
    created_at = models.DateTimeField(auto_now_add=True)
    last_accessed = models.DateTimeField(auto_now=True)
    access_count = models.PositiveIntegerField(default=1)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    ip_address = models.CharField(max_length=50, blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    
    class Meta:
        verbose_name = "Invoice Log"
        verbose_name_plural = "Invoice Logs"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['order']),
            models.Index(fields=['invoice_number']),
            models.Index(fields=['generated_by']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Invoice #{self.invoice_number} for Order #{self.order.id}"

class About(models.Model):
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True, null=True, verbose_name="Arabic Title")
    story_title = models.CharField(max_length=200)
    story_title_ar = models.CharField(max_length=200, blank=True, null=True, verbose_name="Arabic Story Title")
    story_content = models.TextField()
    story_content_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Story Content")
    
    operations_title = models.CharField(max_length=200)
    operations_title_ar = models.CharField(max_length=200, blank=True, null=True, verbose_name="Arabic Operations Title")
    operations_content = models.TextField()
    operations_content_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Operations Content")
    
    values_title = models.CharField(max_length=200)
    values_title_ar = models.CharField(max_length=200, blank=True, null=True, verbose_name="Arabic Values Title")
    values_content = models.TextField()
    values_content_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Values Content")
    
    # Contact information
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "About Information"
        verbose_name_plural = "About Information"
    
    def __str__(self):
        return self.title

class Career(models.Model):
    JOB_TYPE_CHOICES = [
        ('full_time', 'Full Time'),
        ('part_time', 'Part Time'),
        ('contract', 'Contract'),
        ('internship', 'Internship'),
    ]
    
    EXPERIENCE_LEVEL_CHOICES = [
        ('entry', 'Entry Level'),
        ('mid', 'Mid Level'),
        ('senior', 'Senior Level'),
        ('lead', 'Lead Level'),
        ('manager', 'Manager Level'),
    ]
    
    title = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200, blank=True, null=True, verbose_name="Arabic Title")
    department = models.CharField(max_length=100)
    department_ar = models.CharField(max_length=100, blank=True, null=True, verbose_name="Arabic Department")
    location = models.CharField(max_length=100)
    location_ar = models.CharField(max_length=100, blank=True, null=True, verbose_name="Arabic Location")
    job_type = models.CharField(max_length=20, choices=JOB_TYPE_CHOICES)
    experience_level = models.CharField(max_length=20, choices=EXPERIENCE_LEVEL_CHOICES)
    description = models.TextField()
    description_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Description")
    requirements = models.TextField()
    requirements_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Requirements")
    responsibilities = models.TextField()
    responsibilities_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Responsibilities")
    benefits = models.TextField(blank=True, null=True)
    benefits_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Benefits")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Career"
        verbose_name_plural = "Careers"
        ordering = ['-created_at']

    def __str__(self):
        return self.title

class CareerApplication(models.Model):
    career = models.ForeignKey(Career, on_delete=models.CASCADE, related_name='applications')
    full_name = models.CharField(max_length=200)
    email = models.EmailField()
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    cv_file = models.FileField(upload_to='career_cvs/', blank=True, null=True)
    cover_letter = models.TextField(blank=True, null=True)
    years_of_experience = models.IntegerField(blank=True, null=True)
    current_position = models.CharField(max_length=200, blank=True, null=True)
    linkedin_profile = models.URLField(blank=True, null=True)
    portfolio_website = models.URLField(blank=True, null=True)
    salary_expectation = models.CharField(max_length=100, blank=True, null=True)
    available_start_date = models.DateField(blank=True, null=True)
    is_processed = models.BooleanField(default=False)
    notes = models.TextField(blank=True, null=True, help_text="Admin notes")
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Career Application"
        verbose_name_plural = "Career Applications"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.full_name} - {self.career.title}"

class ProductSeason(models.Model):
    """Model for managing product seasonal availability"""
    
    MONTH_CHOICES = [
        (1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'),
        (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'),
        (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December'),
    ]
    
    name = models.CharField(max_length=100, verbose_name="Product Name")
    name_ar = models.CharField(max_length=100, blank=True, null=True, verbose_name="Arabic Product Name")
    
    start_month = models.PositiveIntegerField(choices=MONTH_CHOICES, verbose_name="From Month")
    end_month = models.PositiveIntegerField(choices=MONTH_CHOICES, verbose_name="To Month")
    
    description = models.TextField(blank=True, null=True)
    description_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Description")

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        verbose_name = "Product Season"
        verbose_name_plural = "Product Seasons"
        ordering = ['order', 'start_month', 'name']
    
    def __str__(self):
        return f"{self.name} ({self.get_start_month_display()} - {self.get_end_month_display()})"

class Configuration(SingletonModel):
    objects = models.Manager()
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Created At")
    updated_on = models.DateTimeField(auto_now=True, verbose_name="Updated On")
    leasing_short_description = models.TextField(verbose_name="Leasing Short Description", blank=True, null=True, help_text="Short description for Leasing in header")
    factoring_short_description = models.TextField(verbose_name="Factoring Short Description", blank=True, null=True, help_text="Short description for Factoring in header")
    company_overview_description = ProseEditorField(verbose_name="Company Overview Description", blank=True, null=True)
    company_overview_image1 = models.ImageField(upload_to="company_overview/", verbose_name="Company Overview IMG1", null=True, blank=True, validators=[FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'ico', 'svg', 'gif'])])
    company_overview_image2 = models.ImageField(upload_to="company_overview/", verbose_name="Company Overview IMG2", null=True, blank=True, validators=[FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'ico', 'svg', 'gif'])])
    governance_description = models.TextField(verbose_name="Governance Description", blank=True, null=True)
    about_us_short_description = models.TextField(verbose_name="About Us Short Description", blank=True, null=True, help_text="Short description for About Us in header")
    linkedin_url = models.URLField(verbose_name="LinkedIn URL", blank=True, null=True)
    emails = models.TextField(verbose_name="Emails", blank=True, null=True, help_text="Emails For Requests")
    
    # SEO tag fields for different pages
    home_SEO_description = models.TextField(verbose_name="Home SEO Description", null=True, blank=True, help_text="Home page SEO Description")
    home_SEO_key_words = models.TextField(verbose_name="Home SEO Key Words", null=True, blank=True, help_text="Home page SEO Key Words")
    about_SEO_description = models.TextField(verbose_name="About SEO Description", null=True, blank=True, help_text="About page SEO Description")
    about_SEO_key_words = models.TextField(verbose_name="About SEO Key Words", null=True, blank=True, help_text="About page SEO Key Words")
    contact_SEO_description = models.TextField(verbose_name="Contact SEO Description", null=True, blank=True, help_text="Contact page SEO Description")
    contact_SEO_key_words = models.TextField(verbose_name="Contact SEO Key Words", null=True, blank=True, help_text="Contact page SEO Key Words")
    news_SEO_description = models.TextField(verbose_name="News SEO Description", null=True, blank=True, help_text="News page SEO Description")
    news_SEO_key_words = models.TextField(verbose_name="News SEO Key Words", null=True, blank=True, help_text="News page SEO Key Words")
    careers_SEO_description = models.TextField(verbose_name="Careers SEO Description", null=True, blank=True, help_text="Careers page SEO Description")
    careers_SEO_key_words = models.TextField(verbose_name="Careers SEO Key Words", null=True, blank=True, help_text="Careers page SEO Key Words")
    leasing_benefits_SEO_description = models.TextField(verbose_name="Leasing Benefits SEO Description", null=True, blank=True, help_text="Leasing benefits page SEO Description")
    leasing_benefits_SEO_key_words = models.TextField(verbose_name="Leasing Benefits SEO Key Words", null=True, blank=True, help_text="Leasing benefits page SEO Key Words")
    factoring_benefits_SEO_description = models.TextField(verbose_name="Factoring Benefits SEO Description", null=True, blank=True, help_text="Factoring benefits page SEO Description")
    factoring_benefits_SEO_key_words = models.TextField(verbose_name="Factoring Benefits SEO Key Words", null=True, blank=True, help_text="Factoring benefits page SEO Key Words")
    
    class Meta:
        verbose_name = "Configuration"
        verbose_name_plural = "Configuration"
    def __str__(self):
        return "Site Configuration"
    def get_home_seo_keywords_list(self):
        if self.home_SEO_key_words:
            return [keyword.strip() for keyword in self.home_SEO_key_words.split(",") if keyword.strip()]
        return []
    def get_about_seo_keywords_list(self):
        if self.about_SEO_key_words:
            return [keyword.strip() for keyword in self.about_SEO_key_words.split(",") if keyword.strip()]
        return []
    def get_contact_seo_keywords_list(self):
        if self.contact_SEO_key_words:
            return [keyword.strip() for keyword in self.contact_SEO_key_words.split(",") if keyword.strip()]
        return []
    def get_news_seo_keywords_list(self):
        if self.news_SEO_key_words:
            return [keyword.strip() for keyword in self.news_SEO_key_words.split(",") if keyword.strip()]
        return []
    def get_careers_seo_keywords_list(self):
        if self.careers_SEO_key_words:
            return [keyword.strip() for keyword in self.careers_SEO_key_words.split(",") if keyword.strip()]
        return []
    def get_leasing_benefits_seo_keywords_list(self):
        if self.leasing_benefits_SEO_key_words:
            return [keyword.strip() for keyword in self.leasing_benefits_SEO_key_words.split(",") if keyword.strip()]
        return []
    def get_factoring_benefits_seo_keywords_list(self):
        if self.factoring_benefits_SEO_key_words:
            return [keyword.strip() for keyword in self.factoring_benefits_SEO_key_words.split(",") if keyword.strip()]
        return []






class ContactUsMessage(models.Model):
    BUSINESS_TYPE_CHOICES = [
        ('supermarket', 'Supermarkets'),
        ('wholesaler', 'Wholesaler'),
        ('distributor', 'Distributor'),
    ]

    FIND_OUT_SOURCE_CHOICES = [
        ('internet', 'Internet'),
        ('exhibitions', 'Exhibitions'),
        ('search_engine', 'Search Engine'),
    ]

    name = models.CharField(max_length=100)
    company_name = models.CharField(max_length=255, blank=True, null=True)
    email = models.EmailField()
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    business_type = models.CharField(max_length=50, choices=BUSINESS_TYPE_CHOICES, blank=True, null=True)
    find_out_source = models.CharField(max_length=50, choices=FIND_OUT_SOURCE_CHOICES, blank=True, null=True)
    subject = models.CharField(max_length=255, blank=True, null=True)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Contact Us Message"
        verbose_name_plural = "Contact Us Messages"
        ordering = ['-created_at']

    def __str__(self):
        return f"Message from {self.name} ({self.email})"

class ExternalContactSubmission(models.Model):
    BUSINESS_TYPE_CHOICES = [
        ('supermarkets', 'Supermarkets'),
        ('wholesaler', 'Wholesaler'),
        ('distributor', 'Distributor'),
    ]

    FIND_OUT_METHOD_CHOICES = [
        ('internet', 'Internet'),
        ('exhibitions', 'Exhibitions'),
        ('search_engine', 'Search Engine'),
    ]

    full_name = models.CharField(max_length=100)
    email = models.EmailField()
    message = models.TextField()
    source_page = models.CharField(max_length=30, blank=True, null=True)
    ip_address = models.CharField(max_length=50, blank=True, null=True)
    
    # Additional fields from the contact form
    company_name = models.CharField(max_length=255, blank=True, null=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    business_type = models.CharField(max_length=50, choices=BUSINESS_TYPE_CHOICES, blank=True, null=True)
    find_out_method = models.CharField(max_length=50, choices=FIND_OUT_METHOD_CHOICES, blank=True, null=True)
    subject = models.CharField(max_length=255, blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    is_processed = models.BooleanField(default=False)
    
    class Meta:
        verbose_name = "External Contact Submission"
        verbose_name_plural = "External Contact Submissions"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['created_at']),
            models.Index(fields=['source_page']),
            models.Index(fields=['is_processed']),
        ]
    
    def __str__(self):
        return f"Contact from {self.full_name} - {self.source_page}"

class NewsletterSubscription(models.Model):
    email = models.EmailField(unique=True)
    source_page = models.CharField(max_length=30, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "Newsletter Subscription"
        verbose_name_plural = "Newsletter Subscriptions"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['created_at']),
            models.Index(fields=['source_page']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"Newsletter Subscription: {self.email}"




class ProductType(models.Model):
    """Model to manage product types for the calendar"""
    name = models.CharField(max_length=100)
    name_ar = models.CharField(max_length=100, blank=True, null=True, verbose_name="Arabic Name")
    slug = models.SlugField(unique=True)
    color = models.CharField(max_length=7, default='#22c55e', help_text="Hex color code for calendar display")
    description = models.TextField(blank=True, null=True)
    description_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Description")
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Product Type"
        verbose_name_plural = "Product Types"
        ordering = ['order', 'name']
    
    def __str__(self):
        return self.name


class ProductAvailability(models.Model):
    """Model to manage month-by-month availability for product types"""
    
    MONTH_CHOICES = [
        (1, 'January'),
        (2, 'February'),
        (3, 'March'),
        (4, 'April'),
        (5, 'May'),
        (6, 'June'),
        (7, 'July'),
        (8, 'August'),
        (9, 'September'),
        (10, 'October'),
        (11, 'November'),
        (12, 'December'),
    ]
    
    STATUS_CHOICES = [
        ('available', 'Available'),
        ('limited', 'Limited Availability'),
        ('none', 'Not Available'),
        ('peak', 'Peak Season'),
    ]
    
    product_type = models.ForeignKey(ProductType, on_delete=models.CASCADE, related_name='availabilities')
    year = models.PositiveIntegerField()
    month = models.PositiveIntegerField(choices=MONTH_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    notes = models.TextField(blank=True, null=True, help_text="Additional notes about availability")
    notes_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Notes")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Product Availability"
        verbose_name_plural = "Product Availabilities"
        ordering = ['year', 'month', 'product_type']
        unique_together = ('product_type', 'year', 'month')
        indexes = [
            models.Index(fields=['product_type', 'year', 'month']),
            models.Index(fields=['status']),
            models.Index(fields=['year']),
        ]
    
    def __str__(self):
        return f"{self.product_type.name} - {self.get_month_display()} {self.year} ({self.get_status_display()})"


class ProdCat(models.Model):
    """Model for calendar product categories with date ranges"""
    
    MONTH_CHOICES = [
        (1, 'January'),
        (2, 'February'),
        (3, 'March'),
        (4, 'April'),
        (5, 'May'),
        (6, 'June'),
        (7, 'July'),
        (8, 'August'),
        (9, 'September'),
        (10, 'October'),
        (11, 'November'),
        (12, 'December'),
    ]
    
    name = models.CharField(max_length=255, verbose_name="Product Name")
    month = models.PositiveIntegerField(choices=MONTH_CHOICES, verbose_name="Month")
    date_from = models.DateField(verbose_name="From Date")
    date_to = models.DateField(verbose_name="To Date")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Created At")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="Updated At")

    class Meta:
        verbose_name = "Product Calendar"
        verbose_name_plural = "Product Calendars"
        ordering = ['month', 'date_from', 'name']
        indexes = [
            models.Index(fields=['month']),
            models.Index(fields=['date_from', 'date_to']),
            models.Index(fields=['name']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.name} - {self.get_month_display()} ({self.date_from} to {self.date_to})"

    def clean(self):
        if self.date_from and self.date_to and self.date_from > self.date_to:
            raise ValidationError("From Date cannot be later than To Date")


class CountryFlag(models.Model):
    CONTINENT_CHOICES = [
        ('europe', 'Europe'),
        ('africa', 'Africa'),
        ('gulf', 'Gulf'),
        ('asia', 'Asia'),
    ]

    continent = models.CharField(max_length=20, choices=CONTINENT_CHOICES)
    country_name = models.CharField(max_length=100)
    flag_image = ProcessedImageField(
        upload_to='flags/',
        processors=[ResizeToFill(50, 50)],
        format='PNG',
        options={'quality': 90},
        blank=True,
        null=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Country Flag"
        verbose_name_plural = "Country Flags"
        ordering = ['continent', 'country_name']

    def __str__(self):
        return f"{self.country_name} ({self.get_continent_display()})"


class AboutUsVideo(SingletonModel):
    title = models.CharField(max_length=200, default="About Us")
    title_ar = models.CharField(max_length=200, blank=True, null=True, verbose_name="Arabic Title")
    description = models.TextField(
        default="GB Farms was established in 1991 by Dr. Raouf Ghabbour, founder and CEO of GB Auto, a publicly traded company leading the MENA region in automotive manufacturing and distribution, and financial solutions with a workforce of more than 23,000 employees."
    )
    description_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Description")
    video = models.FileField(
        upload_to='website_videos/',
        blank=True,
        null=True,
        help_text="Upload video file for About Us section"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "About Us Video Section"
        verbose_name_plural = "About Us Video Section"

    def __str__(self):
        return "About Us Video Section"


class GrapeVariety(models.Model):
    COLOR_CHOICES = [
        ('white', 'White Seedless'),
        ('red', 'Red Seedless'),
        ('black', 'Black Seedless'),
    ]
    
    WEIGHT_UNIT_CHOICES = [
        ('gm', 'Grams'),
        ('kg', 'Kilograms'),
    ]
    
    name = models.CharField(max_length=100)
    color = models.CharField(max_length=10, choices=COLOR_CHOICES)
    
    # Date fields - storing as separate fields for flexibility
    date_from_day = models.PositiveIntegerField(help_text="Day (1-31)")
    date_from_month = models.CharField(max_length=3, help_text="Month abbreviation (Jan, Feb, etc.)")
    date_to_day = models.PositiveIntegerField(help_text="Day (1-31)")
    date_to_month = models.CharField(max_length=3, help_text="Month abbreviation (Jan, Feb, etc.)")
    
    # Weight fields
    weight_from = models.PositiveIntegerField(help_text="Minimum weight")
    weight_to = models.PositiveIntegerField(help_text="Maximum weight")
    weight_unit = models.CharField(max_length=2, choices=WEIGHT_UNIT_CHOICES, default='gm')
    

    
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['color', 'order', 'name']
        verbose_name = 'Grape Variety'
        verbose_name_plural = 'Grape Varieties'

    def __str__(self):
        return f"{self.name} ({self.get_color_display()})"

    @property
    def date_range_display(self):
        """Returns formatted date range like '5 Sep - 10 Nov'"""
        return f"{self.date_from_day} {self.date_from_month} - {self.date_to_day} {self.date_to_month}"
    
    @property
    def weight_range_display(self):
        """Returns formatted weight range like '1000 - 350 gm'"""
        return f"{self.weight_from} - {self.weight_to} {self.weight_unit}"


class MangoVariety(models.Model):
    VARIETY_TYPE_CHOICES = [
        ('international', 'International Varieties'),
        ('local', 'Local Varieties'),
    ]
    
    WEIGHT_UNIT_CHOICES = [
        ('gm', 'Grams'),
        ('kg', 'Kilograms'),
    ]
    
    name = models.CharField(max_length=100)
    variety_type = models.CharField(max_length=15, choices=VARIETY_TYPE_CHOICES)
    
    # Date fields - storing as separate fields for flexibility
    date_from_day = models.PositiveIntegerField(help_text="Day (1-31)")
    date_from_month = models.CharField(max_length=3, help_text="Month abbreviation (Jan, Feb, etc.)")
    date_to_day = models.PositiveIntegerField(help_text="Day (1-31)")
    date_to_month = models.CharField(max_length=3, help_text="Month abbreviation (Jan, Feb, etc.)")
    
    # Weight fields
    weight_from = models.PositiveIntegerField(help_text="Minimum weight")
    weight_to = models.PositiveIntegerField(help_text="Maximum weight")
    weight_unit = models.CharField(max_length=2, choices=WEIGHT_UNIT_CHOICES, default='gm')
    

    
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['variety_type', 'order', 'name']
        verbose_name = 'Mango Variety'
        verbose_name_plural = 'Mango Varieties'

    def __str__(self):
        return f"{self.name} ({self.get_variety_type_display()})"
    
    @property
    def date_range_display(self):
        """Returns formatted date range like '5 Sep - 10 Nov'"""
        return f"{self.date_from_day} {self.date_from_month} - {self.date_to_day} {self.date_to_month}"
    
    @property
    def weight_range_display(self):
        """Returns formatted weight range like '1000 - 350 gm'"""
        return f"{self.weight_from} - {self.weight_to} {self.weight_unit}"


class OtherProduct(models.Model):
    WEIGHT_UNIT_CHOICES = [
        ('gm', 'Grams'),
        ('kg', 'Kilograms'),
    ]
    
    name = models.CharField(max_length=100)
    
    # Date fields - storing as separate fields for flexibility
    date_from_day = models.PositiveIntegerField(help_text="Day (1-31)")
    date_from_month = models.CharField(max_length=3, help_text="Month abbreviation (Jan, Feb, etc.)")
    date_to_day = models.PositiveIntegerField(help_text="Day (1-31)")
    date_to_month = models.CharField(max_length=3, help_text="Month abbreviation (Jan, Feb, etc.)")
    
    # Weight fields
    weight_from = models.PositiveIntegerField(help_text="Minimum weight")
    weight_to = models.PositiveIntegerField(help_text="Maximum weight")
    weight_unit = models.CharField(max_length=2, choices=WEIGHT_UNIT_CHOICES, default='gm')
    

    
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'name']
        verbose_name = 'Other Product'
        verbose_name_plural = 'Other Products'

    def __str__(self):
        return self.name
    
    @property
    def date_range_display(self):
        """Returns formatted date range like '5 Sep - 10 Nov'"""
        return f"{self.date_from_day} {self.date_from_month} - {self.date_to_day} {self.date_to_month}"
    
    @property
    def weight_range_display(self):
        """Returns formatted weight range like '1000 - 350 gm'"""
        return f"{self.weight_from} - {self.weight_to} {self.weight_unit}"


class Branch(models.Model):
    """Model for managing company branches with map coordinates"""
    
    name = models.CharField(max_length=200, verbose_name="Branch Name")
    name_ar = models.CharField(max_length=200, blank=True, null=True, verbose_name="Arabic Branch Name")
    address = models.TextField(verbose_name="Full Address")
    address_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Address")
    
    # Coordinates for map positioning
    latitude = models.DecimalField(
        max_digits=20,
        decimal_places=15,
        help_text="Latitude coordinate (e.g., 30.037 for Cairo)"
    )
    longitude = models.DecimalField(
        max_digits=20,
        decimal_places=15,
        help_text="Longitude coordinate (e.g., 31.207 for Cairo)"
    )
    
    # Status
    is_active = models.BooleanField(default=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Branch"
        verbose_name_plural = "Branches"
        ordering = ['name']
        indexes = [
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.name}"
    
    def clean(self):
        # Validate coordinates are within reasonable ranges
        if self.latitude is not None and (self.latitude < -90 or self.latitude > 90):
            raise ValidationError("Latitude must be between -90 and 90 degrees.")
        if self.longitude is not None and (self.longitude < -180 or self.longitude > 180):
            raise ValidationError("Longitude must be between -180 and 180 degrees.")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)
    
    @property
    def coordinates_display(self):
        """Returns formatted coordinates"""
        return f"{self.latitude}, {self.longitude}"
    
    @property
    def google_maps_url(self):
        """Returns Google Maps URL for this location"""
        return f"https://www.google.com/maps?q={self.latitude},{self.longitude}"


class CorporateSocial(SingletonModel):
    """Model to manage the Corporate Social Responsibility section."""
    title = models.CharField(max_length=200, blank=True, null=True)
    title_ar = models.CharField(max_length=200, blank=True, null=True, verbose_name="Arabic Title")
    description_part1 = models.TextField(blank=True, null=True)
    description_part1_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Description Part 1")
    description_part2 = models.TextField(blank=True, null=True)
    description_part2_ar = models.TextField(blank=True, null=True, verbose_name="Arabic Description Part 2")
    image = ProcessedImageField(
        upload_to='corporate_social/',
        processors=[ResizeToFill(1920, 1080)],
        format='JPEG',
        options={'quality': 90},
        blank=True,
        null=True
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Corporate Social Responsibility"
        verbose_name_plural = "Corporate Social Responsibility"

    def __str__(self):
        return self.title or "Corporate Social Responsibility"


from django.db import models


class NewCatGroup(models.Model):
    """مجموعة رئيسية (مثال: Others, Mangoes...)"""
    title = models.CharField(max_length=100, verbose_name="Group Title", blank=True, null=True)  # اسم المجموعة الرئيسية
    table_title = models.CharField(max_length=100, verbose_name="Table Title")  # العنوان في الجدول (مثال: Our Products)
    main_image = models.ImageField(upload_to="groups/", blank=True, null=True, verbose_name="Right Image ", help_text="Please remove background first before uploading")
    title_color = models.CharField(max_length=7, default="#ef4444", verbose_name="Title Color", help_text="Hex color code (e.g., #f59e0b for yellow, #ef4444 for red)")
    underline_color = models.CharField(max_length=7, default="#ef4444", verbose_name="Underline Color", help_text="Hex color code for the underline")
    table_title_color = models.CharField(max_length=7, default="#ef4444", verbose_name="Table Title Color", help_text="Hex color code for the table title")
    order = models.PositiveIntegerField(default=0, verbose_name="Display Order", blank=False, null=False)

    class Meta:
        db_table = "new_cat_group"
        verbose_name = "Category Group"
        verbose_name_plural = "Category Groups"

    def __str__(self):
        return self.title or f"Group #{self.id}"


class NewCatGroupDescription(models.Model):
    """الوصف النصي أسفل المجموعة (4 أسطر وصفية مثلاً)"""
    group = models.ForeignKey(NewCatGroup, related_name="descriptions", on_delete=models.CASCADE ,null=True, blank=True)
    text = models.TextField(verbose_name="Description Text", blank=True, null=True)
    color = models.CharField(max_length=7, default="#FF0000", verbose_name="Text Color")

    class Meta:
        db_table = "new_cat_group_description"
        verbose_name = "Group Description"
        verbose_name_plural = "Group Descriptions"

    def __str__(self):
        return f"Description for {self.group.title}"


class NewProduct(models.Model):
    """منتج داخل المجموعة"""
    group = models.ForeignKey(NewCatGroup, related_name="products", on_delete=models.CASCADE ,null=True, blank=True)
    name = models.CharField(max_length=100, verbose_name="Product Name")
    color = models.CharField(max_length=7, default="#FF0000", verbose_name="Row Color")
    start_date = models.DateField(verbose_name="Start Date")
    end_date = models.DateField(verbose_name="End Date")
    weight_from = models.PositiveIntegerField(verbose_name="Min Weight")
    weight_to = models.PositiveIntegerField(verbose_name="Max Weight")
    is_active = models.BooleanField(default=True, verbose_name="Is Active?")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    

    class Meta:
        db_table = "new_product"
        verbose_name = "Product"
        verbose_name_plural = "Products"

    def __str__(self):
        return self.name

    @property
    def date_range_display(self):
        """عرض فترة التواجد بشكل أنيق"""
        return f"{self.start_date} - {self.end_date}"

    @property
    def weight_range_display(self):
        """عرض الوزن بشكل أنيق"""
        return f"{self.weight_from}g - {self.weight_to}g"


class NewVariety(models.Model):
    """الصنف (Variety) الخاص بالمنتج"""
    product = models.ForeignKey(NewProduct, related_name="varieties", on_delete=models.CASCADE)
    name = models.CharField(max_length=100, verbose_name="Variety Name")
    start_date = models.DateField(verbose_name="Start Date")
    end_date = models.DateField(verbose_name="End Date")
    weight_from = models.PositiveIntegerField(verbose_name="Min Weight")
    weight_to = models.PositiveIntegerField(verbose_name="Max Weight")
    is_active = models.BooleanField(default=True, verbose_name="Is Active?")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "new_variety"
        verbose_name = "Variety"
        verbose_name_plural = "Varieties"

    def __str__(self):
        return f"{self.name} ({self.product.name})"

    @property
    def date_range_display(self):
        return f"{self.start_date} - {self.end_date}"

    @property
    def weight_range_display(self):
        return f"{self.weight_from}g - {self.weight_to}g"

class GroupItem(models.Model):
  image = models.ImageField(upload_to='group_items/', blank=True, null=True, verbose_name="Group Item Image ")
  group = models.ForeignKey(NewCatGroup, related_name="group_items", on_delete=models.CASCADE)
  is_active = models.BooleanField(default=True, verbose_name="Is Active?")
  created_at = models.DateTimeField(auto_now_add=True)
  updated_at = models.DateTimeField(auto_now=True)

  class Meta:
      db_table = "group_item"
      verbose_name = "Group Item"
      verbose_name_plural = "Group Items"

  def __str__(self):
      return f"Group Item for {self.group.title}"        