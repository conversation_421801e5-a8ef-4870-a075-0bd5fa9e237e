{% extends 'GB_FARM/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "About Us" %}{% endblock %}

{% block content %}
{% get_current_language as CURRENT_LANGUAGE %}
<div class="container py-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            {% if about_info %}
                {% if CURRENT_LANGUAGE == 'ar' and about_info.title_ar %}
                    <h1 class="text-center mb-5">{{ about_info.title_ar }}</h1>
                    
                    <div class="card shadow-sm mb-4" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px);">
                        <div class="card-body">
                            <h2 class="h4 mb-4">{{ about_info.story_title_ar|default:about_info.story_title }}</h2>
                            <p class="lead">{{ about_info.story_content_ar|default:about_info.story_content }}</p>
                        </div>
                    </div>

                    <div class="card shadow-sm mb-4" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px);">
                        <div class="card-body">
                            <h2 class="h4 mb-4">{{ about_info.operations_title_ar|default:about_info.operations_title }}</h2>
                            <p>{{ about_info.operations_content_ar|default:about_info.operations_content|linebreaks }}</p>
                        </div>
                    </div>

                    <div class="card shadow-sm mb-4" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px);">
                        <div class="card-body">
                            <h2 class="h4 mb-4">{{ about_info.values_title_ar|default:about_info.values_title }}</h2>
                            <p>{{ about_info.values_content_ar|default:about_info.values_content|linebreaks }}</p>
                        </div>
                    </div>
                {% else %}
                    <h1 class="text-center mb-5">{{ about_info.title }}</h1>
                    
                    <div class="card shadow-sm mb-4" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px);">
                        <div class="card-body">
                            <h2 class="h4 mb-4">{{ about_info.story_title }}</h2>
                            <p class="lead">{{ about_info.story_content }}</p>
                        </div>
                    </div>

                    <div class="card shadow-sm mb-4" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px);">
                        <div class="card-body">
                            <h2 class="h4 mb-4">{{ about_info.operations_title }}</h2>
                            <p>{{ about_info.operations_content|linebreaks }}</p>
                        </div>
                    </div>

                    <div class="card shadow-sm mb-4" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px);">
                        <div class="card-body">
                            <h2 class="h4 mb-4">{{ about_info.values_title }}</h2>
                            <p>{{ about_info.values_content|linebreaks }}</p>
                        </div>
                    </div>
                {% endif %}
            {% else %}
                <h1 class="text-center mb-5">{% trans "About GB Farms" %}</h1>
                
                <div class="card shadow-sm mb-4" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px);">
                    <div class="card-body">
                        <h2 class="h4 mb-4">{% trans "Our Story" %}</h2>
                        <p class="lead">{% trans "GB Farms was established in 1991 by Dr. Raouf Ghabbour, founder and CEO of GB Auto, a publicly traded company leading the MENA region in automotive manufacturing and distribution, with a workforce of around 14,000 employees." %}</p>
                    </div>
                </div>

                <div class="card shadow-sm mb-4" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px);">
                    <div class="card-body">
                        <h2 class="h4 mb-4">{% trans "Our Operations" %}</h2>
                        <p>{% trans "GB Farms owns approximately 2,000 acres of land specialized in growing and exporting a wide range of high-quality fruits and vegetables from Egypt." %}</p>
                        <p>{% trans "We ship our products to customers worldwide with strict adherence to international standards of quality, food safety, and traceability." %}</p>
                    </div>
                </div>

                <div class="card shadow-sm mb-4" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(5px);">
                    <div class="card-body">
                        <h2 class="h4 mb-4">{% trans "Our Values" %}</h2>
                        <p>{% trans "What makes GB Farms special is its strict business ethics and integrity, which are deeply rooted in our core values." %}</p>
                        <p>{% trans "Our company-wide commitment to our values and corporate culture is evident at every stage of our operations, from understanding and meeting the different requirements of each customer to preserving our land, air, and water while protecting and developing our workforce and community." %}</p>
                    </div>
                </div>
            {% endif %}

            <div class="row mt-5">
                <div class="col-md-4 text-center">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="bi bi-shield-check display-4 text-success mb-3"></i>
                            <h3 class="h5">{% trans "Quality Assurance" %}</h3>
                            <p>{% trans "Strict adherence to international standards" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="bi bi-globe display-4 text-success mb-3"></i>
                            <h3 class="h5">{% trans "Global Reach" %}</h3>
                            <p>{% trans "Worldwide shipping and distribution" %}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <i class="bi bi-heart display-4 text-success mb-3"></i>
                            <h3 class="h5">{% trans "Sustainability" %}</h3>
                            <p>{% trans "Environmental and community responsibility" %}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Corporate Social Responsibility Section -->
            {% if corporate_social_info and corporate_social_info.is_active and corporate_social_info.title %}
            <div class="card shadow-sm mt-5" style="background: linear-gradient(135deg, #29ae29, #22a122); color: white;">
                <div class="card-body">
                    {% get_current_language as CURRENT_LANGUAGE %}
                    <div class="text-center mb-4">
                        <h1 class="display-4 font-weight-bold mb-3">
                            {% if CURRENT_LANGUAGE == 'ar' and corporate_social_info.title_ar %}
                                {{ corporate_social_info.title_ar }}
                            {% else %}
                                {% if corporate_social_info.title %}{{ corporate_social_info.title }}{% else %}CSR{% endif %}
                            {% endif %}
                        </h1>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6 mb-3">
                            <p class="h5 font-weight-bold">
                                {% if CURRENT_LANGUAGE == 'ar' and corporate_social_info.description_part1_ar %}
                                    {{ corporate_social_info.description_part1_ar }}
                                {% else %}
                                    {% if corporate_social_info.description_part1 %}{{ corporate_social_info.description_part1 }}{% else %}At GB Farms, we value the importance of giving back to the community. We focus on developing new talents by giving technical workshops and training to students in high schools.{% endif %}
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <p class="h5 font-weight-bold">
                                {% if CURRENT_LANGUAGE == 'ar' and corporate_social_info.description_part2_ar %}
                                    {{ corporate_social_info.description_part2_ar }}
                                {% else %}
                                    {% if corporate_social_info.description_part2 %}{{ corporate_social_info.description_part2 }}{% else %}and universities in the field and at the packhouse to prepare them for work in the agri field upon their graduation.{% endif %}
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    <div class="text-center">
                        {% if corporate_social_info.image %}
                            <img src="{{ corporate_social_info.image.url }}" alt="{{ corporate_social_info.title }} Image" class="img-fluid rounded shadow-lg" style="max-height: 400px; width: 100%; object-fit: cover;">
                        {% else %}
                            <img src="{% static 'images/flag/team.jpg' %}" alt="Team" class="img-fluid rounded shadow-lg" style="max-height: 400px; width: 100%; object-fit: cover;">
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            <div class="card shadow-sm mt-5">
                <div class="card-body text-center">
                    <h2 class="h4 mb-4">{% trans "Contact Us" %}</h2>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <i class="bi bi-telephone-fill text-success me-2"></i>
                                <strong>{% trans "Phone" %}:</strong>
                                <p class="mb-0">{% if about_info %}{{ about_info.phone }}{% else %}+20235392319{% endif %}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <i class="bi bi-envelope-fill text-success me-2"></i>
                                <strong>{% trans "Email" %}:</strong>
                                <p class="mb-0">{% if about_info %}{{ about_info.email }}{% else %}<EMAIL>{% endif %}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
main.container {
    background: rgba(255, 255, 255, 0.45);
    backdrop-filter: blur(3px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    margin: 2rem auto;
    padding: 2rem;
    position: relative;
    z-index: 1;
}

.card {
    transition: transform 0.3s ease;
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(3px);
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.95) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}
</style>
{% endblock %} 