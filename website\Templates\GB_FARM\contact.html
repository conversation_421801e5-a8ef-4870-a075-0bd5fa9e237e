{% extends 'GB_FARM/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Contact Us" %}{% endblock %}

{% block meta_description %}{% trans "Contact GB Farm for inquiries about our organic products, place special orders, or provide feedback. We're here to help!" %}{% endblock %}
{% block meta_keywords %}{% trans "contact, customer service, inquiry, feedback, organic farm contact" %}{% endblock %}

{% block content %}
<div class="container py-5" data-aos="fade-up">
    <h1 class="text-center mb-5">{% trans "Contact Us" %}</h1>
    
    <div class="row justify-content-center">
        <!-- Contact Information -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h3 class="card-title text-primary mb-4">{% trans "Get In Touch" %}</h3>
                    
                    {% if contact_info %}
                        <div class="mb-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="icon-wrapper me-3 rounded-circle bg-light p-3">
                                    <i class="fas fa-map-marker-alt text-primary"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">{% trans "Address" %}</h5>
                                    <p class="mb-0">{{ contact_info.address }}</p>
                                </div>
                            </div>
                            
                            <div class="d-flex align-items-center mb-3">
                                <div class="icon-wrapper me-3 rounded-circle bg-light p-3">
                                    <i class="fas fa-phone text-primary"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">{% trans "Phone" %}</h5>
                                    <p class="mb-0">{{ contact_info.phone }}</p>
                                </div>
                            </div>
                            
                            <div class="d-flex align-items-center mb-3">
                                <div class="icon-wrapper me-3 rounded-circle bg-light p-3">
                                    <i class="fas fa-envelope text-primary"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">{% trans "Email" %}</h5>
                                    <p class="mb-0">{{ contact_info.email }}</p>
                                </div>
                            </div>
                            
                            {% if contact_info.working_hours %}
                            <div class="d-flex align-items-center mb-3">
                                <div class="icon-wrapper me-3 rounded-circle bg-light p-3">
                                    <i class="fas fa-clock text-primary"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">{% trans "Working Hours" %}</h5>
                                    <p class="mb-0">{{ contact_info.working_hours }}</p>
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if contact_info.additional_info %}
                            <div class="mt-4">
                                <h5>{% trans "Additional Information" %}</h5>
                                <p>{{ contact_info.additional_info }}</p>
                            </div>
                            {% endif %}
                        </div>
                    {% else %}
                        <div class="mb-4">
                            <p>{% trans "Contact information coming soon." %}</p>
                        </div>
                    {% endif %}
                    
                    <div class="social-links mt-4">
                        <h5>{% trans "Follow Us" %}</h5>
                        <div class="d-flex mt-3">
                            {% if footer.facebook_link %}
                                <a href="{{ footer.facebook_link }}" target="_blank" class="me-3"><i class="fab fa-facebook-f fa-lg"></i></a>
                            {% endif %}
                            {% if footer.instagram_link %}
                                <a href="{{ footer.instagram_link }}" target="_blank" class="me-3"><i class="fab fa-instagram fa-lg"></i></a>
                            {% endif %}
                            {% if footer.twitter_link %}
                                <a href="{{ footer.twitter_link }}" target="_blank" class="me-3"><i class="fab fa-twitter fa-lg"></i></a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Google Maps -->
    <div class="mt-5" data-aos="fade-up">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-0">
                <div class="map-responsive">
                    {% if contact_info and contact_info.map_embed %}
                        {{ contact_info.map_embed|safe }}
                    {% else %}
                        {% if contact_info %}
                            <iframe 
                                src="https://maps.google.com/maps?q={{ contact_info.latitude }},{{ contact_info.longitude }}&z=15&output=embed" 
                                width="100%" 
                                height="450" 
                                style="border:0;" 
                                allowfullscreen="" 
                                loading="lazy" 
                                referrerpolicy="no-referrer-when-downgrade">
                            </iframe>
                        {% else %}
                            <iframe 
                                src="https://maps.google.com/maps?q=30.080062373661846,31.04347892147481&z=15&output=embed" 
                                width="100%" 
                                height="450" 
                                style="border:0;" 
                                allowfullscreen="" 
                                loading="lazy" 
                                referrerpolicy="no-referrer-when-downgrade">
                            </iframe>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .icon-wrapper {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .map-responsive {
        overflow: hidden;
        padding-bottom: 400px;
        position: relative;
        height: 0;
    }
    
    .map-responsive iframe {
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        position: absolute;
    }
    
    .social-links a {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        border-radius: 50%;
        transition: all 0.3s ease;
    }
    
    .social-links a:hover {
        background-color: var(--primary-color);
        color: white;
        transform: translateY(-3px);
    }
</style>
{% endblock %} 