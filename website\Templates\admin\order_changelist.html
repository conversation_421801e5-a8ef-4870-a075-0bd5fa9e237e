{% extends "admin/change_list.html" %}
{% load i18n admin_urls static admin_modify %}

{% block content %}
<div class="row mb-4">
    <!-- Statistics Cards -->
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h5 class="card-title">Total Orders</h5>
                <h2 class="card-text">{{ total_orders }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <h5 class="card-title">Total Revenue</h5>
                <h2 class="card-text">{{ total_revenue }} EGP</h2>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <h5 class="card-title">Orders by Status</h5>
                <div class="mt-2">
                    {% for status in orders_by_status %}
                    <div class="d-flex justify-content-between">
                        <span>{{ status.status }}</span>
                        <span>{{ status.count }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Orders -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Recent Orders</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Order ID</th>
                        <th>User</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in recent_orders %}
                    <tr>
                        <td>{{ order.id }}</td>
                        <td>{{ order.user.username }}</td>
                        <td>{{ order.created_at|date:"Y-m-d H:i" }}</td>
                        <td>
                            <span class="badge {% if order.status == 'delivered' %}bg-success
                                             {% elif order.status == 'pending' %}bg-warning
                                             {% elif order.status == 'cancelled' %}bg-secondary
                                             {% else %}bg-secondary{% endif %}">
                                {{ order.status }}
                            </span>
                        </td>
                        <td>{{ order.total_amount }} EGP</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Orders by Date Chart -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Orders by Date</h5>
    </div>
    <div class="card-body">
        <canvas id="ordersChart"></canvas>
    </div>
</div>

{{ block.super }}

{% block extrajs %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('ordersChart').getContext('2d');
        const ordersData = JSON.parse('{{ orders_by_date|safe }}');
        
        const chartData = {
            labels: ordersData.map(item => item.date),
            datasets: [{
                label: 'Number of Orders',
                data: ordersData.map(item => item.count),
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }]
        };
        
        new Chart(ctx, {
            type: 'line',
            data: chartData,
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });
</script>
{% endblock %}
{% endblock %} 