{% extends 'GB_FARM/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ product.name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Product Image -->
        <div class="col-md-6 mb-4" data-aos="fade-right">
            <div class="product-image-container position-relative card-glass">
                {% if product.video %}
                <video controls autoplay muted class="img-fluid rounded product-image fade-in" controlsList="nodownload">
                    <source src="{{ product.video.url }}#t=0.1" type="video/mp4">
                    {% trans "Your browser does not support the video tag." %}
                </video>
                {% elif product.image %}
                <img src="{{ product.image.url }}" 
                     class="img-fluid rounded product-image fade-in" 
                     alt="{{ product.name }}"
                     loading="lazy">
                {% else %}
                <div class="bg-light rounded product-image d-flex align-items-center justify-content-center">
                    <i class="fas fa-image text-muted fa-3x"></i>
                </div>
                {% endif %}
                {% if product.is_featured %}
                <span class="badge bg-warning position-absolute top-0 end-0 m-2">
                    <i class="fas fa-star"></i> {% trans "Featured" %}
                </span>
                {% endif %}
            </div>
        </div>

        <!-- Product Info -->
        <div class="col-md-6" data-aos="fade-left">
            <div class="card-glass p-4">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'GB_FARM:home' %}">{% trans "Home" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'GB_FARM:product_list' %}">{% trans "Products" %}</a></li>
                        {% if product.category %}
                        <li class="breadcrumb-item"><a href="{% url 'GB_FARM:category_products' product.category.id %}">{{ product.category.name }}</a></li>
                        {% endif %}
                        <li class="breadcrumb-item active" aria-current="page">{{ product.name }}</li>
                    </ol>
                </nav>

            <h1 class="mb-3">{{ product.name }}</h1>
            
            <!-- Category -->
            {% if product.category %}
            <div class="mb-3">
                <span class="badge bg-primary">
                    <i class="fas fa-tag"></i> {{ product.category.name }}
                </span>
            </div>
            {% endif %}
            
            <!-- Stock Status -->
            <div class="mb-3">
                {% if product.stock > 0 %}
                <span class="badge bg-success">
                    <i class="fas fa-check-circle"></i> {% trans "In Stock" %}
                </span>
                <small class="text-muted ms-2">{% trans "Only" %} {{ product.stock }} {% trans "left" %}</small>
                {% else %}
                <span class="badge bg-secondary">
                    <i class="fas fa-times-circle"></i> {% trans "Out of Stock" %}
                </span>
                {% endif %}
            </div>

                <!-- Price -->
                <div class="mb-4">
                    <h2 class="text-primary" id="product-price">{{ product.price }} {% trans "EGP" %} / {{ product.get_unit_display }}</h2>
                    {% if product.offer_set.exists %}
                    <small class="text-success">
                        <i class="fas fa-tag"></i> {% trans "Special Offer Available!" %}
                    </small>
                    {% endif %}
                </div>

                <!-- Description -->
                <div class="mb-4">
                    <h5>{% trans "Description" %}</h5>
                    <p class="text-muted">{{ product.description }}</p>
                </div>

                <!-- Actions -->
                <div class="d-grid gap-2">
                    {% if product.stock > 0 %}
                    <form action="{% url 'GB_FARM:add_to_cart' product.id %}" method="POST" class="add-to-cart-form">
                        {% csrf_token %}
                        {% if product.unit_category == 'FREE WEIGHT' %}
                        <input type="hidden" name="selected_weight" id="selected-weight-input" value="kg">
                        <input type="hidden" name="adjusted_price" id="adjusted-price-input" value="{{ product.price }}">
                        {% endif %}
                        
                        <!-- Quantity selector -->
                        <div class="quantity-selector mb-3">
                            <div class="input-group">
                                <button type="button" class="btn btn-outline-secondary quantity-decrease">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" name="quantity" id="quantity-input" value="1" min="1" max="{{ product.stock }}" 
                                       class="form-control text-center" readonly>
                                <button type="button" class="btn btn-outline-secondary quantity-increase">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg w-100 hover-scale">
                            <i class="fas fa-shopping-cart"></i> {% trans "Add to Cart" %}
                        </button>
                    </form>
                    {% endif %}

                    {% if user.is_authenticated %}
                        {% if product in user.wishlist.all %}
                        <form action="{% url 'GB_FARM:remove_from_wishlist' product.id %}" method="POST" class="wishlist-form">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-outline-danger w-100 hover-scale">
                                <i class="fas fa-heart-broken"></i> {% trans "Remove from Wishlist" %}
                            </button>
                        </form>
                        {% else %}
                        <form action="{% url 'GB_FARM:add_to_wishlist' product.id %}" method="POST" class="wishlist-form">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-outline-danger w-100 hover-scale">
                                <i class="fas fa-heart"></i> {% trans "Add to Wishlist" %}
                            </button>
                        </form>
                        {% endif %}

                        <!-- Weight selection dropdown - *Dynamically populated* -->
                        {% if product.unit_category == 'FREE WEIGHT' and product.available_units %}
                        <div class="mt-3 weight-selection">
                            <label for="weight-select" class="form-label">{% trans "Select Weight" %}</label>
                            <select id="weight-select" name="weight" class="form-select">
                                {# Loop through units selected by admin for this product #}
                                {% for unit_value, unit_display in product.get_available_units_tuples %}
                                    {# Determine price factor based on unit value #}
                                    {% with unit_str=unit_value|stringformat:"s" %}
                                        {% if unit_str == 'kg' %}
                                            {% with factor=1 %}
                                            <option value="{{ unit_value }}" data-price-factor="{{ factor }}">{{ unit_display }}</option>
                                            {% endwith %}
                                        {% elif unit_str == '500g' %}
                                            {% with factor=0.5 %}
                                            <option value="{{ unit_value }}" data-price-factor="{{ factor }}">{{ unit_display }}</option>
                                            {% endwith %}
                                        {% elif unit_str == '250g' %}
                                            {% with factor=0.25 %}
                                            <option value="{{ unit_value }}" data-price-factor="{{ factor }}">{{ unit_display }}</option>
                                            {% endwith %}
                                        {% else %} {# Default factor if not matched #}
                                            {% with factor=1 %}
                                            <option value="{{ unit_value }}" data-price-factor="{{ factor }}">{{ unit_display }}</option>
                                            {% endwith %}
                                        {% endif %}
                                    {% endwith %}
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}

                        <!-- Review Summary -->
                        <div class="mt-3 pt-3 border-top">
                            <div class="d-flex align-items-center">
                                <div class="me-2">
                                    {% if average_rating > 0 %}
                                        {% for i in "12345" %}
                                            {% with rating_value=average_rating|floatformat:1 %}
                                            {% if forloop.counter <= rating_value %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% elif forloop.counter <= rating_value|add:0.5 %}
                                                <i class="fas fa-star-half-alt text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star text-muted"></i>
                                            {% endif %}
                                            {% endwith %}
                                        {% endfor %}
                                        <span class="ms-1">({{ average_rating|floatformat:1 }})</span>
                                    {% else %}
                                        <span class="text-muted">{% trans "No reviews yet" %}</span>
                                    {% endif %}
                                </div>
                                {% if review_count > 0 %}
                                <a href="{% url 'GB_FARM:add_review' product.id %}" class="ms-auto text-decoration-none">
                                    {% trans "Read Reviews" %} ({{ review_count }})
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Reviews Section -->
    <div class="row mt-5" data-aos="fade-up">
        <div class="col-12">
            <div class="card-glass p-4">
                <h3>{% trans "Reviews" %}</h3>

                <!-- Review Form (Moved from reviews.html) -->
                {% if user.is_authenticated %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{% trans "Write or Update Your Review" %}</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="{% url 'GB_FARM:add_review' product.id %}">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label class="form-label">{% trans "Rating" %}</label>
                                <div class="rating">
                                    <!-- Assuming you have logic in views.py to pre-select the user's existing rating -->
                                    {% for i in "12345" %}
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="rating" id="rating{{ i }}" value="{{ i }}" required {% if user_review.rating == i|add:0 %}checked{% endif %}>
                                        <label class="form-check-label" for="rating{{ i }}">{{ i }} ⭐</label>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="comment" class="form-label">{% trans "Your Review" %}</label>
                                <textarea class="form-control" id="comment" name="comment" rows="4" required>{{ user_review.comment|default:"" }}</textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">{% trans "Submit Review" %}</button>
                        </form>
                    </div>
                </div>
                {% else %}
                <div class="alert alert-info mb-4">
                    {% trans "Please" %} <a href="{% url 'GB_FARM:loginpage' %}?next={{ request.path }}">{% trans "login" %}</a> {% trans "to write a review." %}
                </div>
                {% endif %}

                <!-- Display Existing Reviews -->
                <h4 class="mt-4 mb-3">{% trans "Customer Reviews" %}</h4>
                {% if reviews %}
                    {% for review in reviews %}
                    <div class="review-card mb-3 fade-in">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title">{{ review.user.username }}</h5>
                                <div class="text-warning">
                                    {% for i in "12345"|make_list %}
                                        {% if forloop.counter <= review.rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                            <p class="card-text">{{ review.comment }}</p>
                            <small class="text-muted">{{ review.created_at|date:"F d, Y" }}</small>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">{% trans "No reviews yet. Be the first to review this product!" %}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
/* Override main container styles for this page */
main.container {
    background: rgba(255, 255, 255, 0.45);
    backdrop-filter: blur(3px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    margin: 2rem auto;
    padding: 2rem;
    position: relative;
    z-index: 1;
}

/* Weight selection styling */
.weight-selection {
    background: rgba(255, 255, 255, 0.7);
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.weight-selection:hover {
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.weight-selection select {
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
    border-radius: 0.3rem;
    background-color: white;
    width: 100%;
    transition: all 0.2s ease;
}

.weight-selection select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

/* Card glass effect */
.card-glass {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(5px);
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.card-glass:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.95);
}

/* Product image container */
.product-image-container {
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    max-height: 500px;
}

.product-image {
    object-fit: cover;
    transition: transform 0.5s ease;
    width: 100%;
    max-height: 500px;
}

.product-image-container:hover .product-image {
    transform: scale(1.05);
}

/* Video-specific styles */
video.product-image {
    height: auto;
    display: block;
    background-color: #000;
}

/* Review cards */
.review-card {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(5px);
    border-radius: 0.8rem;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.review-card:hover {
    background: rgba(255, 255, 255, 0.85);
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Action buttons */
.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: translateY(-3px);
}

/* Fade-in animation */
.fade-in {
    opacity: 0;
    animation: fadeIn 0.5s forwards;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Breadcrumb styling */
.breadcrumb {
    background: transparent;
    margin-bottom: 1rem;
    padding: 0;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.2s ease;
}

.breadcrumb-item a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: var(--text-color);
    font-weight: 500;
}

/* Button enhancer */
.btn-outline-danger {
    border-width: 2px;
}

/* Quantity Selector Styling */
.quantity-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.quantity-selector .btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    border-radius: 0;
    padding: 0;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.quantity-selector .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1;
}

.quantity-selector .btn:hover::before {
    opacity: 0.1;
}

.quantity-selector .btn:active {
    transform: scale(0.95);
}

.quantity-selector .form-control {
    width: 80px;
    text-align: center;
    font-weight: 600;
    border-radius: 0;
    height: 40px;
    border-left: none;
    border-right: none;
}

/* Enhanced price display */
#product-price {
    position: relative;
    transition: all 0.3s ease;
}

#product-price small {
    opacity: 0.8;
    font-size: 0.85rem;
    font-weight: normal;
    transition: all 0.3s ease;
}

/* Weight selector enhancements */
.weight-selection select {
    height: 42px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
}

.weight-selection select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(46, 204, 113, 0.25);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quantity selector functionality
    const quantityInput = document.getElementById('quantity-input');
    const decreaseBtn = document.querySelector('.quantity-decrease');
    const increaseBtn = document.querySelector('.quantity-increase');
    const productPriceDisplay = document.getElementById('product-price');
    const adjustedPriceInput = document.getElementById('adjusted-price-input');
    const selectedWeightInput = document.getElementById('selected-weight-input');
    const weightSelect = document.getElementById('weight-select');
    
    // Get the base price from the displayed price text
    let basePrice = 0;
    let selectedUnit = 'kg';
    let priceFactor = 1;
    
    if (productPriceDisplay) {
        const priceParts = productPriceDisplay.innerText.split(' ');
        basePrice = parseFloat(priceParts[0]);
    }
    
    if (decreaseBtn && increaseBtn && quantityInput) {
        decreaseBtn.addEventListener('click', function() {
            let currentValue = parseInt(quantityInput.value);
            if (currentValue > 1) {
                showButtonLoading(this);
                quantityInput.value = currentValue - 1;
                updateFormValues();
            }
        });
        
        increaseBtn.addEventListener('click', function() {
            let currentValue = parseInt(quantityInput.value);
            let maxValue = parseInt(quantityInput.getAttribute('max'));
            if (currentValue < maxValue) {
                showButtonLoading(this);
                quantityInput.value = currentValue + 1;
                updateFormValues();
            }
        });
        
        quantityInput.addEventListener('change', function() {
            let currentValue = parseInt(quantityInput.value);
            let maxValue = parseInt(quantityInput.getAttribute('max'));
            
            if (isNaN(currentValue) || currentValue < 1) {
                quantityInput.value = 1;
            } else if (currentValue > maxValue) {
                quantityInput.value = maxValue;
            }
            
            updateFormValues();
        });
    }
    
    // Weight selection functionality
    if (weightSelect && selectedWeightInput) {
        weightSelect.addEventListener('change', function() {
            const selectedOption = weightSelect.options[weightSelect.selectedIndex];
            priceFactor = parseFloat(selectedOption.getAttribute('data-price-factor'));
            selectedUnit = selectedOption.text;
            selectedWeightInput.value = selectedOption.value;
            
            // Update price display and form values
            updateFormValues();
        });
        
        // Initialize on page load
        if (weightSelect.options.length > 0) {
            const selectedOption = weightSelect.options[weightSelect.selectedIndex];
            priceFactor = parseFloat(selectedOption.getAttribute('data-price-factor')) || 1;
            selectedUnit = selectedOption.text;
        }
    }
    
    function updateFormValues() {
        if (isNaN(basePrice)) return;
        
        // Get current quantity
        const quantity = parseInt(quantityInput.value) || 1;
        
        // Add loading effect to price display
        if (productPriceDisplay) {
            const originalContent = productPriceDisplay.innerHTML;
            const originalWidth = productPriceDisplay.offsetWidth;
            
            // Set minimum width to prevent layout shift
            productPriceDisplay.style.minWidth = originalWidth + 'px';
            
            // Show spinner next to price
            productPriceDisplay.innerHTML = '<div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>' + originalContent;
            
            setTimeout(() => {
                // Calculate adjusted price based on weight selection and quantity
                const adjustedPrice = basePrice * priceFactor;
                const totalPrice = adjustedPrice * quantity;
                
                // Update price display with correct unit
                productPriceDisplay.innerHTML = `${adjustedPrice.toFixed(2)} EGP / ${selectedUnit}`;
                productPriceDisplay.style.minWidth = '';
                
                // Add total if quantity > 1
                if (quantity > 1) {
                    productPriceDisplay.innerHTML += `<small class="ms-2">(${totalPrice.toFixed(2)} EGP total)</small>`;
                }
            }, 400);
        } else {
            // If price display isn't available, still update the hidden input
            const adjustedPrice = basePrice * priceFactor;
            
            // Update hidden input for the form submission
            if (adjustedPriceInput) {
                adjustedPriceInput.value = adjustedPrice.toFixed(2);
            }
        }
    }
    
    // Add loading indicator to quantity buttons
    const showButtonLoading = (button) => {
        // Save original content
        const originalContent = button.innerHTML;
        
        // Replace with spinner
        button.innerHTML = '<div class="spinner-border spinner-border-sm text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
        button.disabled = true;
        
        // Restore after a short delay
        setTimeout(() => {
            button.innerHTML = originalContent;
            button.disabled = false;
        }, 500);
    };
    
    // Initialize values on page load
    updateFormValues();
    
    // Image zoom or lightbox functionality could be added here
});
</script>
{% endblock %} 