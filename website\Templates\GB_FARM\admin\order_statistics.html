{% extends 'GB_FARM/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Order Statistics" %}{% endblock %}

{% block content %}
<div class="container py-4">
    <h1 class="mb-4">{% trans "Order Statistics" %}</h1>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Total Orders" %}</h5>
                    <h2 class="card-text">{{ total_orders }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Completed Orders" %}</h5>
                    <h2 class="card-text">{{ completed_orders }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Pending Orders" %}</h5>
                    <h2 class="card-text">{{ pending_orders }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Completion Rate" %}</h5>
                    <h2 class="card-text">{{ completion_rate|floatformat:1 }}%</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="row">
        <!-- Order Status Distribution -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Order Status Distribution" %}</h5>
                </div>
                <div class="card-body">
                    <canvas id="orderStatusChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Daily Orders Trend -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{% trans "Daily Orders Trend" %}</h5>
                </div>
                <div class="card-body">
                    <canvas id="orderTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Order Status Distribution Chart
    const statusCtx = document.getElementById('orderStatusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'pie',
        data: {
            labels: ['{% trans "Completed" %}', '{% trans "Pending" %}', '{% trans "Other" %}'],
            datasets: [{
                data: [
                    {{ completed_orders }},
                    {{ pending_orders }},
                    {{ total_orders|add:"-"|add:completed_orders|add:"-"|add:pending_orders }}
                ],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#6c757d'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Daily Orders Trend Chart (Sample data - replace with actual data)
    const trendCtx = document.getElementById('orderTrendChart').getContext('2d');
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: Array.from({length: 7}, (_, i) => {
                const d = new Date();
                d.setDate(d.getDate() - (6 - i));
                return d.toLocaleDateString();
            }),
            datasets: [{
                label: '{% trans "Orders" %}',
                data: [5, 8, 12, 7, 10, 15, 9], // Replace with actual data
                borderColor: '#007bff',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
{% endblock %} 