from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from django.db.models import Sum, Count
from django.db.models.functions import TruncDate
from .models import *

class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 1
    readonly_fields = ('subtotal',)

    def subtotal(self, obj):
        return f"{obj.quantity * obj.price} EGP"
    subtotal.short_description = 'Subtotal'

@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'created_at', 'status', 'total_amount', 'get_payment_status', 'get_order_items')
    list_filter = ('status', 'created_at')
    search_fields = ('user__username', 'user__email', 'id')
    readonly_fields = ('created_at', 'total_amount')
    inlines = [OrderItemInline]
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'
    change_list_template = 'admin/order_changelist.html'
    
    def get_order_items(self, obj):
        items = obj.orderitem_set.all()
        return format_html('<br>'.join([f"{item.product.name} x {item.quantity}" for item in items]))
    get_order_items.short_description = 'Order Items'

    def get_payment_status(self, obj):
        payment = obj.payments.first()
        if payment:
            return payment.get_status_display()
        return 'No Payment'
    get_payment_status.short_description = 'Payment Status'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')

    def changelist_view(self, request, extra_context=None):
        # Get statistics
        total_orders = Order.objects.count()
        total_revenue = Order.objects.filter(status='delivered').aggregate(total=Sum('total_amount'))['total'] or 0
        orders_by_status = Order.objects.values('status').annotate(count=Count('id'))
        orders_by_date = Order.objects.annotate(date=TruncDate('created_at')).values('date').annotate(count=Count('id')).order_by('date')
        
        # Get recent orders
        recent_orders = Order.objects.select_related('user').order_by('-created_at')[:5]
        
        extra_context = extra_context or {}
        extra_context.update({
            'total_orders': total_orders,
            'total_revenue': total_revenue,
            'orders_by_status': orders_by_status,
            'orders_by_date': orders_by_date,
            'recent_orders': recent_orders,
        })
        return super().changelist_view(request, extra_context=extra_context)

@admin.register(Cart)
class CartAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'product', 'quantity', 'added_at', 'get_total_price')
    list_filter = ('added_at', 'user')
    search_fields = ('user__username', 'product__name')
    readonly_fields = ('added_at',)
    ordering = ('-added_at',)
    
    def get_total_price(self, obj):
        return f"{obj.total_price} EGP"
    get_total_price.short_description = 'Total Price'

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'name_ar', 'slug', 'created_at', 'updated_at')
    prepopulated_fields = {'slug': ('name',)}
    search_fields = ('name', 'name_ar', 'description', 'description_ar')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'name_ar', 'slug', 'image')
        }),
        ('Description', {
            'fields': ('description', 'description_ar'),
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'name_ar', 'get_category', 'price', 'stock', 'status', 'is_featured')
    list_filter = ('category', 'status', 'is_featured')
    search_fields = ('name', 'name_ar', 'description', 'description_ar', 'category__name')
    readonly_fields = ('created_at', 'updated_at')
    autocomplete_fields = ['category']
    list_select_related = ['category']
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'name_ar', 'category', 'price', 'stock', 'unit_category', 'available_units', 'status', 'is_featured')
        }),
        ('Media', {
            'fields': ('image', 'video', 'thumbnail'),
        }),
        ('Descriptions', {
            'fields': ('description', 'description_ar'),
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def get_category(self, obj):
        if obj.category:
            return format_html('<a href="/admin/website/category/{}/change/">{}</a>', 
                             obj.category.id, obj.category.name)
        return '-'
    get_category.admin_order_field = 'category__name'
    get_category.short_description = 'Category'

@admin.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ('username', 'email', 'user_type', 'is_staff', 'is_active')
    list_filter = ('is_staff', 'is_active', 'user_type')
    search_fields = ('username', 'email')
    ordering = ('username',)
    readonly_fields = ('date_joined', 'last_login')

    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'email', 'user_phone')}),
        ('Permissions', {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'user_type',
                       'groups', 'user_permissions'),
        }),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
    )
    
    filter_horizontal = ('groups', 'user_permissions',)

@admin.register(Offer)
class OfferAdmin(admin.ModelAdmin):
    list_display = ('product', 'discount_percentage', 'created_at', 'is_active')
    list_filter = ('is_active', 'created_at')
    search_fields = ('product__name', 'description', 'description_ar')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('product', 'discount_percentage', 'is_active', 'is_featured')
        }),
        ('Descriptions', {
            'fields': ('description', 'description_ar'),
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(WebsiteVideo)
class WebsiteVideoAdmin(admin.ModelAdmin):
    list_display = ('title', 'title_ar', 'is_active', 'opacity', 'created_at', 'preview_video')
    list_filter = ('is_active', 'created_at')
    search_fields = ('title', 'title_ar')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'title_ar', 'video', 'is_active')
        }),
        ('Video Settings', {
            'fields': ('opacity',),
            'description': 'Set the opacity of the video overlay (0.0 to 1.0)'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def preview_video(self, obj):
        if obj.video:
            return format_html(
                '<video width="200" controls><source src="{}" type="video/mp4">Your browser does not support the video tag.</video>',
                obj.video.url
            )
        return "No video"
    preview_video.short_description = 'Preview'

    def save_model(self, request, obj, form, change):
        # Ensure opacity is between 0 and 1
        if obj.opacity < 0:
            obj.opacity = 0
        elif obj.opacity > 1:
            obj.opacity = 1
        super().save_model(request, obj, form, change)

@admin.register(Wishlist)
class WishlistAdmin(admin.ModelAdmin):
    list_display = ('user', 'product', 'added_at')
    list_filter = ('added_at',)
    search_fields = ('user__username', 'product__name')
    readonly_fields = ('added_at',)

@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('user', 'product', 'rating', 'created_at')
    list_filter = ('rating', 'created_at')
    search_fields = ('user__username', 'product__name', 'comment', 'comment_ar')
    readonly_fields = ('created_at',)
    fieldsets = (
        ('Review Information', {
            'fields': ('user', 'product', 'rating')
        }),
        ('Comments', {
            'fields': ('comment', 'comment_ar'),
        }),
        ('Timestamps', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )

@admin.register(Stock)
class StockAdmin(admin.ModelAdmin):
    list_display = ('product', 'quantity', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('product__name',)
    readonly_fields = ('created_at',)

@admin.register(APIToken)
class APITokenAdmin(admin.ModelAdmin):
    list_display = ('user', 'name', 'created_at', 'last_used_at', 'expires_at', 'is_active')
    list_filter = ('is_active', 'created_at')
    search_fields = ('user__username', 'name')
    readonly_fields = ('token', 'created_at', 'last_used_at')

@admin.register(CarouselImage)
class CarouselImageAdmin(admin.ModelAdmin):
    list_display = ('title', 'title_ar', 'order', 'is_active', 'created_at', 'updated_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('title', 'title_ar', 'description', 'description_ar')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('order',)
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'title_ar', 'description', 'description_ar', 'image', 'button_text', 'button_text_ar', 'button_link', 'order', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(OrderMessage)
class OrderMessageAdmin(admin.ModelAdmin):
    list_display = ('order', 'sender', 'is_read', 'created_at')
    list_filter = ('is_read', 'created_at')
    search_fields = ('order__id', 'sender__username', 'message', 'message_ar')
    readonly_fields = ('created_at',)
    fieldsets = (
        ('Message Details', {
            'fields': ('order', 'sender', 'message', 'message_ar', 'is_read')
        }),
        ('Timestamps', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )

@admin.register(Footer)
class FooterAdmin(admin.ModelAdmin):
    list_display = ('address', 'address_ar', 'phone', 'email', 'updated_at')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Content', {
            'fields': ('address', 'address_ar', 'phone', 'email', 'facebook_link', 'instagram_link', 'twitter_link')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def has_add_permission(self, request):
        # Check if any footer content exists
        return not Footer.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion of the footer content
        return False

@admin.register(ExternalFooter)
class ExternalFooterAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'phone', 'email')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Content', {
            'fields': ('address', 'address_ar', 'phone', 'email', 'copyright_text', 'facebook_url', 'instagram_url', 'youtube_url')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def has_add_permission(self, request):
        # Allow only one instance to be added
        return not ExternalFooter.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion
        return False

@admin.register(Zone)
class ZoneAdmin(admin.ModelAdmin):
    list_display = ('name', 'name_ar', 'price', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'name_ar')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'name_ar', 'price', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(Delivery)
class DeliveryAdmin(admin.ModelAdmin):
    list_display = ('name', 'name_ar', 'delivery_type', 'is_active', 'created_at')
    list_filter = ('delivery_type', 'is_active', 'created_at')
    search_fields = ('name', 'name_ar')
    filter_horizontal = ('zones',)
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'name_ar', 'delivery_type', 'is_active')
        }),
        ('Zones', {
            'fields': ('zones',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(ContactInfo)
class ContactInfoAdmin(admin.ModelAdmin):
    list_display = ('title', 'title_ar', 'phone', 'email', 'is_active', 'updated_at')
    list_filter = ('is_active',)
    search_fields = ('title', 'title_ar', 'address', 'address_ar', 'phone', 'email')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'title_ar', 'is_active')
        }),
        ('Contact Details', {
            'fields': ('address', 'address_ar', 'phone', 'email')
        }),
        ('Working Hours', {
            'fields': ('working_hours', 'working_hours_ar')
        }),
        ('Map & Additional Info', {
            'fields': ('latitude', 'longitude', 'map_embed', 'additional_info', 'additional_info_ar')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(InvoiceLog)
class InvoiceLogAdmin(admin.ModelAdmin):
    list_display = ('invoice_number', 'order_link', 'generated_by_user', 'created_at', 'last_accessed', 'access_count', 'total_amount')
    list_filter = ('created_at', 'last_accessed', 'access_count')
    search_fields = ('invoice_number', 'order__id', 'generated_by__username', 'ip_address')
    readonly_fields = ('created_at', 'last_accessed', 'ip_address', 'user_agent')
    date_hierarchy = 'created_at'
    
    def order_link(self, obj):
        if obj.order:
            return format_html(f'<a href="/admin/website/order/{obj.order.id}/change/">{obj.order.id}</a>')
        return "-"
    order_link.short_description = 'Order'

    def generated_by_user(self, obj):
        if obj.generated_by:
            return format_html(f'<a href="/admin/auth/user/{obj.generated_by.id}/change/">{obj.generated_by.username}</a>')
        return "System"
    generated_by_user.short_description = 'Generated By'

@admin.register(About)
class AboutAdmin(admin.ModelAdmin):
    list_display = ('title', 'is_active', 'updated_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('title', 'title_ar', 'story_content', 'operations_content', 'values_content')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'title_ar', 'is_active')
        }),
        ('Story Section', {
            'fields': ('story_title', 'story_title_ar', 'story_content', 'story_content_ar')
        }),
        ('Operations Section', {
            'fields': ('operations_title', 'operations_title_ar', 'operations_content', 'operations_content_ar')
        }),
        ('Values Section', {
            'fields': ('values_title', 'values_title_ar', 'values_content', 'values_content_ar')
        }),
        ('Contact Info (for About Page)', {
            'fields': ('phone', 'email')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def has_add_permission(self, request):
        # Only allow creating one about page content
        return not About.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion of the about content
        return False

@admin.register(Career)
class CareerAdmin(admin.ModelAdmin):
    list_display = ('title', 'department', 'location', 'job_type', 'experience_level', 'is_active', 'created_at')
    list_filter = ('job_type', 'experience_level', 'is_active', 'created_at')
    search_fields = ('title', 'title_ar', 'department', 'department_ar', 'location', 'location_ar', 'description', 'requirements')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'title_ar', 'department', 'department_ar', 'location', 'location_ar', 'job_type', 'experience_level', 'is_active')
        }),
        ('Details', {
            'fields': ('description', 'description_ar', 'requirements', 'requirements_ar', 'responsibilities', 'responsibilities_ar', 'benefits', 'benefits_ar')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(Configuration)
class ConfigurationAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'created_at', 'updated_on')
    readonly_fields = ('created_at', 'updated_on')
    fieldsets = (
        ('Header Descriptions', {
            'fields': ('leasing_short_description', 'factoring_short_description')
        }),
        ('Company Overview', {
            'fields': ('company_overview_description', 'company_overview_image1', 'company_overview_image2')
        }),
        ('Governance', {
            'fields': ('governance_description',)
        }),
        ('About Us', {
            'fields': ('about_us_short_description',)
        }),
        ('Contact Information', {
            'fields': ('linkedin_url', 'emails')
        }),
        ('SEO Information (Home Page)', {
            'fields': ('home_SEO_description', 'home_SEO_key_words')
        }),
        ('SEO Information (About Page)', {
            'fields': ('about_SEO_description', 'about_SEO_key_words')
        }),
        ('SEO Information (Contact Page)', {
            'fields': ('contact_SEO_description', 'contact_SEO_key_words')
        }),
        ('SEO Information (News Page)', {
            'fields': ('news_SEO_description', 'news_SEO_key_words')
        }),
        ('SEO Information (Careers Page)', {
            'fields': ('careers_SEO_description', 'careers_SEO_key_words')
        }),
        ('SEO Information (Leasing Benefits Page)', {
            'fields': ('leasing_benefits_SEO_description', 'leasing_benefits_SEO_key_words')
        }),
        ('SEO Information (Factoring Benefits Page)', {
            'fields': ('factoring_benefits_SEO_description', 'factoring_benefits_SEO_key_words')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_on'),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        # Allow only one instance to be added
        return not Configuration.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion
        return False

@admin.register(ContactUsMessage)
class ContactUsMessageAdmin(admin.ModelAdmin):
    list_display = ('name', 'email', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('name', 'email', 'message')
    readonly_fields = ('created_at',)

@admin.register(ProdCat)
class ProdCatAdmin(admin.ModelAdmin):
    list_display = ('name', 'get_month_display', 'date_from', 'date_to', 'created_at', 'updated_at')
    list_filter = ('month', 'date_from', 'date_to', 'created_at')
    search_fields = ('name',)
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'date_from'
    ordering = ('month', 'date_from', 'name')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'month', 'date_from', 'date_to')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def get_month_display(self, obj):
        return dict(ProdCat.MONTH_CHOICES).get(obj.month, "")
    get_month_display.short_description = 'Month'

    def get_queryset(self, request):
        return super().get_queryset(request).order_by('month', 'date_from')

@admin.register(CountryFlag)
class CountryFlagAdmin(admin.ModelAdmin):
    list_display = ('country_name', 'continent', 'flag_image', 'created_at', 'updated_at')
    list_filter = ('continent',)
    search_fields = ('country_name',)
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('country_name', 'continent', 'flag_image')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(AboutUsVideo)
class AboutUsVideoAdmin(admin.ModelAdmin):
    list_display = ('__str__', 'title', 'is_active', 'created_at', 'updated_at')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'title_ar', 'description', 'description_ar', 'video', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def has_add_permission(self, request):
        # Allow only one instance to be added (Singleton pattern)
        return not AboutUsVideo.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion of the About Us Video content
        return False

@admin.register(GrapeVariety)
class GrapeVarietyAdmin(admin.ModelAdmin):
    list_display = ('name', 'color', 'date_range_display', 'weight_range_display', 'order', 'is_active', 'created_at')
    list_filter = ('color', 'is_active', 'weight_unit', 'created_at')
    search_fields = ('name',)
    readonly_fields = ('created_at', 'updated_at', 'date_range_display', 'weight_range_display')
    ordering = ('color', 'order', 'name')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'color', 'order', 'is_active')
        }),
        ('Date Range', {
            'fields': ('date_from_day', 'date_from_month', 'date_to_day', 'date_to_month', 'date_range_display')
        }),
        ('Weight Information', {
            'fields': ('weight_from', 'weight_to', 'weight_unit', 'weight_range_display')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(MangoVariety)
class MangoVarietyAdmin(admin.ModelAdmin):
    list_display = ('name', 'variety_type', 'date_range_display', 'weight_range_display', 'order', 'is_active', 'created_at')
    list_filter = ('variety_type', 'is_active', 'weight_unit', 'created_at')
    search_fields = ('name',)
    readonly_fields = ('created_at', 'updated_at', 'date_range_display', 'weight_range_display')
    ordering = ('variety_type', 'order', 'name')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'variety_type', 'order', 'is_active')
        }),
        ('Date Range', {
            'fields': ('date_from_day', 'date_from_month', 'date_to_day', 'date_to_month', 'date_range_display')
        }),
        ('Weight Information', {
            'fields': ('weight_from', 'weight_to', 'weight_unit', 'weight_range_display')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(OtherProduct)
class OtherProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'date_range_display', 'weight_range_display', 'order', 'is_active', 'created_at')
    list_filter = ('is_active', 'weight_unit', 'created_at')
    search_fields = ('name',)
    readonly_fields = ('created_at', 'updated_at', 'date_range_display', 'weight_range_display')
    ordering = ('order', 'name')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'order', 'is_active')
        }),
        ('Date Range', {
            'fields': ('date_from_day', 'date_from_month', 'date_to_day', 'date_to_month', 'date_range_display')
        }),
        ('Weight Information', {
            'fields': ('weight_from', 'weight_to', 'weight_unit', 'weight_range_display')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(CareerApplication)
class CareerApplicationAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'email', 'career', 'years_of_experience', 'is_processed', 'created_at')
    list_filter = ('is_processed', 'career', 'years_of_experience', 'created_at')
    search_fields = ('full_name', 'email', 'career__title', 'current_position')
    readonly_fields = ('created_at', 'updated_at', 'ip_address')
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('Application Details', {
            'fields': ('career', 'full_name', 'email', 'phone_number', 'cv_file', 'cover_letter', 'years_of_experience', 'current_position', 'linkedin_profile', 'portfolio_website', 'salary_expectation', 'available_start_date')
        }),
        ('Admin Processing', {
            'fields': ('is_processed', 'notes')
        }),
        ('Metadata', {
            'fields': ('ip_address', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('career')

@admin.register(ExternalContactSubmission)
class ExternalContactSubmissionAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'email', 'company_name', 'business_type', 'is_processed', 'created_at')
    list_filter = ('is_processed', 'business_type', 'find_out_method', 'created_at')
    search_fields = ('full_name', 'email', 'company_name', 'phone_number', 'subject', 'message')
    readonly_fields = ('full_name', 'email', 'phone_number', 'company_name', 'business_type', 'find_out_method', 'subject', 'message', 'created_at')
    date_hierarchy = 'created_at'
    ordering = ['-created_at']

    fieldsets = (
        ('Contact Information', {
            'fields': ('full_name', 'email', 'phone_number', 'company_name'),
            'description': 'Basic contact details provided by the user'
        }),
        ('Business Details', {
            'fields': ('business_type', 'find_out_method', 'subject'),
            'description': 'Business-related information'
        }),
        ('Message', {
            'fields': ('message',),
            'description': 'User message content'
        }),
        ('Processing', {
            'fields': ('is_processed',),
            'description': 'Processing status'
        }),
        ('Metadata', {
            'fields': ('created_at',),
            'classes': ('collapse',),
            'description': 'Technical information'
        })
    )

    actions = ['mark_as_processed', 'mark_as_unprocessed']

    def has_add_permission(self, request):
        """Disable adding new submissions through admin"""
        return False

    def has_change_permission(self, request, obj=None):
        """Allow viewing but restrict editing to only is_processed field"""
        return True

    def has_delete_permission(self, request, obj=None):
        """Disable deleting submissions"""
        return False

    def get_readonly_fields(self, request, obj=None):
        """Make all fields readonly except is_processed"""
        if obj:  # editing an existing object
            return ('full_name', 'email', 'phone_number', 'company_name', 'business_type', 'find_out_method', 'subject', 'message', 'created_at')
        return self.readonly_fields

    def mark_as_processed(self, request, queryset):
        queryset.update(is_processed=True)
        self.message_user(request, f"{queryset.count()} submissions marked as processed.")
    mark_as_processed.short_description = "Mark selected submissions as processed"

    def mark_as_unprocessed(self, request, queryset):
        queryset.update(is_processed=False)
        self.message_user(request, f"{queryset.count()} submissions marked as unprocessed.")
    mark_as_unprocessed.short_description = "Mark selected submissions as unprocessed"

@admin.register(NewsletterSubscription)
class NewsletterSubscriptionAdmin(admin.ModelAdmin):
    list_display = ('email', 'source_page', 'is_active', 'created_at')
    list_filter = ('is_active', 'source_page', 'created_at')
    search_fields = ('email',)
    readonly_fields = ('created_at',)
    date_hierarchy = 'created_at'
    ordering = ['-created_at']

    fieldsets = (
        ('Subscription Details', {
            'fields': ('email', 'source_page', 'is_active')
        }),
        ('Metadata', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )
    
    actions = ['activate_subscriptions', 'deactivate_subscriptions']

    def activate_subscriptions(self, request, queryset):
        queryset.update(is_active=True)
        self.message_user(request, f"{queryset.count()} subscriptions activated.")
    activate_subscriptions.short_description = "Activate selected subscriptions"

    def deactivate_subscriptions(self, request, queryset):
        queryset.update(is_active=False)
        self.message_user(request, f"{queryset.count()} subscriptions deactivated.")
    deactivate_subscriptions.short_description = "Deactivate selected subscriptions"

@admin.register(Branch)
class BranchAdmin(admin.ModelAdmin):
    list_display = ('name', 'name_ar', 'address', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'name_ar', 'address', 'address_ar')
    readonly_fields = ('created_at', 'updated_at', 'coordinates_display', 'google_maps_url')
    ordering = ['name']
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'name_ar', 'is_active')
        }),
        ('Location Information', {
            'fields': ('address', 'address_ar', 'latitude', 'longitude', 'coordinates_display', 'google_maps_url')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    @admin.display(description='Coordinates')
    def coordinates_display(self, obj):
        return f"{obj.latitude}, {obj.longitude}"

    @admin.display(description='Google Maps')
    def google_maps_url(self, obj):
        return format_html(f'<a href="https://www.google.com/maps?q={obj.latitude},{obj.longitude}" target="_blank">View on Map</a>')

@admin.register(ProductSeason)
class ProductSeasonAdmin(admin.ModelAdmin):
    list_display = ('name', 'start_month', 'end_month', 'is_active', 'order')
    list_filter = ('is_active', 'start_month', 'end_month')
    search_fields = ('name', 'name_ar', 'description', 'description_ar')
    ordering = ('order', 'start_month', 'name')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'name_ar', 'is_active', 'order')
        }),
        ('Season Details', {
            'fields': ('start_month', 'end_month', 'description', 'description_ar')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(CorporateSocial)
class CorporateSocialAdmin(admin.ModelAdmin):
    list_display = ('title', 'is_active', 'created_at', 'updated_at')
    list_filter = ('is_active',)
    search_fields = ('title', 'title_ar', 'description_part1', 'description_part2')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'title_ar', 'is_active')
        }),
        ('Content', {
            'fields': ('description_part1', 'description_part1_ar', 'description_part2', 'description_part2_ar')
        }),
        ('Image', {
            'fields': ('image',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def has_add_permission(self, request):
        return not CorporateSocial.objects.exists()

    def has_delete_permission(self, request, obj=None):
        return False

# Re-enabled ExVarietyInline, ExProductInline, ExCategoryAdmin, ExProductAdmin, ExVarietyAdmin

from django.contrib import admin
from django import forms
from django.utils.safestring import mark_safe
from .models import NewCatGroup, NewCatGroupDescription, NewProduct, NewVariety


class ColorWidget(forms.TextInput):
    """Simple and working color picker widget"""

    def render(self, name, value, attrs=None, renderer=None):
        if value is None:
            value = '#ef4444'

        # Generate unique ID
        field_id = f"id_{name}"
        color_id = f"color_{name}"

        # Preset colors
        presets = ['#ef4444', '#f59e0b', '#22c55e', '#3b82f6', '#8b5cf6', '#ec4899', '#6b7280', '#000000']

        preset_html = ''.join([
            f'<span style="display:inline-block;width:20px;height:20px;background:{color};border:1px solid #333;margin:2px;cursor:pointer;" onclick="document.getElementById(\'{field_id}\').value=\'{color}\';document.getElementById(\'{color_id}\').value=\'{color}\';document.getElementById(\'preview_{name}\').style.backgroundColor=\'{color}\';"></span>'
            for color in presets
        ])

        return mark_safe(f'''
            <div style="display:flex;gap:10px;align-items:center;padding:8px;border:1px solid #ddd;border-radius:4px;background:#f9f9f9;">
                <div>
                    <strong>Color:</strong><br>
                    <input type="color" id="{color_id}" value="{value}" style="width:40px;height:30px;border:none;cursor:pointer;"
                           onchange="document.getElementById('{field_id}').value=this.value;document.getElementById('preview_{name}').style.backgroundColor=this.value;">
                </div>
                <div>
                    <strong>Hex:</strong><br>
                    <input type="text" name="{name}" id="{field_id}" value="{value}" maxlength="7"
                           style="width:80px;padding:4px;font-family:monospace;" placeholder="#ef4444"
                           oninput="if(this.value.match(/^#[0-9A-F]{{6}}$/i)){{document.getElementById('{color_id}').value=this.value;document.getElementById('preview_{name}').style.backgroundColor=this.value;}}">
                </div>
                <div>
                    <strong>Preview:</strong><br>
                    <div id="preview_{name}" style="width:30px;height:30px;border:1px solid #333;background:{value};"></div>
                </div>
                <div>
                    <strong>Quick:</strong><br>
                    {preset_html}
                </div>
            </div>
        ''')



# Custom Forms with Color Picker
class NewCatGroupForm(forms.ModelForm):
    class Meta:
        model = NewCatGroup
        fields = '__all__'
        widgets = {
            'title_color': ColorWidget(),
            'underline_color': ColorWidget(),
            'table_title_color': ColorWidget(),
        }


class NewCatGroupDescriptionForm(forms.ModelForm):
    class Meta:
        model = NewCatGroupDescription
        fields = '__all__'
        widgets = {
            'color': ColorWidget(),
        }


class NewProductForm(forms.ModelForm):
    class Meta:
        model = NewProduct
        fields = '__all__'
        widgets = {
            'color': ColorWidget(),
        }


class NewVarietyInline(admin.TabularInline):
    model = NewVariety
    extra = 1


class NewProductInline(admin.TabularInline):
    model = NewProduct
    form = NewProductForm
    extra = 1


class NewCatGroupDescriptionInline(admin.TabularInline):
    model = NewCatGroupDescription
    form = NewCatGroupDescriptionForm
    extra = 1


@admin.register(NewVariety)
class NewVarietyAdmin(admin.ModelAdmin):
    list_display = ("name", "product", "start_date", "end_date", "weight_from", "weight_to")
    list_filter = ("product",)


@admin.register(NewProduct)
class NewProductAdmin(admin.ModelAdmin):
    form = NewProductForm
    inlines = [NewVarietyInline]
    list_display = ("name", "group", "start_date", "end_date", "weight_from", "weight_to", "color")
    list_filter = ("group",)


@admin.register(NewCatGroup)
class NewCatGroupAdmin(admin.ModelAdmin):
    form = NewCatGroupForm
    inlines = [NewCatGroupDescriptionInline, NewProductInline]
    list_display = ("get_title_display", "table_title", "order", "title_color", "underline_color", "table_title_color")
    list_editable = ("order",)
    ordering = ("order", "title")
    fields = ("title", "table_title", "main_image", "title_color", "underline_color", "table_title_color", "order")

    def get_title_display(self, obj):
        return obj.title or "(No Title)"
    get_title_display.short_description = "Group Title"


@admin.register(NewCatGroupDescription)
class NewCatGroupDescriptionAdmin(admin.ModelAdmin):
    form = NewCatGroupDescriptionForm
    list_display = ("group", "text", "color")
    list_filter = ("group",)

@admin.register(GroupItem)
class GroupItemAdmin(admin.ModelAdmin):
    list_display = ("group", "image", "is_active")
    list_filter = ("group", "is_active")