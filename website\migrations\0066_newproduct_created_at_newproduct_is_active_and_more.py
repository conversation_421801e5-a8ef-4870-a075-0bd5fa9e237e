# Generated by Django 4.2.20 on 2025-07-21 05:28

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0065_newcatgroup_newproduct_newvariety_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='newproduct',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='newproduct',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='newproduct',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
    ]
