{% extends "Extrnal.html/EX.base.html" %}
{% load static %}

{% block head %}
    <title>Our Products - GB Farms</title>
    <style>
        .products-hero-bg {
            background-image: url("{% static 'images/prodect mango/close-up-orange-tree-with-many-leaves.jpg' %}");
            background-size: cover;
            background-position: center;
        }
    </style>
{% endblock %}

{% block header %}
    {% include "Extrnal.html/navebar.html" %}
{% endblock %}

{% block content %}
    <!-- Main Section - Products Page -->
    <main class="relative h-[80vh] md:h-[90vh] w-full flex items-center justify-start products-hero-bg">
        <div class="pl-4 pt-20 md:pl-20 lg:pl-40 md:pt-24 max-w-5xl z-10">
            <h1 class="text-white text-4xl md:text-6xl font-bold mb-4">Our Products</h1>
            <p class="text-white text-lg md:text-xl mb-6">Discover our premium selection of fresh fruits and agricultural products</p>
        </div>
    </main>

    <!-- Products Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            {% for category in categories %}
                <div class="mb-16">
                    <!-- Category Header -->
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">{{ category.name }}</h2>
                        {% if category.description1 %}
                            <p class="text-gray-600 text-lg max-w-3xl mx-auto">{{ category.description1 }}</p>
                        {% endif %}
                    </div>

                    <!-- Products Grid -->
                    {% if category.products.all %}
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            {% for product in category.products.all %}
                                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                                    {% if product.img %}
                                        <img src="{{ product.img.url }}" alt="{{ product.title }}" class="w-full h-48 object-cover">
                                    {% else %}
                                        <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                            <span class="text-gray-500">No Image</span>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="p-6">
                                        <h3 class="text-xl font-bold text-gray-800 mb-3">{{ product.title }}</h3>
                                        
                                        <!-- Varieties -->
                                        {% if product.varieties.all %}
                                            <div class="mb-4">
                                                <h4 class="text-sm font-semibold text-gray-600 mb-2">Available Varieties:</h4>
                                                <div class="flex flex-wrap gap-2">
                                                    {% for variety in product.varieties.all %}
                                                        <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                                                            {{ variety.name }}
                                                        </span>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-12">
                            <p class="text-gray-500 text-lg">No products available in this category.</p>
                        </div>
                    {% endif %}
                </div>
            {% empty %}
                <div class="text-center py-16">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">No Categories Available</h2>
                    <p class="text-gray-600">Please check back later for our product catalog.</p>
                </div>
            {% endfor %}
        </div>
    </section>
{% endblock %}

{% block footer %}
    {% include "Extrnal.html/footer.html" %}
{% endblock %}