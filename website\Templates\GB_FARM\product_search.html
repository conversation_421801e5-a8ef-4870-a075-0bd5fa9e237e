{% extends 'GB_FARM/base.html' %}

{% block title %}Search Products{% endblock %}

{% block extra_css %}
<style>
    .filter-sidebar {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .product-card {
        transition: transform 0.3s ease;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
    }
    
    .product-image {
        height: 200px;
        object-fit: cover;
    }
    
    .price-range {
        display: flex;
        gap: 10px;
        align-items: center;
    }
    
    .price-range input {
        width: 100px;
    }
    
    .sort-select {
        max-width: 200px;
    }

    .category-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(255, 255, 255, 0.9);
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        z-index: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <!-- Filters Sidebar -->
        <div class="col-md-3">
            <div class="filter-sidebar">
                <h4 class="mb-4">Filters</h4>
                <form method="get" action="{% url 'GB_FARM:product_search' %}" id="filter-form">
                    <!-- Search Query -->
                    <div class="mb-3">
                        <label for="q" class="w-40 h-40 rounded-lg">Search</label>
                        <input type="text" class="form-control" id="q" name="q" value="{{ query }}" placeholder="Search products...">
                    </div>
                    
                    <!-- Category Filter -->
                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">All Categories</option>
                            {% for cat in categories %}
                            <option value="{{ cat.slug }}" {% if category == cat.slug %}selected{% endif %}>
                                {{ cat.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Price Range -->
                    <div class="mb-3">
                        <label class="form-label">Price Range</label>
                        <div class="price-range">
                            <input type="number" class="form-control" name="min_price" value="{{ min_price }}" placeholder="Min">
                            <span>to</span>
                            <input type="number" class="form-control" name="max_price" value="{{ max_price }}" placeholder="Max">
                        </div>
                    </div>
                    
                    <!-- Status Filter -->
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="available" {% if status == 'available' %}selected{% endif %}>Available</option>
                            <option value="unavailable" {% if status == 'unavailable' %}selected{% endif %}>Unavailable</option>
                        </select>
                    </div>
                    
                    <!-- Sort Options -->
                    <div class="mb-3">
                        <label for="sort" class="form-label">Sort By</label>
                        <select class="form-select sort-select" id="sort" name="sort">
                            <option value="">Newest First</option>
                            <option value="price_asc" {% if sort_by == 'price_asc' %}selected{% endif %}>Price: Low to High</option>
                            <option value="price_desc" {% if sort_by == 'price_desc' %}selected{% endif %}>Price: High to Low</option>
                            <option value="name_asc" {% if sort_by == 'name_asc' %}selected{% endif %}>Name: A to Z</option>
                            <option value="name_desc" {% if sort_by == 'name_desc' %}selected{% endif %}>Name: Z to A</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                </form>
            </div>
        </div>
        
        <!-- Products Grid -->
        <div class="col-md-9">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Search Results</h2>
                <div class="text-muted">
                    {{ products.count }} products found
                </div>
            </div>
            
            {% if products %}
            <div class="row row-cols-1 row-cols-md-3 g-4">
                {% for product in products %}
                <div class="col">
                    <div class="card h-100 shadow-sm product-card">
                        <div class="position-relative">
                            {% if product.image %}
                            <img src="{{ product.image.url }}" class="card-img-top product-image" alt="{{ product.name }}">
                            {% else %}
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center product-image">
                                <i class="bi bi-image text-muted fs-1"></i>
                            </div>
                            {% endif %}
                            {% if product.category %}
                            <span class="category-badge">
                                <i class="fas fa-tag"></i> {{ product.category.name }}
                            </span>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">{{ product.name }}</h5>
                            <p class="card-text text-muted">{{ product.description|truncatewords:20 }}</p>
                            <p class="card-text"><strong>Price:</strong> EGP {{ product.price|floatformat:2 }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <a href="{% url 'GB_FARM:product_detail' product.id %}" class="btn btn-outline-primary">
                                    View Details
                                </a>
                                {% if product.status == 'available' %}
                                <a href="{% url 'GB_FARM:add_to_cart' product.id %}" class="btn btn-primary">
                                    Add to Cart
                                </a>
                                {% else %}
                                <button class="btn btn-secondary" disabled>Out of Stock</button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if products.has_other_pages %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if products.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ products.previous_page_number }}{% if query %}&q={{ query }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if min_price %}&min_price={{ min_price }}{% endif %}{% if max_price %}&max_price={{ max_price }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">
                            Previous
                        </a>
                    </li>
                    {% endif %}

                    {% for num in products.paginator.page_range %}
                    <li class="page-item {% if products.number == num %}active{% endif %}">
                        <a class="page-link" href="?page={{ num }}{% if query %}&q={{ query }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if min_price %}&min_price={{ min_price }}{% endif %}{% if max_price %}&max_price={{ max_price }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">
                            {{ num }}
                        </a>
                    </li>
                    {% endfor %}

                    {% if products.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ products.next_page_number }}{% if query %}&q={{ query }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if min_price %}&min_price={{ min_price }}{% endif %}{% if max_price %}&max_price={{ max_price }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">
                            Next
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            {% else %}
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-search fs-1 text-muted mb-3"></i>
                    <h3>No products found</h3>
                    <p class="text-muted">Try adjusting your search or filters to find what you're looking for.</p>
                    <a href="{% url 'GB_FARM:product_list' %}" class="btn btn-primary mt-3">Browse All Products</a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when select elements change
        const selectElements = document.querySelectorAll('select');
        selectElements.forEach(select => {
            select.addEventListener('change', function() {
                document.getElementById('filter-form').submit();
            });
        });
    });
</script>
{% endblock %}
{% endblock %} 