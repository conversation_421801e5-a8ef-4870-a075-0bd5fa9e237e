# Generated by Django 4.2.20 on 2025-07-20 09:59

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0053_alter_corporatesocial_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Variety',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Variety Name')),
                ('color', models.CharField(default='#000000', max_length=7, verbose_name='Variety Color')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('weight_from', models.PositiveIntegerField(verbose_name='Min Weight')),
                ('weight_to', models.PositiveIntegerField(verbose_name='Max Weight')),
                ('weight_unit', models.CharField(choices=[('gm', 'Grams'), ('kg', 'Kilograms')], default='kg', max_length=5)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Variety',
                'verbose_name_plural': 'Varieties',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='VarietyProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=150, verbose_name='Product Title')),
                ('color', models.CharField(default='#000000', max_length=7, verbose_name='Product Color')),
                ('img', models.ImageField(blank=True, null=True, upload_to='products/', verbose_name='Product Image')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('varieties', models.ManyToManyField(related_name='products', to='website.variety')),
            ],
            options={
                'verbose_name': 'Variety Product',
                'verbose_name_plural': 'Variety Products',
                'ordering': ['title'],
            },
        ),
        migrations.CreateModel(
            name='VarietyCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Category Name')),
                ('description', models.TextField(blank=True, max_length=500, null=True, verbose_name='Category Description')),
                ('color', models.CharField(default='#000000', max_length=7, verbose_name='Category Color')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='categories', to='website.varietyproduct')),
            ],
            options={
                'verbose_name': 'Variety Category',
                'verbose_name_plural': 'Variety Categories',
                'ordering': ['name'],
            },
        ),
    ]
