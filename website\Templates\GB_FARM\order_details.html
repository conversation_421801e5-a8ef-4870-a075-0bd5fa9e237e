{% extends 'GB_FARM/base.html' %}
{% load static %}

{% block title %}Order Details{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <!-- Order Header -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="mb-0">Order #{{ order.id }}</h2>
                        <span class="badge bg-{{ order.status_color }}">{{ order.get_status_display }}</span>
                    </div>
                    <p class="text-muted mb-0">Placed on {{ order.created_at|date:"F j, Y" }}</p>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Customer Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Name:</strong> {{ order.user.get_full_name }}</p>
                            <p><strong>Email:</strong> {{ order.user.email }}</p>
                            <p><strong>Phone:</strong> {{ order.user.profile.phone_number|default:"Not provided" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Customer ID:</strong> {{ order.user.id }}</p>
                            <p><strong>Account Created:</strong> {{ order.user.date_joined|date:"F j, Y" }}</p>
                            <p><strong>Total Orders:</strong> {{ order.user.orders.count }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Order Items</h5>
                </div>
                <div class="card-body">
                    {% for item in order_items %}
                    <div class="row mb-3 {% if not forloop.last %}border-bottom pb-3{% endif %}">
                        <div class="col-md-2">
                            {% if item.product.image %}
                            <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="img-fluid rounded">
                            {% else %}
                            <div class="bg-light d-flex align-items-center justify-content-center rounded" style="height: 100px;">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-7">
                            <h6 class="mb-1">{{ item.product.name }}</h6>
                            <p class="text-muted mb-1">Quantity: {{ item.quantity }}</p>
                            <p class="text-muted mb-0">Price: EGP {{ item.price|floatformat:2 }}</p>
                        </div>
                        <div class="col-md-3 text-end">
                            <p class="mb-0">EGP {{ item.total_price|floatformat:2 }}</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Order Summary -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Order Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-6">Subtotal</div>
                        <div class="col-6 text-end">EGP {{ order.total_amount|floatformat:2 }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">Shipping</div>
                        <div class="col-6 text-end">Free</div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6"><strong>Total</strong></div>
                        <div class="col-6 text-end"><strong>EGP {{ order.total_amount|floatformat:2 }}</strong></div>
                    </div>
                </div>
            </div>

            <!-- Shipping Address -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Shipping Address</h5>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ order.shipping_address|linebreaks }}</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="mt-4 text-center">
                <a href="{% url 'GB_FARM:order_messages' order_id=order.id %}" class="btn btn-primary me-2">
                    <i class="fas fa-comment"></i> Messages
                    {% if unread_messages_count > 0 %}
                    <span class="badge bg-secondary ms-1">{{ unread_messages_count }}</span>
                    {% endif %}
                </a>
                <a href="{% url 'GB_FARM:track_order' %}?order_id={{ order.id }}" class="btn btn-outline-primary me-2">
                    <i class="fas fa-truck"></i> Track Order
                </a>
                <a href="{% url 'GB_FARM:generate_invoice' order_id=order.id %}" class="btn btn-outline-info me-2" target="_blank">
                    <i class="fas fa-print"></i> Print Invoice
                </a>
                <a href="{% url 'GB_FARM:product_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-shopping-bag"></i> Continue Shopping
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %} 