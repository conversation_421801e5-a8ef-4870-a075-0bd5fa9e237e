from django.db import models
from django.contrib.auth.models import User
from .models import Category, Product, Cart, Order, OrderItem, Payment

def create_product_example():
    """
    Example of creating a new product with category
    """
    try:
        # Create category first
        farm_produce = Category.objects.create(
            name="Farm Produce",
            slug="farm-produce",
            description="Fresh vegetables and fruits from the farm"
        )
        print(f"Created category: {farm_produce.name}")

        # Create product in that category
        tomatoes = Product.objects.create(
            name="Organic Tomatoes",
            category=farm_produce,
            price=3.99,
            stock=100,
            status="available",
            description="Fresh organic tomatoes harvested daily"
        )
        print(f"Created product: {tomatoes.name}")
        return tomatoes
    except Exception as e:
        print(f"Error creating product: {str(e)}")
        return None

def add_to_cart_example(username, product_name):
    """
    Example of adding items to cart
    """
    try:
        # Get user and product
        user = User.objects.get(username=username)
        product = Product.objects.get(name=product_name)

        # Add to cart (handles both new items and existing ones)
        cart_item, created = Cart.objects.get_or_create(
            user=user,
            product=product,
            defaults={'quantity': 1}
        )

        if not created:
            cart_item.quantity += 1
            cart_item.save()
            print(f"Updated quantity for {product.name} in cart")
        else:
            print(f"Added {product.name} to cart")

        return cart_item
    except User.DoesNotExist:
        print(f"User {username} not found")
        return None
    except Product.DoesNotExist:
        print(f"Product {product_name} not found")
        return None
    except Exception as e:
        print(f"Error adding to cart: {str(e)}")
        return None

def create_order_example(username):
    """
    Example of creating an order from cart items
    """
    try:
        # Get user's cart items
        user = User.objects.get(username=username)
        cart_items = Cart.objects.filter(user=user)

        if not cart_items.exists():
            print("Cart is empty")
            return None

        # Calculate total
        total_amount = sum(item.product.price * item.quantity for item in cart_items)

        # Create order
        order = Order.objects.create(
            user=user,
            total_amount=total_amount,
            status='pending',
            shipping_address=user.profile.default_shipping_address or "1234 Farm Road"
        )
        print(f"Created order #{order.id}")

        # Create order items from cart
        for cart_item in cart_items:
            OrderItem.objects.create(
                order=order,
                product=cart_item.product,
                quantity=cart_item.quantity,
                price=cart_item.product.price
            )
            print(f"Added {cart_item.product.name} to order")

        # Clear cart after order creation
        cart_items.delete()
        print("Cleared cart after order creation")

        return order
    except User.DoesNotExist:
        print(f"User {username} not found")
        return None
    except Exception as e:
        print(f"Error creating order: {str(e)}")
        return None

def process_payment_example(order_id):
    """
    Example of processing payment for an order
    """
    try:
        order = Order.objects.get(id=order_id)
        
        # Create payment for order
        payment = Payment.objects.create(
            order=order,
            amount=order.total_amount,
            status='pending',
            payment_method='credit_card',
            card_last4='4242',
            card_brand='Visa',
            transaction_id=f'txn_{order_id}_{order.created_at.strftime("%Y%m%d%H%M%S")}'
        )
        print(f"Created payment for order #{order.id}")

        # Simulate payment processing
        payment.status = 'completed'
        payment.save()
        print("Payment marked as completed")

        # Update order status
        order.status = 'processing'
        order.save()
        print("Order status updated to processing")

        return payment
    except Order.DoesNotExist:
        print(f"Order #{order_id} not found")
        return None
    except Exception as e:
        print(f"Error processing payment: {str(e)}")
        return None

def run_examples():
    """
    Run all examples in sequence
    """
    print("\n=== Running Product Creation Example ===")
    product = create_product_example()

    print("\n=== Running Cart Example ===")
    cart_item = add_to_cart_example("customer1", "Organic Tomatoes")

    print("\n=== Running Order Creation Example ===")
    order = create_order_example("customer1")

    if order:
        print("\n=== Running Payment Processing Example ===")
        payment = process_payment_example(order.id)

if __name__ == "__main__":
    run_examples() 