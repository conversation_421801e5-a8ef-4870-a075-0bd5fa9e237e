{% extends 'GB_FARM/base.html' %}
{% load static %}

{% block title %}Checkout{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">Checkout</h4>
                </div>
                <div class="card-body">
                    <form method="post" id="checkout-form">
                        {% csrf_token %}
                        
                        <!-- Personal Information Section -->
                        <div class="mb-4">
                            <h5>Personal Information</h5>
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="{{ form.full_name.id_for_label }}" class="form-label">Full Name *</label>
                                    {{ form.full_name }}
                                    {% if form.full_name.errors %}<div class="invalid-feedback d-block">{{ form.full_name.errors.as_text }}</div>{% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number *</label>
                                    {{ form.phone_number }}
                                    {% if form.phone_number.errors %}<div class="invalid-feedback d-block">{{ form.phone_number.errors.as_text }}</div>{% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Delivery Method Section -->
                        <div class="mb-4">
                            <h5>Delivery Method</h5>
                            <div class="delivery-options">
                                <!-- Render delivery_method choices manually -->
                                {% for radio in form.delivery_method %}
                                <div class="form-check mb-2">
                                    {{ radio.tag }}
                                    <label class="form-check-label" for="{{ radio.id_for_label }}">
                                        {{ radio.choice_label }}
                                    </label>
                                </div>
                                {% endfor %}
                                {% if form.delivery_method.errors %}<div class="invalid-feedback d-block">{{ form.delivery_method.errors.as_text }}</div>{% endif %}
                            </div>
                        </div>

                        <!-- Delivery Zone Section (Dropdown, shown conditionally) -->
                        <div class="mb-4" id="delivery-zone-section" style="display: none;">
                            <h5>Delivery Zone *</h5>
                            {{ form.delivery_zone }}
                            {% if form.delivery_zone.errors %}<div class="invalid-feedback d-block">{{ form.delivery_zone.errors.as_text }}</div>{% endif %}
                        </div>
            
                        <!-- Shipping Address Section (shown conditionally) -->
                        <div class="mb-4" id="shipping-address-section" style="display: none;">
                            <h5>Shipping Address *</h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.floor_number.id_for_label }}" class="form-label">Floor Number</label>
                                    {{ form.floor_number }}
                                    {% if form.floor_number.errors %}<div class="invalid-feedback d-block">{{ form.floor_number.errors.as_text }}</div>{% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.office_number.id_for_label }}" class="form-label">Office Number</label>
                                    {{ form.office_number }}
                                    {% if form.office_number.errors %}<div class="invalid-feedback d-block">{{ form.office_number.errors.as_text }}</div>{% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Method Section -->
                        <div class="mb-4">
                            <h5>Payment Method</h5>
                            <div class="payment-options">
                                {% for radio in form.payment_method %}
                                <div class="form-check mb-2">
                                    {{ radio.tag }}
                                    <label class="form-check-label" for="{{ radio.id_for_label }}">
                                        {{ radio.choice_label }}
                                    </label>
                                </div>
                                {% endfor %}
                                {% if form.payment_method.errors %}<div class="invalid-feedback d-block">{{ form.payment_method.errors.as_text }}</div>{% endif %}
                            </div>
                        </div>

                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                        {% endif %}
                        
                        <button type="submit" class="btn btn-primary">Place Order</button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Order Summary Section -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Order Summary</h4>
                </div>
                <div class="card-body">
                    {% for item in cart_items %}
                    <div class="d-flex justify-content-between mb-2">
                        <span>{{ item.product.name }} x {{ item.quantity }}</span>
                        <span>EGP {{ item.total_price }}</span>
                    </div>
                    {% endfor %}
                    <hr>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span>EGP {{ total }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2" id="delivery-fee-row" style="display: none;">
                        <span>Delivery Fee:</span>
                        <span id="delivery-fee">EGP 0.00</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between">
                        <strong>Total:</strong>
                        <strong id="total-amount">EGP {{ total }}</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to delivery method radio buttons
    document.querySelectorAll('.delivery-method-radio').forEach(radio => {
        radio.addEventListener('change', updateDeliveryForm);
    });

    // Add event listener to delivery zone dropdown
    const deliveryZoneSelect = document.getElementById('id_delivery_zone');
    if (deliveryZoneSelect) {
        deliveryZoneSelect.addEventListener('change', updateDeliveryPrice);
    }
    
    // Initial setup based on current selection (if any)
    updateDeliveryForm(); 
});

function updateDeliveryForm() {
    const deliveryMethodRadio = document.querySelector('.delivery-method-radio:checked');
    const deliveryMethod = deliveryMethodRadio ? deliveryMethodRadio.value : null;
    
    const shippingAddressSection = document.getElementById('shipping-address-section');
    const deliveryZoneSection = document.getElementById('delivery-zone-section');
    const deliveryFeeRow = document.getElementById('delivery-fee-row');
    
    if (deliveryMethod === 'delivery') {
        // Show relevant sections for delivery
        shippingAddressSection.style.display = 'block';
        deliveryZoneSection.style.display = 'block';
        deliveryFeeRow.style.display = 'flex'; // Use flex for d-flex justify-content-between
        // Trigger price update based on current zone selection
        updateDeliveryPrice(); 
    } else if (deliveryMethod === 'collect') {
        // Hide sections for store collection
        shippingAddressSection.style.display = 'none';
        deliveryZoneSection.style.display = 'none';
        deliveryFeeRow.style.display = 'none';
        // Reset price for collection
        updateDeliveryPrice(); 
    } else {
        // Default state (neither selected yet)
        shippingAddressSection.style.display = 'none';
        deliveryZoneSection.style.display = 'none';
        deliveryFeeRow.style.display = 'none';
        updateDeliveryPrice();
    }
}

function updateDeliveryPrice() {
    const deliveryMethodRadio = document.querySelector('.delivery-method-radio:checked');
    const deliveryMethod = deliveryMethodRadio ? deliveryMethodRadio.value : null;
    const deliveryZoneSelect = document.getElementById('id_delivery_zone');
    const deliveryFeeEl = document.getElementById('delivery-fee');
    const totalAmountEl = document.getElementById('total-amount');
    const subtotal = parseFloat('{{ total|floatformat:2 }}'); // Use floatformat filter
    let zonePrice = 0;

    if (deliveryMethod === 'delivery' && deliveryZoneSelect && deliveryZoneSelect.value) {
        const selectedOption = deliveryZoneSelect.options[deliveryZoneSelect.selectedIndex];
        // Extract price from the option text like "Zone Name - XX.XX EGP" (Handles different currencies/formats)
        const text = selectedOption.textContent;
        const priceMatch = text.match(/(\d+\.?\d*)\s*(EGP)?$/i); // Match price at the end
        if (priceMatch && priceMatch[1]) {
            zonePrice = parseFloat(priceMatch[1]);
        } else {
             // Fallback or error handling if price not found in text
            zonePrice = 0; 
            console.error("Could not parse price from zone text:", text);
        }
    }

    deliveryFeeEl.textContent = `EGP ${zonePrice.toFixed(2)}`;
    totalAmountEl.textContent = `EGP ${(subtotal + zonePrice).toFixed(2)}`;
}
</script>
{% endblock %}
{% endblock %} 