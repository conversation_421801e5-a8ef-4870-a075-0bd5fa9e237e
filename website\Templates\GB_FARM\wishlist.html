{% extends 'GB_FARM/base.html' %}

{% block title %}My Wishlist{% endblock %}

{% block content %}
<div class="container my-5">
    <h1 class="mb-4">My Wishlist</h1>
    
    {% if messages %}
    <div class="messages mb-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    {% if wishlist_items %}
    <div class="row row-cols-1 row-cols-md-3 g-4">
        {% for item in wishlist_items %}
        <div class="col">
            <div class="card h-100 shadow-sm">
                {% if item.product.image %}
                <img src="{{ item.product.image.url }}" class="card-img-top" alt="{{ item.product.name }}" style="height: 200px; object-fit: cover;">
                {% else %}
                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                    <i class="bi bi-image text-muted fs-1"></i>
                </div>
                {% endif %}
                <div class="card-body">
                    <h5 class="card-title">{{ item.product.name }}</h5>
                    <p class="card-text text-muted">{{ item.product.description|truncatewords:20 }}</p>
                    <p class="card-text"><strong>Price:</strong> EGP {{ item.product.price|floatformat:2 }}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{% url 'GB_FARM:product_detail' item.product.id %}" class="btn btn-outline-primary">
                            View Details
                        </a>
                        <a href="{% url 'GB_FARM:add_to_cart' item.product.id %}" class="btn btn-primary">
                            Add to Cart
                        </a>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <a href="{% url 'GB_FARM:remove_from_wishlist' item.id %}" class="btn btn-outline-danger btn-sm w-100">
                        <i class="bi bi-trash"></i> Remove from Wishlist
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-heart fs-1 text-muted mb-3"></i>
            <h3>Your wishlist is empty</h3>
            <p class="text-muted">Start adding products you love to your wishlist!</p>
            <a href="{% url 'GB_FARM:product_list' %}" class="btn btn-primary mt-3">Browse Products</a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %} 