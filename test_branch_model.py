#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from website.models import Branch

def test_branch_model():
    """Test if the Branch model works correctly"""
    try:
        # Try to create a new branch
        branch = Branch(
            name="Test Branch",
            name_ar="فرع تجريبي",
            address="123 Test Street, Test City",
            address_ar="123 شارع التجربة، مدينة التجربة",
            latitude=30.0444,
            longitude=31.2357,
            is_active=True
        )
        
        # Test the clean method
        branch.clean()
        print("✅ Branch model clean() method works correctly")
        
        # Test string representation
        str_repr = str(branch)
        print(f"✅ Branch string representation: {str_repr}")
        
        # Test properties
        coords = branch.coordinates_display
        print(f"✅ Coordinates display: {coords}")
        
        maps_url = branch.google_maps_url
        print(f"✅ Google Maps URL: {maps_url}")
        
        print("✅ All Branch model tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing Branch model: {e}")
        return False

if __name__ == "__main__":
    test_branch_model()
