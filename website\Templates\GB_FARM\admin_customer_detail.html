{% extends 'GB_FARM/base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}Customer Details - {{ customer.username }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Admin Dashboard</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i> Orders
                    </a>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-box me-2"></i> Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-users me-2"></i> Customers
                    </a>
                    <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'GB_FARM:admin_customer_dashboard' %}">Customers</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ customer.username }}</li>
                </ol>
            </nav>

            <!-- Customer Details -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Customer Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-sm-4 text-muted">Username:</div>
                                <div class="col-sm-8">{{ customer.username }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4 text-muted">Name:</div>
                                <div class="col-sm-8">{{ customer.get_full_name|default:"Not provided" }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4 text-muted">Email:</div>
                                <div class="col-sm-8">{{ customer.email|default:"Not provided" }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4 text-muted">Phone:</div>
                                <div class="col-sm-8">{{ customer.user_phone|default:"Not provided" }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4 text-muted">User Type:</div>
                                <div class="col-sm-8">
                                    <span class="badge bg-{{ customer.user_type|yesno:'primary,secondary' }}">
                                        {{ customer.user_type|title }}
                                    </span>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4 text-muted">Status:</div>
                                <div class="col-sm-8">
                                    <span class="badge bg-{{ customer.is_active|yesno:'success,danger' }}">
                                        {{ customer.is_active|yesno:"Active,Inactive" }}
                                    </span>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4 text-muted">Joined:</div>
                                <div class="col-sm-8">{{ customer.date_joined|date:"F j, Y, g:i a" }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4 text-muted">Last Login:</div>
                                <div class="col-sm-8">
                                    {% if customer.last_login %}
                                    {{ customer.last_login|date:"F j, Y, g:i a" }}
                                    {% else %}
                                    Never
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Order Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-sm-6 text-muted">Total Orders:</div>
                                <div class="col-sm-6">{{ total_orders }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-6 text-muted">Total Spent:</div>
                                <div class="col-sm-6">EGP {{ total_spent|floatformat:2 }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-6 text-muted">Average Order Value:</div>
                                <div class="col-sm-6">
                                    {% if total_orders > 0 %}
                                    EGP {{ total_spent|divide:total_orders|floatformat:2 }}
                                    {% else %}
                                    EGP 0.00
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-6 text-muted">First Order:</div>
                                <div class="col-sm-6">
                                    {% if recent_orders %}
                                    {{ recent_orders.4.created_at|date:"F j, Y" }}
                                    {% else %}
                                    N/A
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-6 text-muted">Last Order:</div>
                                <div class="col-sm-6">
                                    {% if recent_orders %}
                                    {{ recent_orders.0.created_at|date:"F j, Y" }}
                                    {% else %}
                                    N/A
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if profile %}
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Address Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-sm-4 text-muted">Default Shipping:</div>
                                <div class="col-sm-8">
                                    {% if profile.default_shipping_address %}
                                    {{ profile.default_shipping_address|linebreaks }}
                                    {% else %}
                                    Not provided
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Recent Orders</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_orders %}
                                <tr>
                                    <td>{{ order.id }}</td>
                                    <td>{{ order.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <span class="badge bg-{% if order.status == 'delivered' %}success{% elif order.status == 'pending' %}warning{% elif order.status == 'cancelled' %}danger{% else %}info{% endif %}">
                                            {{ order.get_status_display }}
                                        </span>
                                    </td>
                                    <td>EGP {{ order.total_amount|floatformat:2 }}</td>
                                    <td>
                                        <a href="{% url 'GB_FARM:admin_order_detail' order.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">No orders found for this customer</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    {% if total_orders > 5 %}
                    <div class="text-center mt-3">
                        <a href="{% url 'GB_FARM:admin_order_dashboard' %}?q={{ customer.username }}" class="btn btn-outline-primary">
                            <i class="fas fa-list me-1"></i> View All Orders
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Back Button -->
            <div class="d-flex justify-content-start mb-4">
                <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to Customers
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %} 