# Generated by Django 4.2.20 on 2025-07-20 11:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0057_remove_excategory_product_excategory_products'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='excategory',
            name='products',
        ),
        migrations.AddField(
            model_name='exproduct',
            name='categories',
            field=models.ManyToManyField(related_name='products', to='website.excategory'),
        ),
    ]
