# Generated by Django 4.2.20 on 2025-07-05 18:30

from django.db import migrations, models
import imagekit.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0049_remove_branch_description_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CorporateSocial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='CSR', max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Title')),
                ('short_description_part1', models.TextField(default='At GB Farms, we value the importance of giving back to the community. We focus on developing new talents by giving technical workshops and training to students in high schools.', verbose_name='Short Description Part 1')),
                ('short_description_part1_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Short Description Part 1')),
                ('short_description_part2', models.TextField(default='and universities in the field and at the packhouse to prepare them for work in the agri field upon their graduation.', verbose_name='Short Description Part 2')),
                ('short_description_part2_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Short Description Part 2')),
                ('image', imagekit.models.fields.ProcessedImageField(blank=True, help_text='Image for the CSR section (e.g., team photo, community event)', null=True, upload_to='csr/')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Corporate Social Responsibility',
                'verbose_name_plural': 'Corporate Social Responsibility',
            },
        ),
    ]
