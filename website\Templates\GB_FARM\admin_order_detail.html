{% extends 'GB_FARM/base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}Order #{{ order.id }} Details{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline-item {
    position: relative;
    padding-left: 50px;
    margin-bottom: 30px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 30px;
    bottom: -30px;
    width: 2px;
    background: #dee2e6;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white text-white">
                    <h5 class="mb-0">Admin Dashboard</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-shopping-cart me-2"></i> Orders
                    </a>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-box me-2"></i> Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i> Customers
                    </a>
                    <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Order Header -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h2 class="mb-1">Order #{{ order.id }}</h2>
                            <p class="text-muted mb-0">Placed on {{ order.created_at|date:"F j, Y, g:i a" }}</p>
                        </div>
                        <div>
                            <span class="badge bg-{{ order.status_color }} fs-6">
                                {{ order.get_status_display }}
                            </span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Customer Information</h5>
                            <p class="mb-1"><strong>Name:</strong> {{ order.customer_name|default:order.user.username }}</p>
                            <p class="mb-1"><strong>Email:</strong> {{ order.user.email }}</p>
                            {% if order.phone_number %}
                            <p class="mb-1"><strong>Phone:</strong> {{ order.phone_number }}</p>
                            {% endif %}
                            {% if order.office_number %}
                            <p class="mb-0"><strong>Office:</strong> {{ order.office_number }}</p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h5>Shipping Address</h5>
                            <p class="mb-1">{{ order.shipping_address|linebreaks }}</p>
                            {% if order.delivery_zone %}
                            <p class="mb-0"><strong>Zone:</strong> {{ order.delivery_zone.name }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Order Items</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Price</th>
                                    <th>Quantity</th>
                                    <th class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order_items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if item.product.image %}
                                            <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" 
                                                 class="img-fluid rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                            {% else %}
                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                 style="width: 50px; height: 50px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                            {% endif %}
                                            <div>
                                                <h6 class="mb-0">{{ item.product.name }}</h6>
                                                <small class="text-muted">{{ item.product.category.name }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>EGP {{ item.price|floatformat:2 }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td class="text-end">EGP {{ item.quantity|multiply:item.price|floatformat:2 }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="3" class="text-end"><strong>Subtotal:</strong></td>
                                    <td class="text-end">EGP {{ subtotal|floatformat:2 }}</td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="text-end"><strong>Delivery:</strong></td>
                                    <td class="text-end">EGP {{ shipping_cost|floatformat:2 }}</td>
                                </tr>
                                {% if payment %}
                                <tr>
                                    <td colspan="3" class="text-end"><strong>Payment Method:</strong></td>
                                    <td class="text-end">{{ payment.get_payment_method_display }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td colspan="3" class="text-end"><strong>Total:</strong></td>
                                    <td class="text-end"><strong>EGP {{ total|floatformat:2 }}</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Order Timeline -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Order Timeline</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <!-- Order Placed -->
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Order Placed</h6>
                                <p class="text-muted mb-0">{{ order.created_at|date:"F j, Y, g:i a" }}</p>
                            </div>
                        </div>

                        <!-- Payment Status -->
                        <div class="timeline-item">
                            <div class="timeline-marker bg-{{ order.payment_status_color }}">
                                <i class="fas fa-{{ order.payment_status_icon }}"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Payment {{ order.payment_status }}</h6>
                                {% if order.payment_date %}
                                <p class="text-muted mb-0">{{ order.payment_date|date:"F j, Y, g:i a" }}</p>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Processing -->
                        <div class="timeline-item">
                            <div class="timeline-marker bg-{{ order.processing_status_color }}">
                                <i class="fas fa-{{ order.processing_status_icon }}"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Processing</h6>
                                {% if order.processing_date %}
                                <p class="text-muted mb-0">{{ order.processing_date|date:"F j, Y, g:i a" }}</p>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Shipped -->
                        <div class="timeline-item">
                            <div class="timeline-marker bg-{{ order.shipping_status_color }}">
                                <i class="fas fa-{{ order.shipping_status_icon }}"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Shipped</h6>
                                {% if order.shipping_date %}
                                <p class="text-muted mb-0">{{ order.shipping_date|date:"F j, Y, g:i a" }}</p>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Delivered -->
                        <div class="timeline-item">
                            <div class="timeline-marker bg-{{ order.delivery_status_color }}">
                                <i class="fas fa-{{ order.delivery_status_icon }}"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Delivered</h6>
                                {% if order.delivery_date %}
                                <p class="text-muted mb-0">{{ order.delivery_date|date:"F j, Y, g:i a" }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex justify-content-between">
                <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to Orders
                </a>
                
                <div>
                    <a href="{% url 'GB_FARM:admin_order_message' order_id=order.id %}" class="btn btn-primary me-2">
                        <i class="fas fa-comment me-1"></i> Messages
                        {% if unread_messages_count > 0 %}
                        <span class="badge bg-secondary ms-1">{{ unread_messages_count }}</span>
                        {% endif %}
                    </a>
                    
                    <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#updateStatusModal{{ order.id }}">
                        <i class="fas fa-edit me-1"></i> Update Status
                    </button>
                    
                    <a href="{% url 'GB_FARM:generate_invoice' order_id=order.id %}" class="btn btn-outline-info" target="_blank">
                        <i class="fas fa-print me-1"></i> Print Invoice
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal{{ order.id }}" tabindex="-1" data-bs-backdrop="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Order #{{ order.id }} Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'GB_FARM:update_order_status' order.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select" required>
                            <option value="pending" {% if order.status == 'pending' %}selected{% endif %}>Pending</option>
                            <option value="processing" {% if order.status == 'processing' %}selected{% endif %}>Processing</option>
                            <option value="shipped" {% if order.status == 'shipped' %}selected{% endif %}>Shipped</option>
                            <option value="delivered" {% if order.status == 'delivered' %}selected{% endif %}>Delivered</option>
                            <option value="cancelled" {% if order.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle both Update Status buttons
    const updateStatusButtons = document.querySelectorAll('.btn-success[data-bs-toggle="modal"], button[type="submit"]');
    updateStatusButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (this.type === 'submit') {
                // This is a form submit button
                const form = this.closest('form');
                if (form) {
                    e.preventDefault();
                    
                    // Get the form data
                    const formData = new FormData(form);
                    
                    // Submit using fetch
                    fetch(form.action, {
                        method: 'POST',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRFToken': formData.get('csrfmiddlewaretoken')
                        },
                        body: formData
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            // Successfully updated
                            alert('Status updated successfully');
                            // Close the modal
                            const modal = bootstrap.Modal.getInstance(document.querySelector('#updateStatusModal{{ order.id }}'));
                            if (modal) {
                                modal.hide();
                            }
                            // Reload the page to show the updated status
                            window.location.reload();
                        } else {
                            alert(data.message || 'Error updating status');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while updating the status. Please try again.');
                    });
                }
            }
        });
    });
});
</script>
{% endblock %} 