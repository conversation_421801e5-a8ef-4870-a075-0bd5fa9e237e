#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.test import Client, override_settings
from django.conf import settings

@override_settings(DEBUG=False, ALLOWED_HOSTS=['testserver', '127.0.0.1', 'localhost'])
def test_production_error_handlers():
    """Test error handlers in production mode (DEBUG=False)"""
    print("🧪 Testing Error Handlers in Production Mode...")
    print("=" * 60)
    
    client = Client()
    
    print(f"📋 Testing with DEBUG=False (Production Mode)")
    
    # Test real 404 error in production mode
    print(f"\n🔍 Testing Real 404 Error in Production...")
    try:
        response = client.get('/this-page-definitely-does-not-exist/')
        print(f"   ✅ Status Code: {response.status_code}")
        
        content = response.content.decode('utf-8')
        
        # Check if custom 404 page is shown
        if '404' in content and 'GB Farms' in content:
            print("   ✅ Custom 404 page working correctly")
        else:
            print("   ❌ Custom 404 page not working")
            
        # Check for specific 404 content
        if 'Looks like you\'re lost' in content or 'not available' in content:
            print("   ✅ Custom 404 content found")
        else:
            print("   ❌ Custom 404 content not found")
            
        # Check that it's not Django's debug page
        if 'Django' in content and 'debug' in content.lower():
            print("   ❌ Still showing Django debug page")
        else:
            print("   ✅ Not showing Django debug page")
            
    except Exception as e:
        print(f"   ❌ Error testing real 404: {e}")
    
    # Test accessing admin without permission (should trigger 403 in some cases)
    print(f"\n🔍 Testing Potential 403 Error...")
    try:
        # This might not always trigger 403, but let's try
        response = client.get('/admin/auth/user/add/')
        print(f"   ✅ Status Code: {response.status_code}")
        
        if response.status_code == 403:
            content = response.content.decode('utf-8')
            if 'GB Farms' in content:
                print("   ✅ Custom 403 page working")
            else:
                print("   ❌ Custom 403 page not working")
        elif response.status_code == 302:
            print("   ℹ️  Redirected to login (expected behavior)")
        else:
            print(f"   ℹ️  Got status {response.status_code} (may vary)")
            
    except Exception as e:
        print(f"   ❌ Error testing 403: {e}")
    
    print("\n" + "=" * 60)
    print("✅ Production error handler testing completed!")

def test_error_handler_configuration():
    """Test that error handlers are properly configured"""
    print("\n🔧 Testing Error Handler Configuration...")
    print("=" * 50)
    
    # Check if handlers are configured in URLs
    from project import urls
    
    handlers_to_check = ['handler400', 'handler403', 'handler404', 'handler500']
    
    for handler in handlers_to_check:
        if hasattr(urls, handler):
            handler_value = getattr(urls, handler)
            print(f"   ✅ {handler} configured: {handler_value}")
        else:
            print(f"   ❌ {handler} NOT configured")
    
    # Check if views exist
    from website import views
    
    view_functions = ['custom_400', 'custom_403', 'custom_404', 'custom_500']
    
    for view_func in view_functions:
        if hasattr(views, view_func):
            print(f"   ✅ {view_func} view exists")
        else:
            print(f"   ❌ {view_func} view NOT found")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    test_error_handler_configuration()
    test_production_error_handlers()
