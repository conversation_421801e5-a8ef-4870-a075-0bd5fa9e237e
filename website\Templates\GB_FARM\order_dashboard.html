{% extends 'GB_FARM/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Order Dashboard" %}{% endblock %}

{% block content %}
<div class="container py-4">
    <h1 class="mb-4">{% trans "Order Dashboard" %}</h1>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Total Orders" %}</h5>
                    <h2 class="card-text">{{ total_orders }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Total Revenue" %}</h5>
                    <h2 class="card-text">{{ total_revenue|floatformat:2 }} {% trans "EGP" %}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">{% trans "Average Order Value" %}</h5>
                    <h2 class="card-text">
                        {% if total_orders %}
                            {{ total_revenue|divisibleby:total_orders|floatformat:2 }} {% trans "EGP" %}
                        {% else %}
                            0 {% trans "EGP" %}
                        {% endif %}
                    </h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{% trans "Recent Orders" %}</h5>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    {% trans "Filter by Status" %}
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="?">{% trans "All" %}</a></li>
                    <li><a class="dropdown-item" href="?status=pending">{% trans "Pending" %}</a></li>
                    <li><a class="dropdown-item" href="?status=processing">{% trans "Processing" %}</a></li>
                    <li><a class="dropdown-item" href="?status=completed">{% trans "Completed" %}</a></li>
                    <li><a class="dropdown-item" href="?status=cancelled">{% trans "Cancelled" %}</a></li>
                </ul>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>{% trans "Order ID" %}</th>
                            <th>{% trans "Customer" %}</th>
                            <th>{% trans "Date" %}</th>
                            <th>{% trans "Total" %}</th>
                            <th>{% trans "Status" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders %}
                        <tr>
                            <td>#{{ order.id }}</td>
                            <td>{{ order.user.get_full_name|default:order.user.username }}</td>
                            <td>{{ order.created_at|date:"M d, Y H:i" }}</td>
                            <td>{{ order.total_amount }} {% trans "EGP" %}</td>
                            <td>
                                <span class="badge bg-{% if order.status == 'completed' %}success{% elif order.status == 'pending' %}warning{% elif order.status == 'cancelled' %}danger{% else %}info{% endif %}">
                                    {{ order.get_status_display }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#orderModal{{ order.id }}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="updateOrderStatus('{{ order.id }}', 'completed')">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="updateOrderStatus('{{ order.id }}', 'cancelled')">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <!-- Order Details Modal -->
                        <div class="modal fade" id="orderModal{{ order.id }}" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">{% trans "Order Details" %} #{{ order.id }}</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <h6>{% trans "Customer Information" %}</h6>
                                                <p>
                                                    <strong>{% trans "Name" %}:</strong> {{ order.user.get_full_name }}<br>
                                                    <strong>{% trans "Email" %}:</strong> {{ order.user.email }}<br>
                                                    <strong>{% trans "Phone" %}:</strong> {{ order.user.userprofile.phone_number|default:"N/A" }}
                                                </p>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>{% trans "Shipping Address" %}</h6>
                                                <p>{{ order.shipping_address|linebreaks }}</p>
                                            </div>
                                        </div>
                                        <h6>{% trans "Order Items" %}</h6>
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>{% trans "Product" %}</th>
                                                    <th>{% trans "Quantity" %}</th>
                                                    <th>{% trans "Price" %}</th>
                                                    <th>{% trans "Total" %}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for item in order.orderitem_set.all %}
                                                <tr>
                                                    <td>{{ item.product.name }}</td>
                                                    <td>{{ item.quantity }}</td>
                                                    <td>{{ item.price }} {% trans "EGP" %}</td>
                                                    <td>{{ item.get_total }} {% trans "EGP" %}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="3" class="text-end"><strong>{% trans "Total" %}:</strong></td>
                                                    <td><strong>{{ order.total_amount }} {% trans "EGP" %}</strong></td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <p class="text-muted mb-0">{% trans "No orders found" %}</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for order status updates -->
<script>
function updateOrderStatus(orderId, status) {
    if (confirm('{% trans "Are you sure you want to update this order status?" %}')) {
        fetch(`/admin/order/${orderId}/update-status/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('{% trans "Error updating order status" %}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{% trans "Error updating order status" %}');
        });
    }
}
</script>
{% endblock %} 