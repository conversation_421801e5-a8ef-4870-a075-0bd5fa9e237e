# Generated by Django 4.2.20 on 2025-07-21 04:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0064_excategory_created_at_excategory_description1_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='NewCatGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100, verbose_name='Group Title')),
                ('table_title', models.CharField(max_length=100, verbose_name='Table Title')),
                ('main_image', models.ImageField(blank=True, null=True, upload_to='groups/', verbose_name='Right Image')),
            ],
            options={
                'db_table': 'new_cat_group',
            },
        ),
        migrations.CreateModel(
            name='NewProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('color', models.CharField(default='#FF0000', max_length=7, verbose_name='Row Color')),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/')),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('weight_from', models.PositiveIntegerField()),
                ('weight_to', models.PositiveIntegerField()),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='website.newcatgroup')),
            ],
            options={
                'db_table': 'new_product',
            },
        ),
        migrations.CreateModel(
            name='NewVariety',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('weight_from', models.PositiveIntegerField()),
                ('weight_to', models.PositiveIntegerField()),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='varieties', to='website.newproduct')),
            ],
            options={
                'db_table': 'new_variety',
            },
        ),
        migrations.CreateModel(
            name='NewCatGroupDescription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField()),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='descriptions', to='website.newcatgroup')),
            ],
            options={
                'db_table': 'new_cat_group_description',
            },
        ),
    ]
