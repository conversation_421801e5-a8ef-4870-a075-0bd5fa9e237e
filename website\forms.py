from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from django.core.validators import MinValueValidator, RegexValidator
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
from .models import User, UserProfile, Product, Offer, Order, OrderItem, Cart, Payment, Review, Stock, Zone, ContactUsMessage   

class UserRegisterForm(UserCreationForm):
    class Meta:
        model = User
        fields = ['username', 'email', 'password1', 'password2']

class UserLoginForm(forms.Form):
    username = forms.CharField(max_length=150)
    password = forms.CharField(widget=forms.PasswordInput())

class UserUpdateForm(forms.ModelForm):
    class Meta:
        model = User
        fields = ['username', 'email', 'first_name', 'last_name', 'user_phone']

class ProfileUpdateForm(forms.ModelForm):
    class Meta:
        model = UserProfile
        fields = ['default_shipping_address']
        widgets = {
            'default_shipping_address': forms.Textarea(attrs={'rows': 3}),
        }

class ProductForm(forms.ModelForm):
    class Meta:
        model = Product
        fields = ['name', 'category', 'price', 'stock', 'unit_category', 'status', 'is_featured', 'image', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4}),
            'price': forms.NumberInput(attrs={'min': '0', 'step': '0.01'}),
            'stock': forms.NumberInput(attrs={'min': '0'}),
        }

    def clean_price(self):
        price = self.cleaned_data.get('price')
        if price <= 0:
            raise forms.ValidationError(_('Price must be greater than zero.'))
        return price

    def clean_stock(self):
        stock = self.cleaned_data.get('stock')
        if stock < 0:
            raise forms.ValidationError(_('Stock cannot be negative.'))
        return stock

class OfferForm(forms.ModelForm):
    class Meta:
        model = Offer
        fields = ['product', 'discount_percentage', 'is_active', 'is_featured']
        widgets = {
            'discount_percentage': forms.NumberInput(attrs={'min': '0', 'max': '100'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        return cleaned_data

class OrderForm(forms.ModelForm):
    class Meta:
        model = Order
        fields = ['shipping_address', 'status']
        widgets = {
            'shipping_address': forms.Textarea(attrs={'rows': 3}),
        }

    def clean_shipping_address(self):
        address = self.cleaned_data.get('shipping_address')
        if len(address.strip()) < 10:
            raise forms.ValidationError(_('Please provide a complete shipping address.'))
        return address

class OrderItemForm(forms.ModelForm):
    class Meta:
        model = OrderItem
        fields = ['product', 'quantity', 'price']
        widgets = {
            'quantity': forms.NumberInput(attrs={'min': '1'}),
            'price': forms.NumberInput(attrs={'min': '0.01', 'step': '0.01'}),
        }

class CartForm(forms.ModelForm):
    class Meta:
        model = Cart
        fields = ['product', 'quantity']
        widgets = {
            'quantity': forms.NumberInput(attrs={'min': '1'}),
        }

class PaymentForm(forms.ModelForm):
    class Meta:
        model = Payment
        fields = ['amount', 'payment_method']
        widgets = {
            'amount': forms.NumberInput(attrs={'min': '0.01', 'step': '0.01'}),
        }

    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount <= 0:
            raise forms.ValidationError('Amount must be greater than zero.')
        return amount

class CheckoutForm(forms.Form):
    # Personal Information
    full_name = forms.CharField(
        max_length=100,
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter your full name'})
    )
    phone_number = forms.CharField(
        max_length=15,
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter your phone number'})
    )
    office_number = forms.CharField(
        max_length=10,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter your office number (optional)'})
    )

    # Delivery Options
    delivery_method = forms.ChoiceField(
        choices=[
            ('delivery', 'Delivery'),
            ('collect', 'Collect from Store - Free')
        ],
        required=True,
        widget=forms.RadioSelect(attrs={'class': 'form-check-input delivery-method-radio'})
    )

    # Delivery Zone (Dropdown, only relevant for 'delivery')
    delivery_zone = forms.ModelChoiceField(
        queryset=Zone.objects.filter(is_active=True),
        empty_label="-- Select Delivery Zone --",
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    # Shipping Address components (only required for delivery)
    floor_number = forms.CharField(
        max_length=10,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Floor (Optional)'})
    )

    payment_method = forms.ChoiceField(
        choices=[
            ('credit_card', 'Credit Card'),
            ('cash', 'Cash'),
            ('instapay', 'Instapay')
        ],
        required=True,
        widget=forms.RadioSelect(attrs={'class': 'form-check-input'})
    )

    def clean(self):
        cleaned_data = super().clean()
        delivery_method = cleaned_data.get('delivery_method')
        # Get remaining address components
        floor_number = cleaned_data.get('floor_number')
        office_number = cleaned_data.get('office_number') # Still exists
        delivery_zone = cleaned_data.get('delivery_zone')

        full_shipping_address = ""
        if delivery_method == 'delivery':
            errors = {}
            # Remove validation for street_address and building_number
            # if not street_address:
            #     errors['street_address'] = 'Street address is required for delivery.'
            # if not building_number:
            #     errors['building_number'] = 'Building number is required for delivery.'
            
            # Keep zone validation
            if not delivery_zone:
                errors['delivery_zone'] = 'Please select a delivery zone for delivery.'
            
            if errors:
                raise forms.ValidationError(errors)

            # Construct the full address string using remaining fields
            address_parts = []
            if floor_number:
                address_parts.append(f"Floor: {floor_number}")
            # Re-evaluate if office number should be part of the main address string
            # It's saved separately in the Order model currently.
            # if office_number:
            #     address_parts.append(f"Office: {office_number}") 
            full_shipping_address = ", ".join(filter(None, address_parts))
        
        # Add the constructed address to cleaned_data
        cleaned_data['shipping_address'] = full_shipping_address

        return cleaned_data

class UserProfileForm(forms.ModelForm):
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message=_("Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.")
    )
    phone = forms.CharField(validators=[phone_regex], max_length=17)

    class Meta:
        model = UserProfile
        fields = ['default_shipping_address', 'phone']
        widgets = {
            'default_shipping_address': forms.Textarea(attrs={'rows': 3}),
        }

    def clean_default_shipping_address(self):
        address = self.cleaned_data.get('default_shipping_address')
        if address and len(address.strip()) < 10:
            raise forms.ValidationError(_('Please provide a complete shipping address.'))
        return address

class ZoneForm(forms.ModelForm):
    class Meta:
        model = Zone
        fields = ['name', 'price', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter zone name'}),
            'price': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'Enter delivery price', 'step': '0.01', 'min': '0'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }

class ContactUsMessageForm(forms.ModelForm):
    class Meta:
        model = ContactUsMessage
        fields = ['name', 'company_name', 'email', 'phone_number', 'business_type', 'find_out_source', 'message']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Your Full Name *'}),
            'company_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Company Name *'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Your Email *'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Tel *'}),
            'business_type': forms.RadioSelect(attrs={'class': 'form-check-input'}),
            'find_out_source': forms.RadioSelect(attrs={'class': 'form-check-input'}),
            'message': forms.Textarea(attrs={'class': 'form-control', 'rows': 5, 'placeholder': 'Whats Your message?'}),
        } 
