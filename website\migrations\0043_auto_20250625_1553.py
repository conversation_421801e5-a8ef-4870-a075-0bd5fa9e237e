# Generated by Django 4.2.20 on 2025-06-25 15:53

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0042_careerapplication_delete_careersubmission'),
    ]

    operations = [
        # Add new fields to existing CareerApplication model
        migrations.AddField(
            model_name='careerapplication',
            name='phone_number',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='careerapplication',
            name='cv_file',
            field=models.FileField(blank=True, null=True, upload_to='career_cvs/'),
        ),
        migrations.AddField(
            model_name='careerapplication',
            name='cover_letter',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='careerapplication',
            name='years_of_experience',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='careerapplication',
            name='current_position',
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='careerapplication',
            name='linkedin_profile',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='careerapplication',
            name='portfolio_website',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='careerapplication',
            name='salary_expectation',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='careerapplication',
            name='available_start_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='careerapplication',
            name='is_processed',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='careerapplication',
            name='notes',
            field=models.TextField(blank=True, help_text='Admin notes', null=True),
        ),
        migrations.AddField(
            model_name='careerapplication',
            name='ip_address',
            field=models.GenericIPAddressField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='careerapplication',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='careerapplication',
            name='career',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='website.career'),
            preserve_default=False,
        ),
        # Rename and modify existing fields
        migrations.RenameField(
            model_name='careerapplication',
            old_name='submitted_at',
            new_name='created_at',
        ),
        migrations.AlterField(
            model_name='careerapplication',
            name='full_name',
            field=models.CharField(max_length=200),
        ),
        # Remove old fields
        migrations.RemoveField(
            model_name='careerapplication',
            name='cv',
        ),
        migrations.RemoveField(
            model_name='careerapplication',
            name='subject',
        ),
        migrations.RemoveField(
            model_name='careerapplication',
            name='message',
        ),
    ]
