from .models import Category, <PERSON><PERSON>, <PERSON><PERSON>, ContactInfo, ExternalFooter, AboutUsVideo
from django.conf import settings

def website_context(request):
    """
    Context processor that provides common data to all templates.
    """
    context = {
        'categories': Category.objects.all(),
        'site_name': 'GB Farm',
        'site_description': 'Premium organic fruits, vegetables, and honey—harvested fresh from our farm',
        'contact_email': settings.EMAIL_HOST_USER,
        'debug': settings.DEBUG,
    }
    
    # Add cart count if user is authenticated
    if request.user.is_authenticated:
        context['cart_count'] = Cart.objects.filter(user=request.user).count()
    else:
        context['cart_count'] = 0
    
    return context 

def cart_count_processor(request):
    """Add cart count to all templates"""
    cart_count = 0
    if request.user.is_authenticated:
        cart_count = Cart.objects.filter(user=request.user).count()
    return {'cart_count': cart_count}

def footer_context(request):
    """Provide footer information for all templates"""
    try:
        footer = Footer.objects.first()
        contact_info = ContactInfo.objects.filter(is_active=True).first()
    except:
        footer = None
        contact_info = None
    
    return {
        'footer': footer,
        'contact_info': contact_info,
    }

def interface_context(request):
    """Provide information about which interface to use based on user type"""
    context = {
        'is_internal': False,
        'current_language': request.LANGUAGE_CODE if hasattr(request, 'LANGUAGE_CODE') else 'en',
        'available_languages': [
            ('en', 'English'),
            ('ar', 'Arabic'),
        ]
    }
    
    # Determine which interface to use
    if request.user.is_authenticated and request.user.user_type == 'admin':
        context['is_internal'] = True
    
    return context 

def external_footer_context(request):
    """
    Makes the ExternalFooter object available to all templates.
    """
    try:
        footer_data = ExternalFooter.load()
    except ExternalFooter.DoesNotExist:
        # In case the footer object hasn't been created in the admin yet
        footer_data = None
    return {'external_footer': footer_data}

def about_us_video_context(request):
    """
    Makes the AboutUsVideo object available to all templates.
    """
    try:
        about_us_video = AboutUsVideo.load()
    except AboutUsVideo.DoesNotExist:
        # In case the AboutUsVideo object hasn't been created in the admin yet
        about_us_video = None
    return {'about_us_video': about_us_video} 