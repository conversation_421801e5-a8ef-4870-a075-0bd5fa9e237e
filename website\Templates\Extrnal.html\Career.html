{% extends "Extrnal.html/EX.base.html" %}
{% load static %}

{% block head %}
    <title>Careers - GB Farms</title>
{% endblock %}

{% block header %}
    {% include "Extrnal.html/navebar.html" %}
{% endblock %}

{% block content %}
    <!-- Main Career Section -->
    <main class="relative h-screen w-full flex items-center justify-start"
        style="background-image: url('{% static "images/business-people-silhouette-comapany-working-togetherness-teamwork-office.png" %}'); background-size: cover; background-position: center;">
        <div class="flex justify-between items-center w-full px-4 md:px-20">
        </div>
    </main>

    <!-- Second Section - Career Description -->
    <section class="w-full py-12 md:py-20 bg-gray-100 flex flex-col items-center justify-center text-center md:text-left px-4 md:px-20">
        <h2 class="text-green-700 text-4xl md:text-7xl font-bold mb-4 md:mb-6 w-full max-w-6xl">CAREERS</h2>
        <p class="text-green-700 text-base md:text-lg w-full max-w-6xl">
            Ghabbour farms aims to employ exceptional committed talents that strive to work in a performance driven culture of excellence. We offer our employees exciting career opportunities.
        </p>
    </section>

    <!-- Third Section - Why Ghabbour -->
    <section class="w-full py-12 md:py-20 bg-gray-100 flex flex-col items-center justify-start px-4 md:px-20">
        <h2 class="text-green-700 text-4xl md:text-7xl font-bold mb-4 md:mb-6 w-full max-w-6xl text-center md:text-left">Why Ghabbour</h2>
        <div class="flex flex-col md:flex-row justify-between items-start w-full max-w-6xl space-y-6 md:space-y-0 md:space-x-10">
            <p class="text-green-700 text-base md:text-lg w-full md:w-1/2 text-center md:text-left">
                GB farms is a company that recognizes and values constant growth, not only as a company but for you as an employee and potential future leader within our organization. As a member of the GB family you can rest assured that we will constantly
            </p>
            <p class="text-green-700 text-base md:text-lg w-full md:w-1/2 text-center md:text-left">
                invest in your growth as a professional while providing a work environment helpful to your development. Whether it is comprehensive on the job training or expertly led business training, your progression as members of our organization is our priority.
            </p>
        </div>
    </section>

    <!-- Fifth Section - Open Positions -->
    <section class="w-full py-12 md:py-20 bg-gray-100 flex flex-col items-center justify-start px-4 md:px-20">
        <h2 class="text-green-700 text-4xl md:text-7xl font-bold mb-6 md:mb-10 text-center">Open Positions</h2>
        <div class="w-full max-w-6xl space-y-6 md:space-y-8">
            {% if careers %}
                {% for career in careers %}
                    <div class="bg-gray-50 p-6 md:p-8 rounded-lg shadow-lg flex flex-col md:flex-row justify-between items-start md:items-center transition-transform transform hover:scale-105">
                        <div class="w-full">
                            <h3 class="text-2xl md:text-3xl font-bold text-green-800">{{ career.title }}</h3>
                            <p class="text-gray-600 mt-2 text-sm md:text-base">{{ career.department }} - {{ career.location }}</p>
                            <div class="flex flex-wrap gap-2 mt-3 md:mt-4">
                                <span class="inline-block bg-green-200 text-green-800 text-xs md:text-sm font-semibold px-2 md:px-3 py-1 rounded-full">{{ career.get_job_type_display }}</span>
                                <span class="inline-block bg-blue-200 text-blue-800 text-xs md:text-sm font-semibold px-2 md:px-3 py-1 rounded-full">{{ career.get_experience_level_display }}</span>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <p class="text-center text-gray-500 text-base md:text-lg">There are currently no open positions. Please check back later.</p>
            {% endif %}
        </div>
    </section>

    <!-- Fourth Section - Join Our Team (Contact Form) -->
    <section class="w-full py-12 md:py-20 bg-gray-100 flex flex-col items-center justify-start px-4 md:px-20 relative overflow-hidden">
        <!-- Background leaves - if needed, add a div for a subtle background image here -->
        <h2 class="text-green-700 text-4xl md:text-7xl font-bold mb-6 md:mb-10 text-center">Join Our Team</h2>
        <form id="careerApplicationForm" class="w-full max-w-4xl space-y-4 md:space-y-6" method="post" enctype="multipart/form-data">
            {% csrf_token %}
            <input type="hidden" name="source_page" value="external_career" />
            
            <!-- Full Name -->
            <input type="text" name="full_name" placeholder="Your Full Name" required class="w-full p-3 md:p-4 border border-green-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-700 text-sm md:text-base" />
            
            <!-- Email -->
            <input type="email" name="email" placeholder="Your Email Address" required class="w-full p-3 md:p-4 border border-green-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-700 text-sm md:text-base" />
            
            <!-- CV Upload -->
            <div class="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4">
                <label for="cv-upload" class="px-4 md:px-6 py-2 md:py-3 bg-green-700 text-white rounded-lg cursor-pointer hover:bg-green-950 transition duration-300 text-sm md:text-base text-center">Choose File</label>
                <input id="cv-upload" name="cv_file" type="file" accept=".pdf,.doc,.docx" class="hidden" />
                <span class="text-gray-600 text-xs md:text-base text-center md:text-left" id="file-name">Max size 2 MB</span>
            </div>
            
            <!-- Subject -->
            <input type="text" name="subject" placeholder="Subject*" class="w-full p-3 md:p-4 border border-green-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-700 text-sm md:text-base" />
            
            <!-- Message -->
            <textarea name="cover_letter" placeholder="Whats Your message?" rows="4" class="w-full p-3 md:p-4 border border-green-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-700 text-sm md:text-base md:rows-6"></textarea>
            
            <!-- Hidden fields for additional data (optional) -->
            <input type="hidden" name="phone_number" value="" />
            <input type="hidden" name="years_of_experience" value="" />
            <input type="hidden" name="current_position" value="" />
            <input type="hidden" name="linkedin_profile" value="" />
            <input type="hidden" name="portfolio_website" value="" />
            <input type="hidden" name="salary_expectation" value="" />
            <input type="hidden" name="available_start_date" value="" />
            
            <button type="submit" class="px-6 md:px-8 py-3 md:py-4 bg-green-700 text-white font-bold rounded-lg hover:bg-green-950 transition duration-300 text-sm md:text-base">SEND</button>
        </form>
    </section>
{% endblock %}

{% block footer %}
    {% include "Extrnal.html/footer.html" %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle file upload display
    const fileInput = document.getElementById('cv-upload');
    const fileName = document.getElementById('file-name');
    
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            const file = e.target.files[0];
            const maxSize = 2 * 1024 * 1024; // 2MB in bytes
            
            if (file.size > maxSize) {
                alert('File size must be less than 2MB');
                e.target.value = '';
                fileName.textContent = 'Max size 2 MB';
                return;
            }
            
            fileName.textContent = file.name;
        } else {
            fileName.textContent = 'Max size 2 MB';
        }
    });
    
    // Handle form submission
    const form = document.getElementById('careerApplicationForm');
    form.addEventListener('submit', function(e) {
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        
        // Disable button and show loading state
        submitButton.disabled = true;
        submitButton.textContent = 'SENDING...';
        submitButton.classList.add('opacity-50');
        
        // Re-enable button after a delay (in case of errors)
        setTimeout(function() {
            submitButton.disabled = false;
            submitButton.textContent = originalText;
            submitButton.classList.remove('opacity-50');
        }, 5000);
    });
});
</script>
{% endblock %}
