from django.urls import path
from . import views

app_name = 'GB_FARM'

urlpatterns = [
    path('', views.home, name='home'),
    path('products/', views.product_list, name='product_list'),
    path('category/<int:category_id>/', views.category_products, name='category_products'),
    path('product/<int:product_id>/', views.product_detail, name='product_detail'),
    path('search/', views.product_search, name='product_search'),
    path('product/<int:product_id>/add-to-cart/', views.add_to_cart, name='add_to_cart'),
    path('product/<int:product_id>/add-to-wishlist/', views.add_to_wishlist, name='add_to_wishlist'),
    path('product/<int:product_id>/remove-from-wishlist/', views.remove_from_wishlist, name='remove_from_wishlist'),
    path('product/<int:product_id>/add-review/', views.add_review, name='add_review'),
    path('review/<int:review_id>/delete/', views.delete_review, name='delete_review'),
    path('order/<int:order_id>/update-status/', views.update_order_status, name='update_order_status'),
    path('cart/', views.cart, name='cart'),
    path('cart/update/<int:item_id>/', views.update_cart, name='update_cart'),
    path('cart/remove/<int:item_id>/', views.remove_from_cart, name='remove_from_cart'),
    path('cart/clear/', views.clear_cart, name='clear_cart'),
    path('calendar/', views.external_calendar_view, name='external_calendar'),
] 