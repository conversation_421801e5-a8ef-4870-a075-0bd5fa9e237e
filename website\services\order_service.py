from django.db import transaction
from django.core.exceptions import ValidationError
from ..models import Order, OrderItem, Cart, Product

class OrderService:
    @staticmethod
    @transaction.atomic
    def create_order(user, shipping_address):
        cart_items = Cart.objects.filter(user=user).select_related('product')
        
        if not cart_items.exists():
            raise ValidationError('Cart is empty')
            
        # Validate stock
        for item in cart_items:
            if item.quantity > item.product.stock:
                raise ValidationError(f'{item.product.name} does not have enough stock')
        
        # Calculate total
        total_amount = sum(item.product.price * item.quantity for item in cart_items)
        
        # Create order
        order = Order.objects.create(
            user=user,
            total_amount=total_amount,
            shipping_address=shipping_address,
            status='pending'
        )
        
        # Create order items and update stock
        for cart_item in cart_items:
            OrderItem.objects.create(
                order=order,
                product=cart_item.product,
                quantity=cart_item.quantity,
                price=cart_item.product.price
            )
            
            product = cart_item.product
            product.stock -= cart_item.quantity
            product.save()
        
        # Clear cart
        cart_items.delete()
        
        return order

    @staticmethod
    def get_user_orders(user):
        return Order.objects.filter(user=user).order_by('-created_at')

    @staticmethod
    def update_order_status(order_id, new_status):
        order = Order.objects.get(id=order_id)
        order.status = new_status
        order.save()
        return order 