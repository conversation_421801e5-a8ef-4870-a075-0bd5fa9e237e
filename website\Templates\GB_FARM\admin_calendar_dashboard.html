{% extends 'GB_FARM/admin_base.html' %}

{% block title %}Calendar Products Dashboard{% endblock %}

{% block admin_content %}
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Calendar Products Dashboard</h1>
                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addProductModal">
                    <i class="fas fa-plus me-2"></i>Add New Product
                </button>
            </div>
            
            <!-- Messages -->
            {% if messages %}
            <div class="alert-container">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Debug Information -->
            {% if request.method == 'POST' %}
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <strong>Debug:</strong> Form submitted! Action: {{ request.POST.action }}, Name: {{ request.POST.name }}, Month: {{ request.POST.month }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endif %}
            
            <!-- Filters -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="month" class="form-label">Filter by Month</label>
                            <select class="form-select" id="month" name="month">
                                <option value="">All Months</option>
                                {% for month_num, month_name in month_choices %}
                                <option value="{{ month_num }}" {% if selected_month == month_num|stringformat:"s" %}selected{% endif %}>
                                    {{ month_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search Product Name</label>
                            <input type="text" class="form-control" id="search" name="q" value="{{ search_query }}" placeholder="Search products...">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter me-1"></i> Apply
                            </button>
                            <a href="{% url 'GB_FARM:admin_calendar_dashboard' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-sync-alt me-1"></i> Reset
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Stats -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">Total Calendar Products</h6>
                                    <h2 class="mt-2 mb-0">{{ total_products }}</h2>
                                </div>
                                <i class="fas fa-calendar-alt fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Calendar Products Table -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Calendar Products</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Product Name</th>
                                    <th>Month</th>
                                    <th>From Date</th>
                                    <th>To Date</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in calendar_products %}
                                <tr>
                                    <td>
                                        <strong>{{ product.name }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ product.get_month_display }}</span>
                                    </td>
                                    <td>{{ product.date_from|date:"M d, Y" }}</td>
                                    <td>{{ product.date_to|date:"M d, Y" }}</td>
                                    <td>{{ product.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-primary me-1" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#editProductModal"
                                                data-id="{{ product.id }}"
                                                data-name="{{ product.name }}"
                                                data-month="{{ product.month }}"
                                                data-date-from="{{ product.date_from|date:'Y-m-d' }}"
                                                data-date-to="{{ product.date_to|date:'Y-m-d' }}">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        <form method="post" style="display: inline;">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="product_id" value="{{ product.id }}">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                    onclick="return confirm('Are you sure you want to delete {{ product.name }}?')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        {% if search_query or selected_month %}
                                            No calendar products found matching your filters.
                                        {% else %}
                                            No calendar products available. <a href="/admin/website/prodcat/add/" target="_blank">Add the first one</a>.
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Pagination -->
                {% if calendar_products.has_other_pages %}
                <div class="card-footer">
                    <nav aria-label="Calendar products pagination">
                        <ul class="pagination justify-content-center mb-0">
                            {% if calendar_products.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ calendar_products.previous_page_number }}{% if selected_month %}&month={{ selected_month }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">Previous</a>
                                </li>
                            {% endif %}
                            
                            {% for num in calendar_products.paginator.page_range %}
                                {% if calendar_products.number == num %}
                                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                {% elif num > calendar_products.number|add:'-3' and num < calendar_products.number|add:'3' %}
                                    <li class="page-item"><a class="page-link" href="?page={{ num }}{% if selected_month %}&month={{ selected_month }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">{{ num }}</a></li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if calendar_products.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ calendar_products.next_page_number }}{% if selected_month %}&month={{ selected_month }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">Next</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductModalLabel">Add New Calendar Product</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="action" value="add">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="productName" class="form-label">Product Name *</label>
                        <input type="text" class="form-control" id="productName" name="name" required maxlength="255" placeholder="Enter product name">
                        <div class="invalid-feedback">
                            Please provide a valid product name.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="productMonth" class="form-label">Month *</label>
                        <select class="form-select" id="productMonth" name="month" required>
                            <option value="">Select Month</option>
                            {% for month_num, month_name in month_choices %}
                            <option value="{{ month_num }}">{{ month_name }}</option>
                            {% endfor %}
                        </select>
                        <div class="invalid-feedback">
                            Please select a month.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="dateFrom" class="form-label">From Date *</label>
                        <input type="date" class="form-control" id="dateFrom" name="date_from" required>
                        <div class="invalid-feedback">
                            Please provide a valid from date.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="dateTo" class="form-label">To Date *</label>
                        <input type="date" class="form-control" id="dateTo" name="date_to" required>
                        <div class="invalid-feedback">
                            Please provide a valid to date.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>Add Product
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Product Modal -->
<div class="modal fade" id="editProductModal" tabindex="-1" aria-labelledby="editProductModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editProductModalLabel">Edit Calendar Product</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="product_id" id="editProductId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editProductName" class="form-label">Product Name *</label>
                        <input type="text" class="form-control" id="editProductName" name="name" required maxlength="255" placeholder="Enter product name">
                        <div class="invalid-feedback">
                            Please provide a valid product name.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editProductMonth" class="form-label">Month *</label>
                        <select class="form-select" id="editProductMonth" name="month" required>
                            <option value="">Select Month</option>
                            {% for month_num, month_name in month_choices %}
                            <option value="{{ month_num }}">{{ month_name }}</option>
                            {% endfor %}
                        </select>
                        <div class="invalid-feedback">
                            Please select a month.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editDateFrom" class="form-label">From Date *</label>
                        <input type="date" class="form-control" id="editDateFrom" name="date_from" required>
                        <div class="invalid-feedback">
                            Please provide a valid from date.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editDateTo" class="form-label">To Date *</label>
                        <input type="date" class="form-control" id="editDateTo" name="date_to" required>
                        <div class="invalid-feedback">
                            Please provide a valid to date.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Product
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// JavaScript to populate edit modal with data
document.addEventListener('DOMContentLoaded', function() {
    var editModal = document.getElementById('editProductModal');
    editModal.addEventListener('show.bs.modal', function (event) {
        var button = event.relatedTarget;
        var id = button.getAttribute('data-id');
        var name = button.getAttribute('data-name');
        var month = button.getAttribute('data-month');
        var dateFrom = button.getAttribute('data-date-from');
        var dateTo = button.getAttribute('data-date-to');

        document.getElementById('editProductId').value = id;
        document.getElementById('editProductName').value = name;
        document.getElementById('editProductMonth').value = month;
        document.getElementById('editDateFrom').value = dateFrom;
        document.getElementById('editDateTo').value = dateTo;
    });

    // Form validation for both add and edit forms
    function validateCalendarForm(form) {
        const name = form.querySelector('input[name="name"]').value.trim();
        const month = form.querySelector('select[name="month"]').value;
        const dateFrom = form.querySelector('input[name="date_from"]').value;
        const dateTo = form.querySelector('input[name="date_to"]').value;
        
        // Check required fields
        if (!name || !month || !dateFrom || !dateTo) {
            alert('All fields are required.');
            return false;
        }
        
        // Validate month
        const monthNum = parseInt(month);
        if (monthNum < 1 || monthNum > 12) {
            alert('Please select a valid month.');
            return false;
        }
        
        // Validate date format and logic
        const fromDate = new Date(dateFrom);
        const toDate = new Date(dateTo);
        
        if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
            alert('Please enter valid dates.');
            return false;
        }
        
        if (fromDate > toDate) {
            alert('From Date cannot be later than To Date.');
            return false;
        }
        
        return true;
    }

    // Add form validation
    const addForm = document.querySelector('#addProductModal form');
    if (addForm) {
        addForm.addEventListener('submit', function(e) {
            if (!validateCalendarForm(this)) {
                e.preventDefault();
                return false;
            }
        });
    }
    
    // Edit form validation
    const editForm = document.querySelector('#editProductModal form');
    if (editForm) {
        editForm.addEventListener('submit', function(e) {
            if (!validateCalendarForm(this)) {
                e.preventDefault();
                return false;
            }
        });
    }
    
    // Reset form when add modal is closed
    document.getElementById('addProductModal').addEventListener('hidden.bs.modal', function () {
        const form = this.querySelector('form');
        form.reset();
    });
});
</script>

<style>
    .bg-primary-dark {
        background-color: rgba(0, 0, 0, 0.15);
    }
    .bg-success-dark {
        background-color: rgba(0, 0, 0, 0.15);
    }
    .bg-info-dark {
        background-color: rgba(0, 0, 0, 0.15);
    }
    .bg-warning-dark {
        background-color: rgba(0, 0, 0, 0.15);
    }
</style>

{% endblock %} 