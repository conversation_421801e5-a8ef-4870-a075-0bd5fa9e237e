# Generated by Django 4.2.20 on 2025-06-23 14:02

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0027_externalfooter'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='externalfooter',
            name='newsletter_description',
        ),
        migrations.RemoveField(
            model_name='externalfooter',
            name='newsletter_description_ar',
        ),
        migrations.RemoveField(
            model_name='externalfooter',
            name='newsletter_title',
        ),
        migrations.RemoveField(
            model_name='externalfooter',
            name='newsletter_title_ar',
        ),
        migrations.RemoveField(
            model_name='externalfooter',
            name='phone_number',
        ),
        migrations.AddField(
            model_name='externalfooter',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='externalfooter',
            name='phone',
            field=models.CharField(default=django.utils.timezone.now, help_text='e.g. +2035392341 / +20109931930', max_length=100),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='externalfooter',
            name='address',
            field=models.CharField(help_text='e.g. 16 Hussein Wassel St, Maadi, Dokki, Egypt', max_length=255),
        ),
        migrations.AlterField(
            model_name='externalfooter',
            name='address_ar',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Address'),
        ),
        migrations.AlterField(
            model_name='externalfooter',
            name='copyright_text',
            field=models.CharField(default='ALL RIGHTS RESERVED © GB FARMS 2023', max_length=255),
        ),
        migrations.AlterField(
            model_name='externalfooter',
            name='email',
            field=models.EmailField(max_length=254),
        ),
        migrations.AlterField(
            model_name='externalfooter',
            name='facebook_url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='externalfooter',
            name='instagram_url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='externalfooter',
            name='youtube_url',
            field=models.URLField(blank=True, null=True),
        ),
    ]
