#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.test import Client
from website.models import NewsletterSubscription

def test_footer_on_pages():
    """Test newsletter subscription form on different pages"""
    print("🧪 Testing Footer Newsletter Form on Different Pages...")
    print("=" * 60)
    
    client = Client()
    
    # List of external pages to test
    pages_to_test = [
        ('/', 'Home Page'),
        ('/external-about/', 'About Page'),
        ('/external-contact_us/', 'Contact Page'),
        ('/external-career/', 'Career Page'),
        ('/external-all-products/', 'All Products Page'),
    ]
    
    for url, page_name in pages_to_test:
        print(f"\n📄 Testing {page_name} ({url})...")
        
        try:
            # Test GET request to load the page
            response = client.get(url)
            print(f"   ✅ Page loads successfully (Status: {response.status_code})")
            
            # Check if footer form is present
            content = response.content.decode('utf-8')
            if 'subscribe_newsletter' in content:
                print("   ✅ Newsletter form found in footer")
            else:
                print("   ❌ Newsletter form NOT found in footer")
                
            if 'YOUR MAIL' in content:
                print("   ✅ Email input placeholder found")
            else:
                print("   ❌ Email input placeholder NOT found")
                
            if 'SUBSCRIBE' in content:
                print("   ✅ Subscribe button found")
            else:
                print("   ❌ Subscribe button NOT found")
                
        except Exception as e:
            print(f"   ❌ Error testing {page_name}: {e}")
    
    # Test actual form submission
    print(f"\n📝 Testing Form Submission...")
    
    try:
        # Clean up any existing test subscriptions
        NewsletterSubscription.objects.filter(email='<EMAIL>').delete()
        
        # Test form submission
        response = client.post('/subscribe_newsletter', {
            'email': '<EMAIL>',
            'source_page': 'external_index'
        })
        
        print(f"   ✅ Form submission status: {response.status_code}")
        
        # Check if subscription was created
        subscription = NewsletterSubscription.objects.filter(email='<EMAIL>').first()
        if subscription:
            print(f"   ✅ Subscription created: {subscription.email}")
            print(f"   ✅ Source page: {subscription.source_page}")
            print(f"   ✅ Is active: {subscription.is_active}")
            
            # Clean up
            subscription.delete()
            print("   ✅ Test subscription cleaned up")
        else:
            print("   ❌ Subscription was NOT created")
            
    except Exception as e:
        print(f"   ❌ Form submission test failed: {e}")
    
    # Test duplicate email handling
    print(f"\n🔄 Testing Duplicate Email Handling...")
    
    try:
        # Create initial subscription
        subscription1 = NewsletterSubscription.objects.create(
            email='<EMAIL>',
            source_page='external_index'
        )
        print("   ✅ Initial subscription created")
        
        # Try to create duplicate
        response = client.post('/subscribe_newsletter', {
            'email': '<EMAIL>',
            'source_page': 'external_about'
        })
        
        print(f"   ✅ Duplicate submission status: {response.status_code}")
        
        # Check that only one subscription exists
        count = NewsletterSubscription.objects.filter(email='<EMAIL>').count()
        if count == 1:
            print("   ✅ Duplicate handling works correctly (only 1 subscription)")
        else:
            print(f"   ❌ Duplicate handling failed ({count} subscriptions found)")
        
        # Clean up
        NewsletterSubscription.objects.filter(email='<EMAIL>').delete()
        print("   ✅ Test subscriptions cleaned up")
        
    except Exception as e:
        print(f"   ❌ Duplicate email test failed: {e}")
    
    print("\n" + "=" * 60)
    print("✅ Footer newsletter form testing completed!")

if __name__ == "__main__":
    test_footer_on_pages()
