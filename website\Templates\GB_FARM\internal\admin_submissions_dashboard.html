{% extends 'GB_FARM/admin_base.html' %}
{% load static %}

{% block title %}Contact Submissions | Admin Dashboard{% endblock %}

{% block admin_content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Contact Form Submissions</h6>
                        </div>
                        <div class="col-md-6 text-end">
                            <form method="get" class="d-flex">
                                {% csrf_token %}
                                <input type="text" name="q" value="{{ search_query }}" class="form-control me-2" placeholder="Search submissions...">
                                <select name="status" class="form-select me-2" style="width: auto;">
                                    <option value="">All Submissions</option>
                                    <option value="processed" {% if selected_status == 'processed' %}selected{% endif %}>Processed</option>
                                    <option value="unprocessed" {% if selected_status == 'unprocessed' %}selected{% endif %}>Unprocessed</option>
                                </select>
                                <button type="submit" class="btn btn-primary">Filter</button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="row mb-4 px-4 pt-3">
                        <div class="col-md-4">
                            <div class="card bg-gradient-info">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Submissions</p>
                                                <h5 class="font-weight-bolder mb-0 text-black">
                                                    {{ total_submissions }}
                                                </h5>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-white shadow text-center border-radius-md">
                                                <i class="ni ni-email-83 text-info text-lg opacity-10" aria-hidden="true"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-gradient-success">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Processed</p>
                                                <h5 class="font-weight-bolder mb-0 text-black">
                                                    {{ processed_submissions }}
                                                </h5>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-white shadow text-center border-radius-md">
                                                <i class="ni ni-check-bold text-success text-lg opacity-10" aria-hidden="true"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-gradient-warning">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Pending</p>
                                                <h5 class="font-weight-bolder mb-0 text-black">
                                                    {{ unprocessed_submissions }}
                                                </h5>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-white shadow text-center border-radius-md">
                                                <i class="ni ni-time-alarm text-warning text-lg opacity-10" aria-hidden="true"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Sender</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Company</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Email</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Business Type</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Source</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Date</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Status</th>
                                    <th class="text-secondary opacity-7"></th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for submission in submissions %}
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ submission.full_name }}</h6>
                                                {% if submission.phone_number %}
                                                <p class="text-xs text-secondary mb-0">{{ submission.phone_number }}</p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">{{ submission.company_name|default:"Not provided" }}</p>
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">{{ submission.email }}</p>
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">{{ submission.get_business_type_display|default:"Not provided" }}</p>
                                    </td>
                                    <td>
                                        <span class="badge badge-sm bg-gradient-primary">{{ submission.get_source_page_display|default:submission.source_page }}</span>
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">{{ submission.created_at|date:"M d, Y" }}</p>
                                        <p class="text-xs text-secondary mb-0">{{ submission.created_at|time:"H:i" }}</p>
                                    </td>
                                    <td>
                                        {% if submission.is_processed %}
                                        <span class="badge badge-sm bg-gradient-success">Processed</span>
                                        {% else %}
                                        <span class="badge badge-sm bg-gradient-warning">Pending</span>
                                        {% endif %}
                                    </td>
                                    <td class="align-middle">
                                        <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewModal{{ submission.id }}">
                                            View
                                        </button>
                                        {% if not submission.is_processed %}
                                        <a href="{% url 'GB_FARM:mark_submission_processed' submission.id %}" class="btn btn-sm btn-success">
                                            Mark Processed
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>

                                <!-- View Modal -->
                                <div class="modal fade" id="viewModal{{ submission.id }}" tabindex="-1" aria-labelledby="viewModalLabel{{ submission.id }}" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="viewModalLabel{{ submission.id }}">Contact Submission Details</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p><strong>Name:</strong> {{ submission.full_name }}</p>
                                                        <p><strong>Email:</strong> {{ submission.email }}</p>
                                                        <p><strong>Date:</strong> {{ submission.created_at }}</p>
                                                        <p><strong>Source:</strong> {{ submission.get_source_page_display|default:submission.source_page }}</p>
                                                        <p><strong>IP Address:</strong> {{ submission.ip_address }}</p>
                                                        <p><strong>Status:</strong> 
                                                            {% if submission.is_processed %}
                                                            <span class="badge bg-success">Processed</span>
                                                            {% else %}
                                                            <span class="badge bg-warning">Pending</span>
                                                            {% endif %}
                                                        </p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p><strong>Company:</strong> {{ submission.company_name|default:"Not provided" }}</p>
                                                        <p><strong>Phone:</strong> {{ submission.phone_number|default:"Not provided" }}</p>
                                                        <p><strong>Subject:</strong> {{ submission.subject|default:"Not provided" }}</p>
                                                        <p><strong>Business Type:</strong> {{ submission.get_business_type_display|default:"Not provided" }}</p>
                                                        <p><strong>How they found us:</strong> {{ submission.get_find_out_method_display|default:"Not provided" }}</p>
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-12">
                                                        <div class="card">
                                                            <div class="card-header bg-light">
                                                                <h6 class="mb-0">Message</h6>
                                                            </div>
                                                            <div class="card-body">
                                                                <p style="white-space: pre-wrap;">{{ submission.message }}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                {% if not submission.is_processed %}
                                                <a href="{% url 'GB_FARM:mark_submission_processed' submission.id %}" class="btn btn-success">
                                                    Mark as Processed
                                                </a>
                                                {% endif %}
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <p class="text-sm mb-0">No submissions found.</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <!-- Pagination -->
                    {% if submissions.has_other_pages %}
                    <div class="px-4 py-3">
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if submissions.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ submissions.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                                
                                {% for num in submissions.paginator.page_range %}
                                    {% if submissions.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                    {% elif num > submissions.number|add:'-3' and num < submissions.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}">{{ num }}</a>
                                    </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if submissions.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ submissions.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ submissions.paginator.num_pages }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 