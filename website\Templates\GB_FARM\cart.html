{% extends 'GB_FARM/base.html' %}
{% load static %}
{% load custom_filters %}
{% load i18n %}

{% block title %}{% trans "Shopping Cart" %}{% endblock %}

{% block extra_css %}
<style>
    .cart-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .cart-table {
        width: 100%;
        overflow-x: auto;
    }
    
    .product-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .product-image {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .product-image-placeholder {
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        border-radius: 8px;
    }
    
    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 5px;
    }
    
    .action-buttons .btn {
        white-space: nowrap;
    }
    
    /* Card glass effect for cart items */
    .card-glass {
        background: rgba(255, 255, 255, 0.85);
        backdrop-filter: blur(5px);
        border-radius: 1rem;
        overflow: hidden;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .card-glass:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.95);
    }
    
    /* Empty cart styling */
    .empty-cart {
        text-align: center;
        padding: 40px 20px;
    }
    
    .empty-cart i {
        font-size: 48px;
        color: #6c757d;
        margin-bottom: 20px;
    }
    
    /* Override main container styles for this page */
    main.container {
        background: rgba(255, 255, 255, 0.45);
        backdrop-filter: blur(3px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border-radius: 20px;
        margin: 2rem auto;
        padding: 2rem;
        position: relative;
        z-index: 1;
    }
    
    @media (max-width: 768px) {
        .cart-table {
            display: block;
            overflow-x: auto;
        }
        
        .table th, .table td {
            white-space: nowrap;
        }
        
        .product-image, .product-image-placeholder {
            width: 60px;
            height: 60px;
        }
        
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
    }

    /* Loading spinner */
    .loading {
        pointer-events: none;
        opacity: 0.7;
    }

    .spinner-border {
        width: 1rem;
        height: 1rem;
        margin-right: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="cart-container">
    <h1 class="mb-4">{% trans "Shopping Cart" %}</h1>
    
    <!-- Messages Container: There should be only ONE of these blocks -->
    {% if messages %}
    <div class="messages mb-4" id="messages-container">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    {% if cart_items %}
    <div class="card-glass mb-4">
        <div class="card-body">
            <div class="cart-table">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th scope="col" width="40%">{% trans "Product" %}</th>
                            <th scope="col" width="15%">{% trans "Price" %}</th>
                            <th scope="col" width="20%">{% trans "Quantity" %}</th>
                            <th scope="col" width="15%">{% trans "Subtotal" %}</th>
                            <th scope="col" width="10%">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in cart_items %}
                        <tr data-item-id="{{ item.id }}" data-price="{{ item.product.price }}" data-max-stock="{{ item.product.stock }}">
                            <td>
                                <div class="product-info">
                                    {% if item.product.image %}
                                    <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="product-image">
                                    {% else %}
                                    <div class="product-image-placeholder">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                    {% endif %}
                                    <div>
                                        <a href="{% url 'GB_FARM:product_detail' item.product.id %}" class="text-decoration-none fw-bold">{{ item.product.name }}</a>
                                        {% if item.product.category %}
                                        <div><small class="text-muted">{{ item.product.category.name }}</small></div>
                                        {% endif %}
                                        {% if item.selected_weight %}
                                        <div><small class="badge bg-info">{{ item.selected_weight }}</small></div>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>{% trans "EGP" %} {{ item.price_override|default:item.product.price|floatformat:2 }}</td>
                            <td>
                                <form method="post" action="{% url 'GB_FARM:update_cart' item.id %}" class="quantity-controls">
                                    {% csrf_token %}
                                    <div class="input-group">
                                        <button type="button" class="btn btn-outline-secondary btn-sm quantity-decrease">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" name="quantity" value="{{ item.quantity }}" min="1" max="{{ item.product.stock }}" 
                                               class="form-control form-control-sm text-center quantity-input">
                                        <button type="button" class="btn btn-outline-secondary btn-sm quantity-increase">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </form>
                            </td>
                            <td class="subtotal">{{ item.total_price|floatformat:2 }}</td>
                            <td>
                                <form method="post" action="{% url 'GB_FARM:remove_from_cart' item.id %}" class="action-buttons">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="3" class="text-end fw-bold">{% trans "Total:" %}</td>
                            <td id="cart-total" class="fw-bold">{% trans "EGP" %} {{ total|floatformat:2 }}</td>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-4 mb-3">
            <a href="{% url 'GB_FARM:product_list' %}" class="btn btn-outline-secondary w-100">
                <i class="fas fa-arrow-left"></i> {% trans "Continue Shopping" %}
            </a>
        </div>
        <div class="col-md-4 mb-3">
            <form action="{% url 'GB_FARM:clear_cart' %}" method="POST">
                {% csrf_token %}
                <button type="submit" class="btn btn-outline-danger w-100">
                    <i class="fas fa-trash"></i> {% trans "Clear Cart" %}
                </button>
            </form>
        </div>
        <div class="col-md-4 mb-3">
            <a href="{% url 'GB_FARM:checkout' %}" class="btn btn-success w-100">
                <i class="fas fa-shopping-bag"></i> {% trans "Continue to Checkout" %}
            </a>
        </div>
    </div>
    
    {% else %}
    <div class="card-glass empty-cart">
        <div class="card-body text-center">
            <i class="fas fa-shopping-cart"></i>
            <h3 class="mt-3">{% trans "Your cart is empty" %}</h3>
            <p class="text-muted">{% trans "Add some products to your cart to proceed with checkout." %}</p>
            <a href="{% url 'GB_FARM:product_list' %}" class="btn btn-primary mt-3">
                <i class="fas fa-shopping-basket"></i> {% trans "Start shopping now" %}
            </a>
        </div>
    </div>
    {% endif %}
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Format currency function
        function formatCurrency(amount) {
            return 'EGP ' + parseFloat(amount).toFixed(2);
        }
        
        // Update cart totals
        function updateCartTotals() {
            let cartTotal = 0;
            document.querySelectorAll('tbody tr').forEach(row => {
                const subtotal = parseFloat(row.querySelector('.subtotal').textContent);
                cartTotal += subtotal;
            });
            
            document.getElementById('cart-total').textContent = formatCurrency(cartTotal);
        }
        
        // Quantity selector functionality
        const decreaseBtns = document.querySelectorAll('.quantity-decrease');
        const increaseBtns = document.querySelectorAll('.quantity-increase');
        
        // Disable form auto-submission
        document.querySelectorAll('.quantity-controls').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
            });
        });
        
        // Track if an AJAX request is in progress
        let updateInProgress = false;
        
        decreaseBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                if (updateInProgress) return; // Prevent multiple clicks during update
                
                const input = this.closest('.input-group').querySelector('.quantity-input');
                let value = parseInt(input.value);
                if (value > 0) { // Allow zero to trigger removal
                    value -= 1;
                    input.value = value;
                    updateInProgress = true;
                    updateCartItemAjax(this.closest('form'), value);
                }
            });
        });
        
        increaseBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                if (updateInProgress) return; // Prevent multiple clicks during update
                
                const input = this.closest('.input-group').querySelector('.quantity-input');
                let value = parseInt(input.value);
                const maxStock = parseInt(input.getAttribute('max'));
                if (value < maxStock) {
                    value += 1;
                    input.value = value;
                    updateInProgress = true;
                    updateCartItemAjax(this.closest('form'), value);
                } else {
                    showToast('Warning', `Only ${maxStock} units available in stock`);
                }
            });
        });
        
        // Also handle manual input changes
        document.querySelectorAll('.quantity-input').forEach(input => {
            input.addEventListener('change', function() {
                if (updateInProgress) return; // Prevent conflicting updates
                
                const value = parseInt(this.value);
                updateInProgress = true;
                updateCartItemAjax(this.closest('form'), value);
            });
        });
        
        // Function to show toast messages
        function showToast(title, message) {
            // Check if we can use Bootstrap toast
            if (typeof bootstrap !== 'undefined') {
                const toastEl = document.createElement('div');
                toastEl.className = 'toast align-items-center text-white bg-primary border-0';
                toastEl.setAttribute('role', 'alert');
                toastEl.setAttribute('aria-live', 'assertive');
                toastEl.setAttribute('aria-atomic', 'true');
                
                toastEl.innerHTML = `
                    <div class="d-flex">
                      <div class="toast-body">
                        ${message}
                      </div>
                      <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                `;
                
                document.body.appendChild(toastEl);
                const toast = new bootstrap.Toast(toastEl);
                toast.show();
                
                // Remove after hiding
                toastEl.addEventListener('hidden.bs.toast', function() {
                    toastEl.remove();
                });
            } else {
                // Fallback to alert
                alert(message);
            }
        }
        
        // Function to update cart via AJAX
        function updateCartItemAjax(form, quantity) {
            const url = form.action;
            const row = form.closest('tr');
            const csrfToken = form.querySelector('input[name="csrfmiddlewaretoken"]').value;
            
            // Disable inputs during update
            const inputs = form.querySelectorAll('input, button');
            inputs.forEach(input => input.disabled = true);
            
            // Show loading indicator
            const quantityCell = form.closest('td');
            const originalContent = quantityCell.innerHTML;
            quantityCell.innerHTML = '<div class="spinner-border spinner-border-sm text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
            
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': csrfToken
                },
                body: `quantity=${quantity}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.removed) {
                        // Item was removed (quantity was 0)
                        row.remove();
                        showToast('Success', data.message);
                        
                        // Update cart total
                        document.getElementById('cart-total').textContent = formatCurrency(data.cart_total);
                        
                        // Always reload page after item removal or quantity update for full refresh
                        window.location.reload();
                    } else {
                        // Update the row values
                        row.querySelector('.subtotal').textContent = data.item_total.toFixed(2);
                        updateCartTotals();
                        
                        // Instead of replacing the entire HTML, just update the value
                        const input = form.querySelector('.quantity-input');
                        input.value = data.quantity;
                        input.disabled = false;
                        
                        // Re-enable the buttons
                        const decreaseBtn = form.querySelector('.quantity-decrease');
                        const increaseBtn = form.querySelector('.quantity-increase');
                        decreaseBtn.disabled = false;
                        increaseBtn.disabled = false;

                        // Always reload page after item removal or quantity update for full refresh
                        window.location.reload();
                    }
                } else {
                    // Error occurred
                    showToast('Error', data.message);
                    
                    // Restore quantity cell with all event listeners intact
                    const input = form.querySelector('.quantity-input');
                    input.value = data.max_quantity ? Math.min(quantity, data.max_quantity) : quantity;
                    
                    // Re-enable the inputs
                    inputs.forEach(input => input.disabled = false);
                }
                
                // Reset update flag
                updateInProgress = false;
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Error', 'Failed to update cart. Please try again.');
                
                // Re-enable the inputs without replacing HTML
                inputs.forEach(input => input.disabled = false);
                
                // Reset update flag
                updateInProgress = false;
            });
        }
        
        // Handle remove buttons with AJAX
        document.querySelectorAll('.action-buttons').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const button = this.querySelector('button[type="submit"]');
                const row = this.closest('tr');
                const url = this.action;
                const csrfToken = this.querySelector('input[name="csrfmiddlewaretoken"]').value;
                
                // Disable button and show spinner
                if (button) {
                    button.disabled = true;
                    button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
                }
                
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken
                    }
                })
                .then(response => {
                    if (response.ok) {
                        // Remove the row with animation
                        row.style.opacity = '0';
                        row.style.transition = 'opacity 0.3s ease';
                        
                        setTimeout(() => {
                            row.remove();
                            updateCartTotals();
                            
                            // Always reload page after item removal or quantity update for full refresh
                            window.location.reload();
                        }, 300);
                        
                        showToast('Success', 'Item removed from cart');
                    } else {
                        throw new Error('Failed to remove item');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('Error', 'Failed to remove item. Please try again.');
                    
                    // Reset button
                    if (button) {
                        button.disabled = false;
                        button.innerHTML = '<i class="fas fa-trash-alt"></i>';
                    }
                });
            });
        });
    });
</script>
{% endblock %}
{% endblock %}
