{% extends 'GB_FARM/base.html' %}
{% load static %}

{% block title %}Payment Successful{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg border-0">
                <div class="card-body p-5 text-center">
                    <div class="mb-4">
                        <div class="success-checkmark">
                            <div class="check-icon">
                                <span class="icon-line line-tip"></span>
                                <span class="icon-line line-long"></span>
                            </div>
                        </div>
                    </div>
                    
                    <h1 class="display-5 fw-bold mb-4 text-success">Payment Successful!</h1>
                    <p class="lead mb-4">Thank you for your order. Your payment has been processed successfully.</p>
                    
                    <div class="payment-details mb-4">
                        <h5 class="mb-3">Payment Details</h5>
                        <div class="row">
                            <div class="col-6 text-start border-end">
                                <p class="mb-1"><strong>Order ID:</strong></p>
                                <p class="mb-1"><strong>Subtotal:</strong></p>
                                <p class="mb-1"><strong>Delivery Fee:</strong></p>
                                <p class="mb-1"><strong>Total Amount:</strong></p>
                                <p class="mb-1"><strong>Payment Method:</strong></p>
                                <p class="mb-1"><strong>Status:</strong></p>
                                {% if payment.transaction_id %}
                                <p class="mb-1"><strong>Transaction ID:</strong></p>
                                {% endif %}
                                <p class="mb-1"><strong>Date:</strong></p>
                            </div>
                            <div class="col-6 text-start">
                                <p class="mb-1">#{{ order.id }}</p>
                                <p class="mb-1">EGP {{ payment.amount|add:"-5"|floatformat:2 }}</p>
                                <p class="mb-1">EGP 5.00 <i class="fas fa-truck text-muted ms-1"></i></p>
                                <p class="mb-1">EGP {{ payment.amount|floatformat:2 }}</p>
                                <p class="mb-1">
                                    {% if payment.payment_method == 'credit_card' %}
                                    <i class="fas fa-credit-card text-primary me-1"></i>
                                    {% elif payment.payment_method == 'cash' %}
                                    <i class="fas fa-money-bill-wave text-success me-1"></i>
                                    {% elif payment.payment_method == 'instapay' %}
                                    <i class="fas fa-mobile-alt text-info me-1"></i>
                                    {% endif %}
                                    {{ payment.get_payment_method_display }}
                                </p>
                                <p class="mb-1">{{ payment.get_status_display }}</p>
                                {% if payment.transaction_id %}
                                <p class="mb-1">{{ payment.transaction_id }}</p>
                                {% endif %}
                                <p class="mb-1">{{ payment.timestamp|date:"M d, Y H:i" }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <p>Your order is now being processed. You can track your order status anytime.</p>
                    </div>
                    
                    <div class="d-grid gap-3 d-sm-flex justify-content-sm-center">
                        <a href="{% url 'GB_FARM:order_details' order.id %}" class="btn btn-primary btn-lg px-4 me-sm-3">View Order Details</a>
                        <a href="{% url 'GB_FARM:home' %}" class="btn btn-outline-success btn-lg px-4">Continue Shopping</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.success-checkmark {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    position: relative;
}
.success-checkmark .check-icon {
    width: 80px;
    height: 80px;
    position: relative;
    border-radius: 50%;
    box-sizing: content-box;
    border: 4px solid #28a745;
}
.success-checkmark .check-icon::before {
    top: 3px;
    left: -2px;
    width: 30px;
    transform-origin: 100% 50%;
    border-radius: 100px 0 0 100px;
}
.success-checkmark .check-icon::after {
    top: 0;
    left: 30px;
    width: 60px;
    transform-origin: 0 50%;
    border-radius: 0 100px 100px 0;
    animation: rotate-circle 4.25s ease-in;
}
.success-checkmark .check-icon::before, .success-checkmark .check-icon::after {
    content: '';
    height: 100px;
    position: absolute;
    background: #FFFFFF;
    transform: rotate(-45deg);
}
.success-checkmark .check-icon .icon-line {
    height: 5px;
    background-color: #28a745;
    display: block;
    border-radius: 2px;
    position: absolute;
    z-index: 10;
}
.success-checkmark .check-icon .icon-line.line-tip {
    top: 46px;
    left: 14px;
    width: 25px;
    transform: rotate(45deg);
    animation: icon-line-tip 0.75s;
}
.success-checkmark .check-icon .icon-line.line-long {
    top: 38px;
    right: 8px;
    width: 47px;
    transform: rotate(-45deg);
    animation: icon-line-long 0.75s;
}
@keyframes icon-line-tip {
    0% {
        width: 0;
        left: 1px;
        top: 19px;
    }
    54% {
        width: 0;
        left: 1px;
        top: 19px;
    }
    70% {
        width: 50px;
        left: -8px;
        top: 37px;
    }
    84% {
        width: 17px;
        left: 21px;
        top: 48px;
    }
    100% {
        width: 25px;
        left: 14px;
        top: 45px;
    }
}
@keyframes icon-line-long {
    0% {
        width: 0;
        right: 46px;
        top: 54px;
    }
    65% {
        width: 0;
        right: 46px;
        top: 54px;
    }
    84% {
        width: 55px;
        right: 0px;
        top: 35px;
    }
    100% {
        width: 47px;
        right: 8px;
        top: 38px;
    }
}
</style>
{% endblock %} 