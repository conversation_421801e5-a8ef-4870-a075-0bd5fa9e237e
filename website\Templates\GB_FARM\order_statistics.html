{% extends 'GB_FARM/base.html' %}

{% block title %}Order Statistics{% endblock %}

{% block content %}
<div class="container-fluid my-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Order Statistics</h1>
        <div>
            <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="btn btn-outline-secondary">Back to Orders</a>
        </div>
    </div>
    
    {% if messages %}
    <div class="messages mb-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-white-50">Total Orders</h6>
                            <h3 class="mb-0">{{ total_orders }}</h3>
                        </div>
                        <div>
                            <i class="bi bi-box fs-1 text-white-50"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="badge bg-light text-primary">
                            Last 30 days
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-white-50">Total Revenue</h6>
                            <h3 class="mb-0">EGP{{ total_revenue|floatformat:2 }}</h3>
                        </div>
                        <div>
                            <i class="bi bi-cash-stack fs-1 text-white-50"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="badge bg-light text-success">
                            Last 30 days
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-white-50">Average Order Value</h6>
                            <h3 class="mb-0">EGP{{ average_order_value|floatformat:2 }}</h3>
                        </div>
                        <div>
                            <i class="bi bi-currency-dollar fs-1 text-white-50"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="badge bg-light text-info">
                            Last 30 days
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-white-50">Pending Orders</h6>
                            <h3 class="mb-0">{{ pending_orders }}</h3>
                        </div>
                        <div>
                            <i class="bi bi-hourglass-split fs-1 text-white-50"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="badge bg-light text-warning">
                            Requires Attention
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card mb-4 h-100">
                <div class="card-header">
                    <h5 class="mb-0">Orders by Status</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Status</th>
                                    <th>Count</th>
                                    <th>Percentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for status in status_counts %}
                                <tr>
                                    <td>
                                        <span class="badge bg-{% if status.status == 'delivered' %}success{% elif status.status == 'cancelled' %}danger{% elif status.status == 'shipped' %}info{% else %}warning{% endif %}">
                                            {{ status.display }}
                                        </span>
                                    </td>
                                    <td>{{ status.count }}</td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar bg-{% if status.status == 'delivered' %}success{% elif status.status == 'cancelled' %}danger{% elif status.status == 'shipped' %}info{% else %}warning{% endif %}"
                                                role="progressbar"
                                                style="width: {{ status.percentage }}%"
                                                aria-valuenow="{{ status.percentage }}"
                                                aria-valuemin="0"
                                                aria-valuemax="100">
                                                {{ status.percentage|floatformat:1 }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card mb-4 h-100">
                <div class="card-header">
                    <h5 class="mb-0">Top Selling Products</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Quantity Sold</th>
                                    <th>Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in top_products %}
                                <tr>
                                    <td>{{ product.name }}</td>
                                    <td>{{ product.quantity }}</td>
                                    <td>EGP{{ product.revenue|floatformat:2 }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="3" class="text-center">No product data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Daily Orders (Last 30 Days)</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="height: 300px;">
                        <canvas id="dailyOrdersChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Data for daily orders chart
        const labels = [];
        const orderCounts = [];
        const orderRevenue = [];
        
        {% for day in daily_orders %}
            labels.push("{{ day.date|date:'M d' }}");
            orderCounts.push({{ day.count|floatformat:0 }});
            orderRevenue.push({{ day.revenue|floatformat:2 }});
        {% endfor %}
        
        // Create the daily orders chart
        const dailyOrdersChart = new Chart(
            document.getElementById('dailyOrdersChart'),
            {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Number of Orders',
                            data: orderCounts,
                            backgroundColor: 'rgba(54, 162, 235, 0.5)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Revenue (EGP)',
                            data: orderRevenue,
                            type: 'line',
                            fill: false,
                            backgroundColor: 'rgba(255, 99, 132, 0.5)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 2,
                            tension: 0.1,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Number of Orders'
                            }
                        },
                        y1: {
                            beginAtZero: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false
                            },
                            title: {
                                display: true,
                                text: 'Revenue (EGP)'
                            }
                        }
                    }
                }
            });
    });
</script>
{% endblock %}

{% block extra_scripts %}
{% endblock %}