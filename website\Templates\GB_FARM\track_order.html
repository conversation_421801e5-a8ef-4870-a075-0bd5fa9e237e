{% extends 'GB_FARM/base.html' %}
{% load static %}
{% load custom_filters %}
{% load i18n %}

{% block title %}Track Order #{{ order.id }}{% endblock %}

{% block extra_css %}
<style>
    .product-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .product-image-small {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .product-image-small-placeholder {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f0f0f0;
        border-radius: 4px;
    }
    /* Ensure table cells don't wrap too much */
    .table th, .table td {
        white-space: nowrap;
        vertical-align: middle;
    }
    /* Adjust padding for cleaner look */
    .table > :not(caption) > * > * {
        padding: 0.75rem 0.5rem;
    }

    @media (max-width: 768px) {
        .table th, .table td {
            white-space: normal;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-white">
                    <h4 class="mb-0">Track Order #{{ order.id }}</h4>
                    <p class="text-muted mb-0">Placed on {{ order.created_at|date:"F j, Y" }}</p>
                </div>
                <div class="card-body">
                    <!-- Order Timeline -->
                    <div class="order-timeline position-relative pb-5">
                        <!-- Order Placed -->
                        <div class="timeline-item {% if order.status != 'pending' %}completed{% endif %} mb-4">
                            <div class="d-flex">
                                <div class="timeline-icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="timeline-content ms-3">
                                    <h5 class="mb-1">Order Placed</h5>
                                    <p class="text-muted mb-0">{{ order.created_at|date:"F j, Y, g:i a" }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Payment -->
                        <div class="timeline-item {% if order.status == 'paid' or order.status == 'processing' or order.status == 'shipped' or order.status == 'delivered' %}completed{% endif %} mb-4">
                            <div class="d-flex">
                                <div class="timeline-icon">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                                <div class="timeline-content ms-3">
                                    <h5 class="mb-1">Payment</h5>
                                    {% if order.payment_date %}
                                        <p class="text-muted mb-0">{{ order.payment_date|date:"F j, Y, g:i a" }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Processing -->
                        <div class="timeline-item {% if order.status == 'processing' or order.status == 'shipped' or order.status == 'delivered' %}completed{% endif %} mb-4">
                            <div class="d-flex">
                                <div class="timeline-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="timeline-content ms-3">
                                    <h5 class="mb-1">Processing</h5>
                                    {% if order.processing_date %}
                                        <p class="text-muted mb-0">{{ order.processing_date|date:"F j, Y, g:i a" }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Shipped -->
                        <div class="timeline-item {% if order.status == 'shipped' or order.status == 'delivered' %}completed{% endif %} mb-4">
                            <div class="d-flex">
                                <div class="timeline-icon">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <div class="timeline-content ms-3">
                                    <h5 class="mb-1">Shipped</h5>
                                    {% if order.shipping_date %}
                                        <p class="text-muted mb-0">{{ order.shipping_date|date:"F j, Y, g:i a" }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Delivered -->
                        <div class="timeline-item {% if order.status == 'delivered' %}completed{% endif %}">
                            <div class="d-flex">
                                <div class="timeline-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="timeline-content ms-3">
                                    <h5 class="mb-1">Delivered</h5>
                                    {% if order.delivery_date %}
                                        <p class="text-muted mb-0">{{ order.delivery_date|date:"F j, Y, g:i a" }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.order-timeline {
    position: relative;
}

.order-timeline::before {
    content: '';
    position: absolute;
    left: 16px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
    z-index: 1;
}

.timeline-item {
    position: relative;
    z-index: 2;
}

.timeline-icon {
    width: 35px;
    height: 35px;
    background: #e9ecef;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.timeline-item.completed .timeline-icon {
    background: #28a745;
    color: white;
}

.timeline-content {
    flex: 1;
}
</style>
{% endblock %} 