{% extends 'GB_FARM/base.html' %}
{% load static %}
{% load i18n %}
{% load custom_filters %}

{% block title %}{% trans "Home" %}{% endblock %}

{% block extra_css %}
<style>
/* Hero Carousel Enhancements */
.carousel-item {
    height: 85vh;
    min-height: 600px;
    background: no-repeat center center scroll;
    background-size: cover;
    position: relative;
    transition: transform 0.5s ease;
}

.carousel-item:hover {
    transform: scale(1.02);
}

.carousel-overlay {
    background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.8));
}

.carousel-caption {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    animation: fadeInUp 1s ease;
}

/* Welcome Section Enhancements */
.welcome-section {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 30px;
    padding: 4rem;
    margin: 4rem 0;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.welcome-section:hover {
    transform: translateY(-5px);
}

.feature-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 2rem;
    margin: 1rem 0;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.feature-card i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
    transition: transform 0.3s ease;
}

.feature-card:hover i {
    transform: scale(1.2);
}

/* Categories Section Enhancements */
.categories-section {
    padding: 5rem 0;
}

.category-card {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    height: 100%;
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.category-card img {
    height: 250px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.category-card:hover img {
    transform: scale(1.1);
}

.category-default-image {
    height: 200px;
    background-image: url('{% static "vegetables.jpg" %}');
    background-size: cover;
    background-position: center;
}

/* Featured Products Enhancements */
.featured-products {
    padding: 5rem 0;
}

.product-card {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.product-card img {
    height: 250px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover img {
    transform: scale(1.1);
}

.category-badge {
    background: rgba(255, 255, 255, 0.9);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Why Choose Us Section Enhancements */
.why-choose-us {
    padding: 5rem 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 30px;
    margin: 4rem 0;
}

.why-choose-us .feature-card {
    text-align: center;
    padding: 2.5rem;
}

.why-choose-us .feature-card i {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .carousel-item {
        height: 60vh;
        min-height: 400px;
    }
    
    .welcome-section,
    .why-choose-us {
        padding: 2rem;
        margin: 2rem 0;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    .category-card img,
    .product-card img {
        height: 200px;
    }
}

/* RTL Support */
[dir="rtl"] .feature-card i,
[dir="rtl"] .why-choose-us .feature-card i {
    margin-left: 1rem;
    margin-right: 0;
}

[dir="rtl"] .category-badge {
    left: 10px;
    right: auto;
}
</style>
{% endblock %}

{% block content %}
<!-- Hero Carousel -->
<div id="heroCarousel" class="carousel slide" data-bs-ride="carousel">
    <div class="carousel-indicators">
        {% for image in carousel_images %}
        <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="{{ forloop.counter0 }}" 
                class="{% if forloop.first %}active{% endif %}"
                aria-current="{% if forloop.first %}true{% endif %}"
                aria-label="Slide {{ forloop.counter }}"></button>
        {% endfor %}
    </div>
    <div class="carousel-inner">
        {% for image in carousel_images %}
        <div class="carousel-item {% if forloop.first %}active{% endif %}">
            <div class="carousel-overlay"></div>
            <img src="{{ image.image.url }}" class="d-block w-100" alt="{{ image.title }}">
            <div class="carousel-caption">
                <h1 class="display-4 fw-bold mb-4" data-aos="fade-up">{{ image.title }}</h1>
                <p class="lead mb-4" data-aos="fade-up" data-aos-delay="100">{{ image.description }}</p>
                {% if image.button_text and image.button_link %}
                <a href="{{ image.button_link }}" class="btn btn-primary btn-lg" data-aos="fade-up" data-aos-delay="200">
                    {{ image.button_text }}
                </a>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Welcome Section -->
<section class="welcome-section" data-aos="fade-up">
    <div class="row align-items-center">
        <div class="col-lg-6">
            <h1 class="display-4 mb-4">{% trans 'Welcome to GB Farms' %}</h1>
            <p class="lead mb-4">{% trans 'Your trusted source for fresh, organic produce and sustainable farming practices.' %}</p>
            <div class="d-flex flex-wrap gap-3">
                <div class="feature-card">
                    <i class="fas fa-leaf text-success mb-3"></i>
                    <h5>{% trans '100% Organic Products' %}</h5>
                </div>
                <div class="feature-card">
                    <i class="fas fa-truck text-success mb-3"></i>
                    <h5>{% trans 'Fast Delivery' %}</h5>
                </div>
                <div class="feature-card">
                    <i class="fas fa-tag text-success mb-3"></i>
                    <h5>{% trans 'Best Prices' %}</h5>
                </div>
            </div>
        </div>
        <div class="col-lg-6 position-relative">
            {% if website_video %}
            <div class="welcome-video-container rounded-3 overflow-hidden">
                <video autoplay muted loop playsinline class="welcome-section-video">
                    <source src="{{ website_video.video.url }}" type="video/mp4">
                    {% trans 'Your browser does not support the video tag.' %}
                </video>
            </div>
            {% else %}
            <img src="{% static 'vegetables.jpg' %}" alt="GB Farms" class="img-fluid rounded-3">
            {% endif %}
            <div class="experience-badge">
                <div class="text-center">
                    <span class="since-text">{% trans 'Since' %}</span>
                    <span class="year-text">1991</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="categories-section py-5" data-aos="fade-up">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 mb-3">{% trans 'Shop by Category' %}</h2>
            <p class="lead">{% trans 'Browse our products by category' %}</p>
        </div>
        <div class="row g-4">
            {% for category in categories %}
            <div class="col-md-4">
                <a href="{% url 'GB_FARM:category_products' category.id %}" class="text-decoration-none">
                    <div class="category-card">
                        {% if category.image %}
                        <img src="{{ category.image.url }}" alt="{{ category.name }}" class="card-img-top">
                        {% else %}
                        <div class="card-img-top category-default-image">
                        </div>
                        {% endif %}
                        <div class="card-body text-center">
                            <h5 class="card-title">{{ category.name }}</h5>
                            <p class="card-text text-muted">{{ category.description|truncatewords:20 }}</p>
                        </div>
                    </div>
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Featured Products Section -->
<section class="featured-products" data-aos="fade-up">
    <div class="text-center mb-5">
        <h2 class="display-5 mb-3">{% trans 'Featured Products' %}</h2>
        <p class="lead">{% trans 'Discover our handpicked selection of premium products' %}</p>
    </div>
    <div class="row g-4">
        {% for product in featured_products %}
        <div class="col-md-4">
            <div class="product-card">
                <div class="position-relative">
                    {% if product.image %}
                    <img src="{{ product.image.url }}" alt="{{ product.name }}" class="card-img-top">
                    {% else %}
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-image text-muted fa-3x"></i>
                    </div>
                    {% endif %}
                    {% if product.category %}
                    <span class="category-badge">
                        <i class="fas fa-tag"></i> {{ product.category.name }}
                    </span>
                    {% endif %}
                </div>
                <div class="card-body">
                    <h5 class="card-title">{{ product.name }}</h5>
                    <p class="card-text">{{ product.description|truncatewords:20 }}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="h5 mb-0">EGP {{ product.price|floatformat:2 }}</span>
                        <a href="{% url 'GB_FARM:product_detail' product.id %}" class="btn btn-success">{% trans 'View Details' %}</a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="why-choose-us" data-aos="fade-up">
    <div class="text-center mb-5">
        <h2 class="display-5 mb-3">{% trans 'Why Choose Us' %}</h2>
        <p class="lead">{% trans 'Experience the difference with GB Farms' %}</p>
    </div>
    <div class="row g-4">
        <div class="col-md-4">
            <div class="feature-card text-center">
                <i class="fas fa-leaf text-success mb-3 fa-2x"></i>
                <h4>{% trans 'Organic Quality' %}</h4>
                <p>{% trans 'All our products are certified organic and grown with sustainable practices.' %}</p>
            </div>
        </div>
        <div class="col-md-4">
            <div class="feature-card text-center">
                <i class="fas fa-truck text-success mb-3 fa-2x"></i>
                <h4>{% trans 'Fast Delivery' %}</h4>
                <p>{% trans 'Quick and reliable delivery to your doorstep with real-time tracking.' %}</p>
            </div>
        </div>
        <div class="col-md-4">
            <div class="feature-card text-center">
                <i class="fas fa-tag text-success mb-3 fa-2x"></i>
                <h4>{% trans 'Best Prices' %}</h4>
                <p>{% trans 'Competitive prices for premium quality organic products.' %}</p>
            </div>
        </div>
    </div>
</section>

<!-- Custom styles for this template -->
<style>
.carousel-item {
    height: 80vh;
    min-height: 500px;
    background: no-repeat center center scroll;
    background-size: cover;
    position: relative;
}

.carousel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.3), rgba(0,0,0,0.7));
    z-index: 1;
}

.carousel-caption {
    z-index: 2;
    bottom: 50%;
    transform: translateY(50%);
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.carousel-caption h1 {
    font-size: 3.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    margin-bottom: 1rem;
}

.carousel-caption p {
    font-size: 1.25rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.carousel-indicators {
    z-index: 3;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 6px;
    background-color: rgba(255,255,255,0.5);
    border: none;
    transition: all 0.3s ease;
}

.carousel-indicators button.active {
    background-color: var(--primary-color);
    transform: scale(1.2);
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
    opacity: 0;
    transition: all 0.3s ease;
}

.carousel:hover .carousel-control-prev,
.carousel:hover .carousel-control-next {
    opacity: 0.8;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-color: rgba(255,255,255,0.2);
    border-radius: 50%;
    padding: 2rem;
    transition: all 0.3s ease;
}

.carousel-control-prev:hover .carousel-control-prev-icon,
.carousel-control-next:hover .carousel-control-next-icon {
    background-color: rgba(255,255,255,0.4);
    transform: scale(1.1);
}

.category-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    height: 100%;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.category-card img {
    height: 200px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.category-card:hover img {
    transform: scale(1.1);
}

.category-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    z-index: 1;
}

@media (max-width: 768px) {
    .carousel-item {
        height: 60vh;
    }
    
    .carousel-caption h1 {
        font-size: 2.5rem;
    }
    
    .carousel-caption p {
        font-size: 1rem;
    }
}

/* Video Styles */
.welcome-video-container {
    height: 400px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

.welcome-section-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.experience-badge {
    background: rgba(46, 204, 113, 0.9);
    backdrop-filter: blur(3px);
    border-radius: 50%;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    position: absolute;
    top: -20px;
    right: -20px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.since-text {
    display: block;
    font-size: 1.1rem;
    margin-bottom: -5px;
}

.year-text {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
}
</style>
{% endblock %} 