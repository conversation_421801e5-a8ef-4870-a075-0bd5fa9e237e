import sqlite3
import os
import django
import sys

# Set up Django environment
# Adjust the path 'Gb-Farms-BE' to your actual Django project directory name if different
project_path = os.path.abspath(os.path.join(os.path.dirname(__file__)))
sys.path.append(project_path)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings') # Replace 'Gb_Farms_BE' with your project's settings module
django.setup()

# Import your models AFTER django.setup()
from website.models import Category, Product # Import other models as needed

# --- Configuration ---
OLD_DB_PATH = r'C:\Users\<USER>\Desktop\GB\Gb-Farms-BE\olddb\db.sqlite3'
NEW_DB_PATH = r'C:\Users\<USER>\Desktop\GB\Gb-Farms-BE\db.sqlite3' # This is managed by Django settings

print(f"Old DB Path: {OLD_DB_PATH}")
print(f"New DB Path (Managed by Django): {NEW_DB_PATH}") # For confirmation

# --- Database Connections ---
try:
    old_conn = sqlite3.connect(OLD_DB_PATH)
    old_cursor = old_conn.cursor()
    print("Successfully connected to the old database.")
except sqlite3.Error as e:
    print(f"Error connecting to old database: {e}")
    sys.exit(1)

# New database connection is handled by Django ORM based on settings.py

# --- Data Transfer Functions ---

def transfer_categories(old_cursor):
    """Transfers data from the old category table to the new one."""
    print("\n--- Transferring Categories ---")
    
    # Define the expected old table name here - adjust if you find the correct one
    old_table_name = 'category' # Or 'website_category', or whatever it should be

    try:
        # Try executing the query
        old_cursor.execute(f"SELECT name, slug, description, image, created_at, updated_at FROM {old_table_name}")
        
        categories_transferred = 0
        categories_skipped = 0

        for row in old_cursor.fetchall():
            name, slug, description, image, created_at, updated_at = row

            # Check if category already exists (by slug, assuming it's unique)
            if Category.objects.filter(slug=slug).exists():
                print(f"Skipping existing category: {name} (Slug: {slug})")
                categories_skipped += 1
                continue

            try:
                Category.objects.create(
                    name=name,
                    slug=slug,
                    description=description,
                    image=image if image else None,
                )
                print(f"Transferred category: {name}")
                categories_transferred += 1
            except Exception as e:
                print(f"Error transferring category '{name}': {e}")

        print(f"Finished attempting category transfer.")
        print(f"Transferred: {categories_transferred}, Skipped: {categories_skipped}")

    except sqlite3.Error as e:
        # Specifically check if the error is about the table not existing
        if f"no such table: {old_table_name}" in str(e):
            print(f"Warning: Old category table '{old_table_name}' not found. Skipping category transfer.")
        else:
            # Print other potential SQLite errors
            print(f"Error querying old categories table: {e}")
    except Exception as e:
        print(f"An unexpected error occurred during category transfer: {e}")


# --- Main Execution ---
if __name__ == "__main__":
    print("Starting data transfer script...")

    # Transfer Categories
    transfer_categories(old_cursor)

    # Add calls to transfer other models here (e.g., transfer_products, transfer_users, etc.)
    # Remember the order matters due to foreign keys!
    # Example:
    # transfer_products(old_cursor)
    # transfer_orders(old_cursor)
    # ...

    # --- Cleanup ---
    old_conn.close()
    print("\nData transfer script finished.") 