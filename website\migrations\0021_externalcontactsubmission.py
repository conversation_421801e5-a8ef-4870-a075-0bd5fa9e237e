# Generated by Django 4.2.20 on 2025-06-08 23:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0020_alter_contactusmessage_subject'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExternalContactSubmission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.Char<PERSON>ield(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('message', models.TextField()),
                ('source_page', models.CharField(choices=[('external_index', 'Home Page'), ('external_about', 'About Page'), ('external_mangoes', 'Mangoes Page'), ('external_grapes', 'Grapes Page'), ('external_Others', 'others Page'), ('external_contact', 'Contact Page')], default='external_index', max_length=30)),
                ('ip_address', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_processed', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'External Contact Submission',
                'verbose_name_plural': 'External Contact Submissions',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['created_at'], name='website_ext_created_651911_idx'), models.Index(fields=['source_page'], name='website_ext_source__b98904_idx'), models.Index(fields=['is_processed'], name='website_ext_is_proc_db5640_idx')],
            },
        ),
    ]
