# Generated by Django 4.2.20 on 2025-06-25 17:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0045_branch'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='branch',
            options={'ordering': ['order', 'name'], 'verbose_name': 'Branch', 'verbose_name_plural': 'Branches'},
        ),
        migrations.RemoveIndex(
            model_name='branch',
            name='website_bra_branch__5c39e4_idx',
        ),
        migrations.RemoveIndex(
            model_name='branch',
            name='website_bra_is_main_601bd4_idx',
        ),
        migrations.RemoveIndex(
            model_name='branch',
            name='website_bra_latitud_04276d_idx',
        ),
        migrations.RemoveField(
            model_name='branch',
            name='branch_type',
        ),
        migrations.RemoveField(
            model_name='branch',
            name='city',
        ),
        migrations.RemoveField(
            model_name='branch',
            name='city_ar',
        ),
        migrations.RemoveField(
            model_name='branch',
            name='country',
        ),
        migrations.RemoveField(
            model_name='branch',
            name='country_ar',
        ),
        migrations.RemoveField(
            model_name='branch',
            name='image',
        ),
        migrations.RemoveField(
            model_name='branch',
            name='is_main_branch',
        ),
        migrations.AddField(
            model_name='branch',
            name='is_headquarters',
            field=models.BooleanField(default=False, help_text='Mark as main headquarters'),
        ),
        migrations.AddField(
            model_name='branch',
            name='manager_name',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Branch Manager'),
        ),
        migrations.AlterField(
            model_name='branch',
            name='description',
            field=models.TextField(blank=True, help_text='Additional branch information', null=True),
        ),
        migrations.AlterField(
            model_name='branch',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name='branch',
            name='latitude',
            field=models.DecimalField(decimal_places=8, help_text='Latitude coordinate (e.g., 30.037 for Cairo)', max_digits=10),
        ),
        migrations.AlterField(
            model_name='branch',
            name='longitude',
            field=models.DecimalField(decimal_places=8, help_text='Longitude coordinate (e.g., 31.207 for Cairo)', max_digits=11),
        ),
        migrations.AlterField(
            model_name='branch',
            name='name_ar',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Branch Name'),
        ),
        migrations.AlterField(
            model_name='branch',
            name='order',
            field=models.PositiveIntegerField(default=0, help_text='Display order on map'),
        ),
        migrations.AlterField(
            model_name='branch',
            name='working_hours',
            field=models.CharField(blank=True, help_text='e.g., Sunday - Thursday: 9:00 AM - 6:00 PM', max_length=200, null=True),
        ),
        migrations.AddIndex(
            model_name='branch',
            index=models.Index(fields=['is_headquarters'], name='website_bra_is_head_165cac_idx'),
        ),
        migrations.AddIndex(
            model_name='branch',
            index=models.Index(fields=['order'], name='website_bra_order_aba501_idx'),
        ),
    ]
