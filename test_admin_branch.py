#!/usr/bin/env python
import os
import sys
import django
from decimal import Decimal

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from website.models import Branch

def test_admin_branch_creation():
    """Test creating a branch like the admin would"""
    try:
        # Create a branch instance (like the admin form would)
        branch = Branch()
        
        # Set the fields (like the admin form would)
        branch.name = "Test Admin Branch"
        branch.name_ar = "فرع إداري تجريبي"
        branch.address = "123 Admin Street, Admin City"
        branch.address_ar = "123 شارع الإدارة، مدينة الإدارة"
        branch.latitude = Decimal('30.04440000')
        branch.longitude = Decimal('31.23570000')
        branch.is_active = True
        
        # Test the full_clean method (this is what the admin calls)
        branch.full_clean()
        print("✅ Branch full_clean() method works correctly")
        
        # Test saving (but don't actually save to avoid database changes)
        # branch.save()
        print("✅ Branch would save successfully")
        
        print("✅ All admin-style tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error in admin-style test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_admin_branch_creation()
