from modeltranslation.translator import register, TranslationOptions, translator
from .models import *
from django.utils.translation import gettext_lazy as _

@register(Product)
class ProductTranslationOptions(TranslationOptions):
    fields = ('name', 'description',)  # Fields you want translated

@register(Category)
class CategoryTranslationOptions(TranslationOptions):
    fields = ('name', 'description',)

@register(About)
class AboutTranslationOptions(TranslationOptions):
    fields = ('title', 'description',)

@register(Career)
class CareerTranslationOptions(TranslationOptions):
    fields = ('title', 'description',)

@register(Configuration)
class ConfigurationTranslationOptions(TranslationOptions):
    fields = ('leasing_short_description', 'factoring_short_description', 'company_overview_description', 'governance_description', 'about_us_short_description',)

@register(Footer)

class FooterTranslationOptions(TranslationOptions):
    fields = ('address', 'phone', 'email', 'facebook_link', 'instagram_link', 'twitter_link',)

@register(ExternalFooter)
class ExternalFooterTranslationOptions(TranslationOptions):
    fields = ('address', 'phone', 'email', 'facebook_url', 'instagram_url', 'youtube_url',)





    

