<!DOCTYPE html>
{% load custom_filters %}
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice #{{ invoice_number }}</title>
    <style type="text/css">
        @page {
            size: a4 portrait;
            margin: 1cm;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            font-size: 12px;
        }
        .invoice-header {
            background-color: #f8f9fa;
            padding: 20px;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
            color: #006400;
        }
        .invoice-details {
            display: block;
            margin-top: 10px;
        }
        .company-details, .customer-details {
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th {
            background-color: #f8f9fa;
            text-align: left;
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }
        td {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .totals {
            text-align: right;
            margin-top: 20px;
        }
        .totals table {
            width: 300px;
            margin-left: auto;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #777;
            font-size: 11px;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            color: white;
            font-size: 12px;
        }
        .status-pending { background-color: #ffc107; }
        .status-processing { background-color: #17a2b8; }
        .status-shipped { background-color: #007bff; }
        .status-delivered { background-color: #ffc107; }
        .status-cancelled { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="invoice-header">
        <div style="float: left; width: 60%;">
            <h1 class="invoice-title">GB Farm</h1>
            <p>Organic Farm Products</p>
        </div>
        <div style="float: right; width: 40%; text-align: right;">
            <h2>INVOICE</h2>
            <p><strong>Invoice #:</strong> {{ invoice_number }}</p>
            <p><strong>Date:</strong> {{ invoice_date|date:"F j, Y" }}</p>
            <p><strong>Order #:</strong> {{ order.id }}</p>
            <p>
                <span class="status-badge status-{{ order.status }}">
                    {{ order.get_status_display }}
                </span>
            </p>
        </div>
        <div style="clear: both;"></div>
    </div>

    <div style="overflow: auto;">
        <div class="company-details" style="float: left; width: 50%;">
            <h3>From:</h3>
            <p><strong>GB Farm</strong></p>
            <p>123 Farm Road</p>
            <p>Cairo, Egypt</p>
            <p>Email: <EMAIL></p>
            <p>Phone: +20 ************</p>
        </div>

        <div class="customer-details" style="float: right; width: 50%;">
            <h3>Bill To:</h3>
            <p><strong>{{ order.customer_name|default:order.user.username }}</strong></p>
            <p>{{ order.shipping_address|linebreaksbr }}</p>
            <p>Phone: {{ order.phone_number }}</p>
            <p>Email: {{ order.user.email }}</p>
        </div>
        <div style="clear: both;"></div>
    </div>

    <table>
        <thead>
            <tr>
                <th style="width: 50%;">Product</th>
                <th>Price</th>
                <th>Quantity</th>
                <th style="text-align: right;">Total</th>
            </tr>
        </thead>
        <tbody>
            {% for item in order_items %}
            <tr>
                <td>{{ item.product.name }}</td>
                <td>EGP {{ item.price|floatformat:2 }}</td>
                <td>{{ item.quantity }}</td>
                <td style="text-align: right;">EGP {{ item.quantity|floatformat:0|default:1|multiply:item.price|floatformat:2 }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div class="totals">
        <table>
            <tr>
                <td><strong>Subtotal:</strong></td>
                <td style="text-align: right;">EGP {{ subtotal|floatformat:2 }}</td>
            </tr>
            <tr>
                <td><strong>Shipping:</strong></td>
                <td style="text-align: right;">EGP {{ shipping_cost|floatformat:2 }}</td>
            </tr>
            <tr>
                <td><strong>Total:</strong></td>
                <td style="text-align: right; font-weight: bold;">EGP {{ total|floatformat:2 }}</td>
            </tr>
            {% if payment %}
            <tr>
                <td><strong>Payment Method:</strong></td>
                <td style="text-align: right;">{{ payment.get_payment_method_display }}</td>
            </tr>
            <tr>
                <td><strong>Payment Status:</strong></td>
                <td style="text-align: right;">{{ payment.get_status_display }}</td>
            </tr>
            {% endif %}
        </table>
    </div>

    <div class="footer">
        <p>Thank you for shopping with GB Farm!</p>
        <p>If you have any questions about this invoice, please contact customer <NAME_EMAIL></p>
    </div>
</body>
</html> 