{% extends 'GB_FARM/base.html' %}
{% load static %}
{% load custom_filters %}
{% load i18n %}

{% block title %}My Orders{% endblock %}

{% block extra_css %}
<style>
    .table-responsive {
        overflow-x: auto;
    }
    .table-hover tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.03);
    }
    .product-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .product-image-small {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .product-image-small-placeholder {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f0f0f0;
        border-radius: 4px;
    }
    .table-separator td {
        border-bottom: 2px solid #e0e0e0 !important;
    }
    /* Ensure table cells don't wrap too much */
    .table th, .table td {
        white-space: nowrap;
        vertical-align: middle;
    }
    /* Adjust padding for cleaner look */
    .table > :not(caption) > * > * {
        padding: 0.75rem 0.5rem;
    }

    @media (max-width: 768px) {
        .table th, .table td {
            white-space: normal;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container my-5">
    <h1 class="mb-4">My Orders</h1>
    
    {% if messages %}
    <div class="messages mb-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    
    {% if orders %}
    <div class="card mb-4">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "Product" %}</th>
                            <th>{% trans "Quantity" %}</th>
                            <th>{% trans "Order Total" %}</th>
                            <th>{% trans "Status" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders %}
                        {% for item in order.orderitem_set.all %}
                        <tr>
                            <td>
                                <div class="product-info">
                                    <div>
                                        {{ item.product.name }}
                                        {% if item.selected_weight %}
                                        <div><small class="badge bg-info">{{ item.selected_weight }}</small></div>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>{{ item.quantity }}</td>
                            {% if forloop.first %}
                            <td rowspan="{{ order.orderitem_set.count }}">EGP{{ order.total_amount|floatformat:2 }}</td>
                            <td rowspan="{{ order.orderitem_set.count }}">
                                <span class="badge bg-{% if order.status == 'delivered' %}success{% elif order.status == 'cancelled' %}danger{% elif order.status == 'shipped' %}info{% else %}warning{% endif %}">
                                    {{ order.get_status_display }}
                                </span>
                            </td>
                            <td rowspan="{{ order.orderitem_set.count }}">
                                <a href="{% url 'GB_FARM:track_order' %}" class="btn btn-sm btn-outline-info">Track Order</a>
                            </td>
                            {% endif %}
                        </tr>
                        {% endfor %}
                        {% if not forloop.last %}
                        <tr class="table-separator">
                            <td colspan="5" class="p-0 border-0"></td>
                        </tr>
                        {% endif %}
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center">No order items found for this order.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    {% if orders.has_other_pages %}
    <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
            {% if orders.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ orders.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">&laquo; Previous</a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">&laquo; Previous</span>
            </li>
            {% endif %}
            
            {% for i in orders.paginator.page_range %}
                {% if orders.number == i %}
                <li class="page-item active">
                    <span class="page-link">{{ i }}</span>
                </li>
                {% else %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ i }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">{{ i }}</a>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if orders.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ orders.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">Next &raquo;</a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">Next &raquo;</span>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-bag-x fs-1 text-muted mb-3"></i>
            <h3>No Orders Found</h3>
            <p class="text-muted">You haven't placed any orders yet.</p>
            <a href="{% url 'GB_FARM:product_list' %}" class="btn btn-primary mt-3">Start Shopping</a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %} 