# Generated by Django 4.2.20 on 2025-06-18 17:04

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0023_calendarsettings_producttype_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CalendarEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_name', models.CharField(max_length=255, verbose_name='Product Name')),
                ('product_name_ar', models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Product Name')),
                ('day', models.PositiveIntegerField(help_text='Day of the month (1-31)', validators=[django.core.validators.MinValueValidator(1)])),
                ('month', models.PositiveIntegerField(choices=[(1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'), (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'), (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')], verbose_name='Month')),
                ('year', models.PositiveIntegerField(default=2025, verbose_name='Year')),
                ('from_date', models.DateField(verbose_name='From Date')),
                ('to_date', models.DateField(verbose_name='To Date')),
                ('description', models.TextField(verbose_name='Description')),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('priority', models.PositiveIntegerField(default=1, help_text='1=High, 2=Medium, 3=Low')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Calendar Event',
                'verbose_name_plural': 'Calendar Events',
                'ordering': ['year', 'month', 'day', 'priority'],
            },
        ),
        migrations.RemoveIndex(
            model_name='productavailability',
            name='website_pro_product_c8d61a_idx',
        ),
        migrations.AddIndex(
            model_name='productavailability',
            index=models.Index(fields=['product_type', 'status'], name='website_pro_product_ab15ba_idx'),
        ),
        migrations.AddField(
            model_name='calendarevent',
            name='product_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='calendar_events', to='website.producttype'),
        ),
        migrations.AddIndex(
            model_name='calendarevent',
            index=models.Index(fields=['year', 'month', 'day'], name='website_cal_year_7320b1_idx'),
        ),
        migrations.AddIndex(
            model_name='calendarevent',
            index=models.Index(fields=['from_date', 'to_date'], name='website_cal_from_da_559790_idx'),
        ),
        migrations.AddIndex(
            model_name='calendarevent',
            index=models.Index(fields=['product_type', 'is_active'], name='website_cal_product_8f12b8_idx'),
        ),
    ]
