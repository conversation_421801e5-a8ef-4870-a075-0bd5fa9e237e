{% extends 'Extrnal.html/main.html' %}
{% load static %}

{% block head %}
    <title>GB Farms - Red Seeder</title>
{% endblock %}

{% block content %}
    <!-- Hero Banner -->
    <div class="relative h-screen w-full overflow-hidden">
        <!-- Background Image with Overlay -->
        <div class="absolute inset-0">
            <img src="{% static 'images/<EMAIL>' %}" 
                 class="w-full h-full object-cover" 
                 alt="GB Farms Beautiful Farm Landscape">
            <!-- Gradient overlay for better text readability -->
            <div class="absolute inset-0 bg-gradient-to-r from-black/60 via-black/30 to-transparent"></div>
        </div>
        
        <!-- Content Container -->
        <div class="relative h-full flex items-center">
            <div class="container mx-auto px-4 md:mx-48 md:mt-40">
                <div class="max-w-4xl">
                    <!-- Main Heading -->
                    <h1 class="text-white text-4xl md:text-6xl font-bold leading-tight mb-6">
                        CULTIVATING GOODNESS
                    </h1>
                    
                    <!-- Subheading -->
                    <h2 class="text-white text-xl md:text-3xl font-semibold mb-8 leading-relaxed">
                        OVER 25 YEARS OF QUALITY, FOOD<br class="hidden md:block">
                        SAFETY, AND PROFESSIONALISM
                    </h2>
                    
                    <!-- Description -->
                    <p class="text-white text-base md:text-xl max-w-3xl mb-10 leading-relaxed opacity-90">
                        We ship to our customers worldwide with strict adherence to international 
                        standards of quality, food safety and traceability.
                    </p>
                    
                    <!-- Call to Action Button -->
                    <a href="{% url 'GB_FARM:external_about' %}" 
                       class="inline-block bg-yellow-300 hover:bg-yellow-400 text-green-900 font-bold py-3 px-6 md:py-4 md:px-10 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        Read More
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2">
            <div class="animate-bounce">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Second Section -->
    <section class="relative h-screen justify-center px-4 md:px-8 pt-20 md:pl-20 bg-gray-100">
        <!-- SVG curved lines -->
        <div class="absolute inset-0 w-full h-full opacity-10">
            <svg viewBox="0 0 1200 400" class="w-full h-full" preserveAspectRatio="none">
                <!-- Left flowing curve -->
                <path d="M50 80 Q 200 60, 350 100 Q 450 140, 500 200 Q 480 280, 350 320 Q 200 340, 50 320" 
                      stroke="#22c55e" 
                      stroke-width="1.5" 
                      fill="none" 
                      opacity="0.8"/>
                <!-- Center flowing curve -->
                <path d="M450 70 Q 600 50, 750 90 Q 850 130, 900 200 Q 880 270, 750 310 Q 600 350, 450 330" 
                      stroke="#22c55e" 
                      stroke-width="1.5" 
                      fill="none" 
                      opacity="0.6"/>
                <!-- Right flowing curve -->
                <path d="M850 80 Q 1000 60, 1150 100 Q 1200 140, 1200 200 Q 1200 280, 1150 320 Q 1000 340, 850 320" 
                      stroke="#22c55e" 
                      stroke-width="1.5" 
                      fill="none" 
                      opacity="0.8"/>
                <!-- Top connecting curve -->
                <path d="M100 50 Q 400 30, 700 50 Q 1000 70, 1200 50" 
                      stroke="#22c55e" 
                      stroke-width="1" 
                      fill="none" 
                      stroke-dasharray="5,3"
                      opacity="0.4"/>
                <!-- Bottom connecting curve -->
                <path d="M100 350 Q 400 370, 700 350 Q 1000 330, 1200 350" 
                      stroke="#22c55e" 
                      stroke-width="1" 
                      fill="none" 
                      stroke-dasharray="5,3"
                      opacity="0.4"/>
            </svg>
        </div>
    <div class="flex flex-col items-end  justify-start overflow-hidden h-[800px] translate-s-96">
        <video autoplay muted loop class="w-[65%] h-[65%]  object-cover rounded-lg">
            {% if about_us_video and about_us_video.video %}
            <source src="{{ about_us_video.video.url }}" type="video/mp4">
            {% else %}
            <source src="{% static 'images/WhatsApp Video 2025-06-02 at 11.21.16_4d952a33.mp4' %}" type="video/mp4">
            {% endif %}
        </video>
    </div>

        <!-- About Us Content Card -->
        <div class="absolute inset-0 flex items-center">
            <div class="mx-4 md:ml-40 w-[40%] rounded-xl bg-[#d4e10a] bg-opacity-85 p-6 md:p-12">
                {% if about_us_video %}
                <h2 class="text-green-800 text-4xl md:text-7xl font-bold mb-6">{{ about_us_video.title }}</h2>
                <p class="text-green-800 text-base md:text-lg mb-8">
                    {{ about_us_video.description|linebreaksbr }}
                </p>
                {% else %}
                <h2 class="text-green-800 text-4xl md:text-7xl font-bold mb-6">About Us</h2>
                <p class="text-green-800 text-base md:text-lg mb-8">
                    GB Farms was established in 1991 by Dr. Raouf Ghabbour, founder
                    and CEO of GB Auto, a publicly traded company leading the MENA
                    region in automotive manufacturing and distribution, and financial
                    solutions with a workforce of more than 23,000 employees.
                </p>
                {% endif %}
                <a href="{% url 'GB_FARM:external_about' %}" class="text-green-800 hover:text-green-900 font-medium">Read More</a>
            </div>
        </div>
    </section>

   <!-- second section -->
   <section class="relative h-auto md:h-[90vh] w-full bg-gray-100 flex flex-col items-center justify-center mb-16 md:mb-32 gap-y-6 md:gap-y-10 py-16 md:py-0">
    <!-- Background pattern -->
    <div
        class="absolute inset-0 bg-[url('https://flowbite.s3.amazonaws.com/docs/jumbotron/hero-pattern.svg')] opacity-10">
    </div>

    <!-- SVG curved lines -->
    <div class="absolute inset-0 w-full h-full opacity-10">
        <svg viewBox="0 0 1200 400" class="w-full h-full" preserveAspectRatio="none">
            <!-- Left flowing curve around grapes -->
            <path d="M50 80 Q 200 60, 350 100 Q 450 140, 500 200 Q 480 280, 350 320 Q 200 340, 50 320" 
                  stroke="#22c55e" 
                  stroke-width="1.5" 
                  fill="none" 
                  opacity="0.8"/>
            <!-- Center flowing curve around mango -->
            <path d="M450 70 Q 600 50, 750 90 Q 850 130, 900 200 Q 880 270, 750 310 Q 600 350, 450 330" 
                  stroke="#22c55e" 
                  stroke-width="1.5" 
                  fill="none" 
                  opacity="0.6"/>
            <!-- Right flowing curve around others -->
            <path d="M850 80 Q 1000 60, 1150 100 Q 1200 140, 1200 200 Q 1200 280, 1150 320 Q 1000 340, 850 320" 
                  stroke="#22c55e" 
                  stroke-width="1.5" 
                  fill="none" 
                  opacity="0.8"/>
            <!-- Top connecting curve -->
            <path d="M100 50 Q 400 30, 700 50 Q 1000 70, 1200 50" 
                  stroke="#22c55e" 
                  stroke-width="1" 
                  fill="none" 
                  stroke-dasharray="5,3"
                  opacity="0.4"/>
            <!-- Bottom connecting curve -->
            <path d="M100 350 Q 400 370, 700 350 Q 1000 330, 1200 350" 
                  stroke="#22c55e" 
                  stroke-width="1" 
                  fill="none" 
                  stroke-dasharray="5,3"
                  opacity="0.4"/>
        </svg>
    </div>

    <!-- Products heading -->
    <h2 class="text-green-400 text-4xl md:text-7xl text-center font-bold z-10">Products</h2>

    <!-- Debug: Show group items count -->
    {% if group_items %}
        <p class="text-center text-sm text-gray-600 mb-4">Found {{ group_items|length }} group item(s)</p>
    {% else %}
        <p class="text-center text-sm text-gray-600 mb-4">No group items found - showing fallback content</p>
    {% endif %}

    <!-- Dynamic Product cards carousel container -->
    <div class="relative z-10 w-full px-4 md:px-48">
        <div id="products-carousel" class="relative w-full min-h-[300px] md:min-h-[400px]" data-carousel="static">
            <!-- Carousel wrapper -->
            <div class="relative overflow-hidden h-full">
                {% if group_items %}
                    {% for group_item in group_items %}
                        <!-- Slide {{ forloop.counter }} -->
                        <div class="{% if forloop.first %}duration-700 ease-in-out{% else %}hidden duration-700 ease-in-out{% endif %}" data-carousel-item="{% if forloop.first %}active{% endif %}">
                            <div class="flex flex-col md:flex-row justify-center gap-8 w-full h-full items-center">
                                <!-- Dynamic Product Card -->
                                <a href="#" class="group">
                                    <img src="{{ group_item.image.url }}" alt="{{ group_item.group.title }}"
                                        class="w-full md:w-96 h-64 md:h-80 rounded-xl transition-transform duration-300 group-hover:scale-105 object-cover">
                                </a>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <!-- Fallback: Original static content -->
                 
                        </div>
                    </div>
                {% endif %}
            </div>

            <!-- Slider controls -->
            {% if group_items and group_items|length > 1 %}
            <button type="button" class="absolute top-1/2 left-4 transform -translate-y-1/2 z-30 flex items-center justify-center w-12 h-12 cursor-pointer group focus:outline-none" data-carousel-prev>
                <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-green-600/80 group-hover:bg-green-600 group-focus:ring-4 group-focus:ring-green-300 group-focus:outline-none shadow-lg">
                    <svg class="w-4 h-4 text-white rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1 1 5l4 4"/>
                    </svg>
                    <span class="sr-only">Previous</span>
                </span>
            </button>
            <button type="button" class="absolute top-1/2 right-4 transform -translate-y-1/2 z-30 flex items-center justify-center w-12 h-12 cursor-pointer group focus:outline-none" data-carousel-next>
                <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-green-600/80 group-hover:bg-green-600 group-focus:ring-4 group-focus:ring-green-300 group-focus:outline-none shadow-lg">
                    <svg class="w-4 h-4 text-white rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                    </svg>
                    <span class="sr-only">Next</span>
                </span>
            </button>

            <!-- Carousel indicators -->
            <div class="absolute z-30 flex -translate-x-1/2 bottom-5 left-1/2 space-x-3 rtl:space-x-reverse">
                {% for group_item in group_items %}
                <button type="button" class="w-3 h-3 rounded-full {% if forloop.first %}bg-green-400/70{% else %}bg-green-400/50{% endif %} hover:bg-green-400" aria-current="{% if forloop.first %}true{% else %}false{% endif %}" aria-label="Slide {{ forloop.counter }}" data-carousel-slide-to="{{ forloop.counter0 }}"></button>
                {% endfor %}
            </div>
            {% endif %}
        </div>
    </div>
     </section>

    <!-- Achievements Section -->
    <section class="relative w-full bg-white py-16 md:py-0 z-10">
        <!-- Background pattern -->
        <div class="absolute inset-0 bg-[url('https://flowbite.s3.amazonaws.com/docs/jumbotron/hero-pattern.svg')] opacity-10">
        </div>

        <!-- Section heading -->
        <h2 class="text-green-400 text-4xl md:text-7xl text-center font-bold mb-16 md:mb-32 z-10 relative">Achievements</h2>
        <!-- Achievements flex -->
        <div class="flex flex-col gap-y-8 md:gap-y-16 z-10 w-full md:h-[700px] items-center relative px-4">
            <!-- Top row -->
            <div class="flex flex-col md:flex-row gap-y-8 md:gap-x-32 w-full justify-center">
                <!-- Employees -->
                <div class="flex flex-col items-center">
                    <svg class="w-16 h-16 md:w-32 md:h-32 text-green-400 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                    <span class="text-green-400 text-2xl md:text-4xl font-bold mb-4">25,000+</span>
                    <span class="text-green-400 text-lg md:text-xl text-center">Employees</span>
                </div>

                <!-- Acres -->
                <div class="flex flex-col items-center">
                    <svg class="w-16 h-16 md:w-32 md:h-32 text-green-400 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                            d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                    <span class="text-green-400 text-2xl md:text-4xl font-bold mb-4">50,000+</span>
                    <span class="text-green-400 text-lg md:text-xl text-center">Acres Under Operation</span>
                </div>
            </div>

            <!-- Bottom row -->
            <div class="flex flex-col md:flex-row gap-y-8 md:gap-x-32">
                <!-- Certificates -->
                <div class="flex flex-col items-center">
                    <svg class="w-16 h-16 md:w-32 md:h-32 text-green-400 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="text-green-400 text-2xl md:text-4xl font-bold mb-4">99+</span>
                    <span class="text-green-400 text-lg md:text-xl text-center">Certificates</span>
                </div>

                <!-- Products -->
                <div class="flex flex-col items-center">
                    <svg class="w-16 h-16 md:w-32 md:h-32 text-green-400 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                            d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    <span class="text-green-400 text-2xl md:text-4xl font-bold mb-4">250,000+</span>
                    <span class="text-green-400 text-lg md:text-xl text-center">Tons of Exported Fresh Products</span>
                </div>

                <!-- Countries -->
                <div class="flex flex-col items-center">
                    <svg class="w-16 h-16 md:w-32 md:h-32 text-green-400 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                            d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="text-green-400 text-2xl md:text-4xl font-bold mb-4">65+</span>
                    <span class="text-green-400 text-lg md:text-xl text-center">Countries</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Flowbite JS -->
{% endblock %}
