from django.urls import path, include
from rest_framework import routers
from rest_framework.authtoken.views import obtain_auth_token

# Import DEBUG directly from django.conf settings to avoid potential circular imports
from django.conf import settings

router = routers.DefaultRouter()

# Define urlpatterns without conditional logic first
urlpatterns = [
    path('', include(router.urls)),
    # path('api-auth/', include('rest_framework.urls')),
    # path('api-token-auth/', obtain_auth_token, name='api_token_auth'),
]

# Then conditionally add more patterns
if settings.DEBUG:
    urlpatterns += [path('api-auth/', include('rest_framework.urls'))]