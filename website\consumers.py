import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import Order
from django.contrib.auth.models import User

class OrderTrackingConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.order_id = self.scope['url_route']['kwargs']['order_id']
        self.room_group_name = f'order_{self.order_id}'

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()

    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json['message']

        # Send message to room group
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'order_update',
                'message': message
            }
        )

    async def order_update(self, event):
        message = event['message']

        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'message': message
        }))

    @database_sync_to_async
    def get_order(self):
        return Order.objects.get(id=self.order_id)

class AdminNotificationConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        # Check if user is admin
        if not self.scope["user"].is_authenticated or not self.scope["user"].is_staff:
            await self.close()
            return
            
        # Add to admin notification group
        await self.channel_layer.group_add(
            "admin_notifications",
            self.channel_name
        )
        
        await self.accept()

    async def disconnect(self, close_code):
        # Remove from admin notification group
        await self.channel_layer.group_discard(
            "admin_notifications",
            self.channel_name
        )

    async def notify_new_order(self, event):
        # Send new order notification to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'new_order',
            'order_id': event['order_id']
        }))

    async def notify_new_message(self, event):
        # Send new message notification to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'new_message',
            'order_id': event['order_id'],
            'message_count': event['message_count']
        })) 