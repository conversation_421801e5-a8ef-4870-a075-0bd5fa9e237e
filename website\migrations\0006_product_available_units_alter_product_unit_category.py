# Generated by Django 4.2.20 on 2025-05-06 11:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0005_alter_product_unit_alter_product_unit_category'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='available_units',
            field=models.CharField(blank=True, help_text='Comma-separated list of available units for customers (e.g., kg,500g,250g)', max_length=100),
        ),
        migrations.AlterField(
            model_name='product',
            name='unit_category',
            field=models.CharField(choices=[('FREE WEIGHT', 'Free Weight'), ('KILOGRAM', 'Kilogram'), ('500G', '500g'), ('250G', '250g'), ('LITER', 'Liter'), ('PIECE', 'Piece')], default='PIECE', max_length=20),
        ),
    ]
