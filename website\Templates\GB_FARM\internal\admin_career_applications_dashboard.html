{% extends "GB_FARM/admin_base.html" %}
{% load static %}

{% block title %}Career Applications | Admin Dashboard{% endblock %}

{% block admin_content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Career Applications</h6>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="d-flex gap-2 justify-content-end">
                                <span class="badge bg-primary">Total: {{ total_applications }}</span>
                                <span class="badge bg-warning">Unprocessed: {{ unprocessed_applications }}</span>
                                <span class="badge bg-success">Processed: {{ processed_applications }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <!-- Filters -->
                    <div class="card mx-4 mb-4">
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-4">
                                    <label for="q" class="form-label">Search</label>
                                    <input type="text" name="q" id="q" value="{{ search_query }}" 
                                           placeholder="Search by name, email, or position..."
                                           class="form-control">
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select name="status" id="status" class="form-select">
                                        <option value="">All Status</option>
                                        <option value="unprocessed" {% if selected_status == 'unprocessed' %}selected{% endif %}>Unprocessed</option>
                                        <option value="processed" {% if selected_status == 'processed' %}selected{% endif %}>Processed</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="career" class="form-label">Position</label>
                                    <select name="career" id="career" class="form-select">
                                        <option value="">All Positions</option>
                                        {% for career in careers %}
                                            <option value="{{ career.id }}" {% if selected_career == career.id|stringformat:"s" %}selected{% endif %}>
                                                {{ career.title }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Applications Table -->
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Applicant</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Position</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Experience</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">CV</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Status</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Date</th>
                                    <th class="text-secondary opacity-7"></th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for application in applications %}
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ application.full_name }}</h6>
                                                <p class="text-xs text-secondary mb-0">{{ application.email }}</p>
                                                {% if application.phone_number %}
                                                <p class="text-xs text-secondary mb-0">{{ application.phone_number }}</p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">{{ application.career.title }}</p>
                                        <p class="text-xs text-secondary mb-0">{{ application.career.department }}</p>
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">
                                            {% if application.years_of_experience %}
                                                {{ application.years_of_experience }} years
                                            {% else %}
                                                -
                                            {% endif %}
                                        </p>
                                    </td>
                                    <td>
                                        {% if application.cv_file %}
                                            <a href="{{ application.cv_file.url }}" target="_blank" 
                                               class="text-primary text-decoration-underline">
                                                View CV
                                            </a>
                                        {% else %}
                                            <span class="text-muted">No CV</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if application.is_processed %}
                                        <span class="badge badge-sm bg-gradient-success">Processed</span>
                                        {% else %}
                                        <span class="badge badge-sm bg-gradient-warning">Pending</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">{{ application.created_at|date:"M d, Y" }}</p>
                                        <p class="text-xs text-secondary mb-0">{{ application.created_at|time:"H:i" }}</p>
                                    </td>
                                    <td class="align-middle">
                                        <a href="{% url 'GB_FARM:admin_career_application_detail' application.id %}" 
                                           class="btn btn-sm btn-info">
                                            View Details
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <p class="text-sm mb-0">No applications found.</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if applications.has_other_pages %}
                    <div class="px-4 py-3">
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if applications.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_career %}&career={{ selected_career }}{% endif %}" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ applications.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_career %}&career={{ selected_career }}{% endif %}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                                
                                {% for num in applications.paginator.page_range %}
                                    {% if applications.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                    {% elif num > applications.number|add:'-3' and num < applications.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_career %}&career={{ selected_career }}{% endif %}">{{ num }}</a>
                                    </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if applications.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ applications.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_career %}&career={{ selected_career }}{% endif %}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ applications.paginator.num_pages }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_career %}&career={{ selected_career }}{% endif %}" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 