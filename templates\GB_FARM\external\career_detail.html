{% extends "GB_FARM/external/base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{{ career.title }} - {% trans "Careers" %} - GB Farms{% endblock %}

{% block extra_css %}
<style>
    .job-header {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
    }
    .job-section {
        margin-bottom: 2rem;
    }
    .job-section h3 {
        margin-bottom: 1rem;
        position: relative;
        padding-bottom: 0.5rem;
    }
    .job-section h3:after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 50px;
        height: 3px;
        background-color: #0d6efd;
    }
    .job-meta {
        padding: 1.5rem;
        background-color: #f8f9fa;
        border-radius: 0.5rem;
    }
    .job-meta-item {
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #dee2e6;
    }
    .job-meta-item:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }
</style>
{% endblock %}

{% block content %}
<!-- Job Header -->
<div class="job-header py-4 px-4 mb-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'GB_FARM:home' %}">{% trans "Home" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'GB_FARM:career_list' %}">{% trans "Careers" %}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ career.title }}</li>
                    </ol>
                </nav>
                <h1 class="mt-3 mb-2">{{ career.title }}</h1>
                <div class="d-flex flex-wrap align-items-center mb-3">
                    <span class="badge bg-primary me-2 mb-2">
                        {% if career.job_type == 'full_time' %}{% trans "Full Time" %}
                        {% elif career.job_type == 'part_time' %}{% trans "Part Time" %}
                        {% elif career.job_type == 'contract' %}{% trans "Contract" %}
                        {% elif career.job_type == 'internship' %}{% trans "Internship" %}
                        {% endif %}
                    </span>
                    <span class="badge bg-secondary me-2 mb-2">
                        {% if career.experience_level == 'entry' %}{% trans "Entry Level" %}
                        {% elif career.experience_level == 'mid' %}{% trans "Mid Level" %}
                        {% elif career.experience_level == 'senior' %}{% trans "Senior Level" %}
                        {% elif career.experience_level == 'lead' %}{% trans "Lead Level" %}
                        {% elif career.experience_level == 'manager' %}{% trans "Manager Level" %}
                        {% endif %}
                    </span>
                    <span class="text-muted small me-3 mb-2">
                        <i class="bi bi-building me-1"></i> {{ career.department }}
                    </span>
                    <span class="text-muted small me-3 mb-2">
                        <i class="bi bi-geo-alt me-1"></i> {{ career.location }}
                    </span>
                    <span class="text-muted small mb-2">
                        <i class="bi bi-calendar3 me-1"></i> {% trans "Posted" %}: {{ career.created_at|date:"M d, Y" }}
                    </span>
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-center justify-content-md-end mt-3 mt-md-0">
                <a href="#apply" class="btn btn-primary btn-lg">
                    <i class="bi bi-send me-2"></i> {% trans "Apply Now" %}
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container mb-5">
    <div class="row">
        <!-- Job Content -->
        <div class="col-lg-8">
            <!-- Job Description -->
            <div class="job-section">
                <h3>{% trans "Job Description" %}</h3>
                <div class="job-description">
                    {{ career.description|linebreaks }}
                </div>
            </div>

            <!-- Responsibilities -->
            <div class="job-section">
                <h3>{% trans "Responsibilities" %}</h3>
                <div class="job-responsibilities">
                    {{ career.responsibilities|linebreaks }}
                </div>
            </div>

            <!-- Requirements -->
            <div class="job-section">
                <h3>{% trans "Requirements" %}</h3>
                <div class="job-requirements">
                    {{ career.requirements|linebreaks }}
                </div>
            </div>

            <!-- Benefits -->
            {% if career.benefits %}
            <div class="job-section">
                <h3>{% trans "Benefits" %}</h3>
                <div class="job-benefits">
                    {{ career.benefits|linebreaks }}
                </div>
            </div>
            {% endif %}

            <!-- Application Form -->
            <div class="job-section" id="apply">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <h3 class="card-title">{% trans "Apply for this Position" %}</h3>
                        <p class="text-muted mb-4">{% trans "Please fill out the form below to apply for this position. All fields marked with * are required." %}</p>
                        
                        <form method="post" enctype="multipart/form-data" action="#" class="needs-validation" novalidate>
                            {% csrf_token %}
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="first_name" class="form-label">{% trans "First Name" %} *</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="last_name" class="form-label">{% trans "Last Name" %} *</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">{% trans "Email Address" %} *</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">{% trans "Phone Number" %} *</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                </div>
                                <div class="col-12">
                                    <label for="resume" class="form-label">{% trans "Resume/CV" %} *</label>
                                    <input type="file" class="form-control" id="resume" name="resume" accept=".pdf,.doc,.docx" required>
                                    <div class="form-text">{% trans "Accepted formats: PDF, DOC, DOCX (Max size: 5MB)" %}</div>
                                </div>
                                <div class="col-12">
                                    <label for="cover_letter" class="form-label">{% trans "Cover Letter" %}</label>
                                    <textarea class="form-control" id="cover_letter" name="cover_letter" rows="4"></textarea>
                                </div>
                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                        <label class="form-check-label" for="terms">
                                            {% trans "I agree to the" %} <a href="#">{% trans "terms and conditions" %}</a> *
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary btn-lg w-100">
                                        <i class="bi bi-send me-2"></i> {% trans "Submit Application" %}
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Job Sidebar -->
        <div class="col-lg-4">
            <div class="job-meta sticky-top" style="top: 2rem;">
                <h4 class="mb-4">{% trans "Job Overview" %}</h4>
                
                <div class="job-meta-item">
                    <div class="small text-muted mb-1">{% trans "Department" %}</div>
                    <div class="fw-bold">
                        <i class="bi bi-building text-primary me-2"></i> {{ career.department }}
                    </div>
                </div>
                
                <div class="job-meta-item">
                    <div class="small text-muted mb-1">{% trans "Location" %}</div>
                    <div class="fw-bold">
                        <i class="bi bi-geo-alt text-primary me-2"></i> {{ career.location }}
                    </div>
                </div>
                
                <div class="job-meta-item">
                    <div class="small text-muted mb-1">{% trans "Job Type" %}</div>
                    <div class="fw-bold">
                        <i class="bi bi-briefcase text-primary me-2"></i>
                        {% if career.job_type == 'full_time' %}{% trans "Full Time" %}
                        {% elif career.job_type == 'part_time' %}{% trans "Part Time" %}
                        {% elif career.job_type == 'contract' %}{% trans "Contract" %}
                        {% elif career.job_type == 'internship' %}{% trans "Internship" %}
                        {% endif %}
                    </div>
                </div>
                
                <div class="job-meta-item">
                    <div class="small text-muted mb-1">{% trans "Experience Level" %}</div>
                    <div class="fw-bold">
                        <i class="bi bi-person-badge text-primary me-2"></i>
                        {% if career.experience_level == 'entry' %}{% trans "Entry Level" %}
                        {% elif career.experience_level == 'mid' %}{% trans "Mid Level" %}
                        {% elif career.experience_level == 'senior' %}{% trans "Senior Level" %}
                        {% elif career.experience_level == 'lead' %}{% trans "Lead Level" %}
                        {% elif career.experience_level == 'manager' %}{% trans "Manager Level" %}
                        {% endif %}
                    </div>
                </div>
                
                <div class="job-meta-item">
                    <div class="small text-muted mb-1">{% trans "Posted Date" %}</div>
                    <div class="fw-bold">
                        <i class="bi bi-calendar3 text-primary me-2"></i> {{ career.created_at|date:"M d, Y" }}
                    </div>
                </div>
                
                <div class="d-grid gap-2 mt-4">
                    <a href="#apply" class="btn btn-primary">
                        <i class="bi bi-send me-2"></i> {% trans "Apply Now" %}
                    </a>
                    <button class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="bi bi-printer me-2"></i> {% trans "Print" %}
                    </button>
                    <a href="{% url 'GB_FARM:career_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i> {% trans "Back to Careers" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Similar Jobs Section -->
<section class="bg-light py-5">
    <div class="container">
        <h2 class="mb-4">{% trans "Similar Positions" %}</h2>
        <div class="row g-4">
            {% for i in "123" %}
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title">{% trans "Sample Position" %} {{ forloop.counter }}</h5>
                        <div class="text-muted small mb-3">
                            <div><i class="bi bi-building me-1"></i> {% trans "Department" %}</div>
                            <div><i class="bi bi-geo-alt me-1"></i> {% trans "Location" %}</div>
                        </div>
                        <a href="{% url 'GB_FARM:career_list' %}" class="btn btn-outline-primary btn-sm">
                            {% trans "View Details" %}
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
    
    // Smooth scroll for apply button
    document.querySelectorAll('a[href="#apply"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });
});
</script>
{% endblock %} 