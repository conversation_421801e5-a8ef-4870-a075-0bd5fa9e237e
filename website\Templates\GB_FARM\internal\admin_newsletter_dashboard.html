{% extends 'GB_FARM/admin_base.html' %}
{% load static %}

{% block title %}Newsletter Subscriptions | Admin Dashboard{% endblock %}

{% block admin_content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Newsletter Subscriptions</h6>
                        </div>
                        <div class="col-md-6 text-end">
                            <form method="get" class="d-flex">
                                {% csrf_token %}
                                <input type="text" name="q" value="{{ search_query }}" class="form-control me-2" placeholder="Search email...">
                                <button type="submit" class="btn btn-sm btn-primary text-black">Search</button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Status filter tabs -->
                    <ul class="nav nav-tabs mb-3">
                        <li class="nav-item">
                            <a class="nav-link {% if not selected_status %}active{% endif %}" href="{% url 'GB_FARM:admin_newsletter_dashboard' %}">
                                All ({{ total_subscriptions }})
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if selected_status == 'active' %}active{% endif %}" href="{% url 'GB_FARM:admin_newsletter_dashboard' %}?status=active">
                                Active ({{ active_subscriptions }})
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if selected_status == 'inactive' %}active{% endif %}" href="{% url 'GB_FARM:admin_newsletter_dashboard' %}?status=inactive">
                                Inactive ({{ inactive_subscriptions }})
                            </a>
                        </li>
                    </ul>

                    <!-- Subscriptions table -->
                    <div class="table-responsive">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Email</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Source</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Date</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Status</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for subscription in subscriptions %}
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ subscription.email }}</h6>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">{{ subscription.get_source_page_display }}</p>
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">{{ subscription.created_at|date:"M d, Y" }}</p>
                                        <p class="text-xs text-secondary mb-0">{{ subscription.created_at|time:"H:i" }}</p>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if subscription.is_active %}success{% else %}danger{% endif %} text-black">
                                            {% if subscription.is_active %}Active{% else %}Inactive{% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-info text-black" data-bs-toggle="modal" data-bs-target="#viewSubscription{{ subscription.id }}"></button>
                                        {% if subscription.is_active %}
                                        <a href="{% url 'GB_FARM:toggle_subscription_status' subscription.id %}" class="btn btn-sm btn-warning text-black">
                                            Deactivate
                                        </a>
                                        {% else %}
                                        <a href="{% url 'GB_FARM:toggle_subscription_status' subscription.id %}" class="btn btn-sm btn-success text-black">
                                            Activate
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>

                                <!-- View Modal -->
                                <div class="modal fade" id="viewSubscription{{ subscription.id }}" tabindex="-1" aria-labelledby="viewSubscriptionLabel{{ subscription.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="viewSubscriptionLabel{{ subscription.id }}">Subscription Details</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="row mb-3">
                                                    <div class="col-md-12">
                                                        <p><strong>Email:</strong> {{ subscription.email }}</p>
                                                        <p><strong>Source Page:</strong> {{ subscription.get_source_page_display }}</p>
                                                        <p><strong>Date:</strong> {{ subscription.created_at }}</p>
                                                        <p><strong>IP Address:</strong> {{ subscription.ip_address }}</p>
                                                        <p><strong>Status:</strong> 
                                                            <span class="badge bg-{% if subscription.is_active %}success{% else %}danger{% endif %} text-black">
                                                                {% if subscription.is_active %}Active{% else %}Inactive{% endif %}
                                                            </span>
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="text-center">
                                                    <form action="{% url 'GB_FARM:toggle_subscription_status' subscription.id %}" method="post">
                                                        {% csrf_token %}
                                                        <button type="submit" class="btn btn-{% if subscription.is_active %}warning{% else %}success{% endif %} text-black">
                                                            {% if subscription.is_active %}Deactivate{% else %}Activate{% endif %}
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary text-black" data-bs-dismiss="modal">Close</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <p class="text-sm mb-0">No subscriptions found.</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if subscriptions.has_other_pages %}
                    <div class="d-flex justify-content-center mt-4">
                        <nav aria-label="Page navigation">
                            <ul class="pagination">
                                {% if subscriptions.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ subscriptions.previous_page_number }}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}

                                {% for num in subscriptions.paginator.page_range %}
                                    {% if subscriptions.number == num %}
                                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                    {% elif num > subscriptions.number|add:'-3' and num < subscriptions.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">{{ num }}</a>
                                    </li>
                                    {% endif %}
                                {% endfor %}

                                {% if subscriptions.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ subscriptions.next_page_number }}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 