# Generated by Django 4.2.20 on 2025-06-25 22:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0046_alter_branch_options_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='carouselimage',
            options={'ordering': ['order'], 'verbose_name': 'Carousel Image', 'verbose_name_plural': 'Carousel Images'},
        ),
        migrations.AlterField(
            model_name='carouselimage',
            name='button_link',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='carouselimage',
            name='button_text',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='carouselimage',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='carouselimage',
            name='order',
            field=models.PositiveIntegerField(default=0, help_text='Display order'),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='carouselimage',
            name='title',
            field=models.Char<PERSON>ield(max_length=200),
        ),
        migrations.AlterField(
            model_name='carouselimage',
            name='title_ar',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Title'),
        ),
    ]
