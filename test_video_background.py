#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.test import Client

def test_video_background_in_error_pages():
    """Test that video background is properly included in all error pages"""
    print("🧪 Testing Video Background in Error Pages...")
    print("=" * 60)
    
    client = Client()
    
    # Test error page URLs
    error_tests = [
        ('/test-400/', '400 Bad Request'),
        ('/test-403/', '403 Forbidden'),
        ('/test-404/', '404 Not Found'),
        ('/test-500/', '500 Server Error'),
    ]
    
    video_path = 'images/prodect graps/WhatsApp Video 2025-07-30 at 11.39.13_07285bf2.mp4'
    
    for url, description in error_tests:
        print(f"\n🔍 Testing {description} ({url})...")
        
        try:
            response = client.get(url)
            print(f"   ✅ Status Code: {response.status_code}")
            
            content = response.content.decode('utf-8')
            
            # Check for video element
            if '<video' in content:
                print("   ✅ Video element found")
            else:
                print("   ❌ Video element NOT found")
            
            # Check for video source
            if video_path in content:
                print("   ✅ Video source path found")
            else:
                print("   ❌ Video source path NOT found")
            
            # Check for video attributes
            if 'autoplay' in content and 'muted' in content and 'loop' in content:
                print("   ✅ Video attributes (autoplay, muted, loop) found")
            else:
                print("   ❌ Video attributes missing")
            
            # Check for video background CSS
            if 'video-background' in content:
                print("   ✅ Video background CSS class found")
            else:
                print("   ❌ Video background CSS class NOT found")
            
            # Check for static template tag
            if '{% load static %}' in content:
                print("   ✅ Static template tag found")
            else:
                print("   ❌ Static template tag NOT found")
            
            # Check for improved content styling
            if 'bg-white bg-opacity-90' in content:
                print("   ✅ Improved content styling found")
            else:
                print("   ❌ Improved content styling NOT found")
                
        except Exception as e:
            print(f"   ❌ Error testing {url}: {e}")
    
    # Check if video file exists
    print(f"\n📁 Checking Video File...")
    video_file_path = f"static/{video_path}"
    
    if os.path.exists(video_file_path):
        print(f"   ✅ Video file exists: {video_file_path}")
        
        # Get file size
        file_size = os.path.getsize(video_file_path)
        file_size_mb = file_size / (1024 * 1024)
        print(f"   ℹ️  File size: {file_size_mb:.2f} MB")
        
    else:
        print(f"   ❌ Video file NOT found: {video_file_path}")
        print("   ⚠️  Make sure the video file is in the correct location")
    
    print("\n" + "=" * 60)
    print("✅ Video background testing completed!")
    
    print("\n💡 Features Added:")
    print("   🎥 Full-screen video background")
    print("   🔄 Auto-play, muted, and looping")
    print("   📱 Responsive design")
    print("   🎨 Semi-transparent content overlay for readability")
    print("   🏠 Home button for easy navigation")

if __name__ == "__main__":
    test_video_background_in_error_pages()
