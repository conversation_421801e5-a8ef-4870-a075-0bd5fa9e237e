{% extends 'GB_FARM/base.html' %}
{% load static %}

{% block extra_css %}
{{ block.super }}
<style>
    .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        padding: 3px 6px;
        border-radius: 50%;
        background: red;
        color: white;
        font-size: 0.7em;
    }
    
    .notification-icon {
        position: relative;
        display: inline-block;
    }
    
    /* New styles for the admin sidebar */
    .admin-sidebar {
        background-color: #f8f9fa;
        border-radius: 10px;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        overflow: hidden;
    }
    
    .admin-sidebar-header {
        background-color: #0d6efd;
        color: white;
        padding: 15px;
        font-weight: bold;
    }
    
    .admin-sidebar .list-group-item {
        border-left: none;
        border-right: none;
        padding: 12px 15px;
        transition: all 0.3s;
    }
    
    .admin-sidebar .list-group-item:first-child {
        border-top: none;
    }
    
    .admin-sidebar .list-group-item.active {
        background-color: #0d6efd;
        color: white;
        border-color: #0d6efd;
    }
    
    .admin-sidebar .list-group-item:hover:not(.active) {
        background-color: #e9ecef;
    }
    
    .admin-sidebar i {
        width: 24px;
        text-align: center;
        margin-right: 8px;
    }
</style>
{% block admin_extra_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="admin-sidebar mb-4">
                <div class="admin-sidebar-header">
                    <h5 class="mb-0">Admin Dashboard</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:admin_dashboard' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'admin_dashboard' %}active{% endif %}">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'admin_order_dashboard' %}active{% endif %}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-shopping-cart"></i> Orders
                            </div>
                            <div class="notification-icon" id="newOrdersBadge" style="display: none;">
                                <span class="notification-badge">0</span>
                            </div>
                        </div>
                    </a>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="list-group-item list-group-item-action {% if 'product' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-box"></i> Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="list-group-item list-group-item-action {% if 'customer' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-users"></i> Customers
                    </a>
                    <a href="{% url 'GB_FARM:admin_calendar_dashboard' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'admin_calendar_dashboard' %}active{% endif %}">
                        <i class="fas fa-calendar-alt"></i> Calendar Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'admin_analytics_dashboard' %}active{% endif %}">
                        <i class="fas fa-chart-line"></i> Analytics
                    </a>
                    <a href="{% url 'GB_FARM:admin_zone_list' %}" class="list-group-item list-group-item-action {% if 'zone' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-map-marker-alt"></i> Delivery Zones
                    </a>
                    <a href="{% url 'GB_FARM:admin_submissions_dashboard' %}" class="list-group-item list-group-item-action {% if 'submissions' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-envelope"></i> Contact Submissions
                    </a>
                    <a href="{% url 'GB_FARM:admin_career_applications_dashboard' %}" class="list-group-item list-group-item-action {% if 'career_application' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-briefcase"></i> Career Applications
                    </a>
                    <a href="{% url 'GB_FARM:admin_newsletter_dashboard' %}" class="list-group-item list-group-item-action {% if 'newsletter' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="fas fa-newspaper"></i> Newsletter
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            {% block admin_content %}{% endblock %}
        </div>
    </div>
</div>

<!-- Notification Sound -->
<audio id="notificationSound" preload="auto">
    <source src="{% static 'GB_FARM/sounds/notification.mp3' %}" type="audio/mpeg">
</audio>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
    // WebSocket connection
    const ws_scheme = window.location.protocol === "https:" ? "wss" : "ws";
    const ws_path = ws_scheme + '://' + window.location.host + '/ws/admin/notifications/';
    const socket = new WebSocket(ws_path);
    
    // Notification sound
    const notificationSound = document.getElementById('notificationSound');
    
    // Notification counters
    let newOrdersCount = 0;
    let unreadMessagesCount = 0;
    
    socket.onmessage = function(e) {
        const data = JSON.parse(e.data);
        
        if (data.type === 'new_order') {
            // Update new orders badge
            newOrdersCount++;
            const badge = document.getElementById('newOrdersBadge');
            badge.querySelector('.notification-badge').textContent = newOrdersCount;
            badge.style.display = 'block';
            
            // Play sound
            notificationSound.play();
            
            // Show toast notification
            showToast('New Order', `Order #${data.order_id} has been placed!`);
        }
        else if (data.type === 'new_message') {
            // Update messages badge for specific order
            const messageBadge = document.querySelector(`#messageBadge_${data.order_id}`);
            if (messageBadge) {
                const count = parseInt(messageBadge.textContent || '0') + 1;
                messageBadge.textContent = count;
                messageBadge.style.display = 'block';
            }
            
            // Play sound
            notificationSound.play();
            
            // Show toast notification
            showToast('New Message', `New message for Order #${data.order_id}`);
        }
    };
    
    function showToast(title, message) {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        
        toast.innerHTML = `
            <div class="toast-header">
                <strong class="me-auto">${title}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;
        
        // Add to container
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        container.appendChild(toast);
        document.body.appendChild(container);
        
        // Initialize and show toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove container after toast is hidden
        toast.addEventListener('hidden.bs.toast', () => {
            container.remove();
        });
    }
    
    // Reset new orders counter when visiting orders page
    if (window.location.pathname.includes('admin_order_dashboard')) {
        newOrdersCount = 0;
        document.getElementById('newOrdersBadge').style.display = 'none';
    }
</script>
{% block admin_extra_js %}{% endblock %}
{% endblock %} 