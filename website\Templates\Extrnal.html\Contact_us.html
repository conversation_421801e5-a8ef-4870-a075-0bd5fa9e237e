{% extends "Extrnal.html/EX.base.html" %}
{% load static %}

{% block head %}
    <title>Contact Us - GB Farms</title>
{% endblock %}

{% block header %}
    {% include "Extrnal.html/navebar.html" %}
{% endblock %}

{% block content %}
<!-- Contact Us Page -->
<main class="relative min-h-screen w-full bg-green-950 pt-16 md:pt-20">
    <!-- Get In Touch Header -->
    <div class="text-center py-8 md:py-12 bg-white">
        <h1 class="text-4xl md:text-6xl font-bold text-green-600 mb-4">Get In Touch</h1>
    </div>

    <!-- Interactive Map Section with Multiple Points -->
    <div class="w-full h-64 md:h-96 relative bg-gray-100 map-container">
        <!-- Layers Control (Google Maps Style) -->
        <div class="absolute bottom-4 left-4 z-10">
            <div class="relative">
                <!-- Layers Button -->
             
                <!-- Dropdown Menu -->
                <div id="layers-menu" class="absolute bottom-full left-0 mb-2 bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden min-w-[120px] hidden">
                    <button id="map-option" class="w-full px-4 py-3 text-left text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors border-b border-gray-100 flex items-center">
                        <div class="w-4 h-4 mr-3 bg-green-500 rounded-sm"></div>
                        Map
                    </button>
                    <button id="satellite-option" class="w-full px-4 py-3 text-left text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors flex items-center">
                        <div class="w-4 h-4 mr-3 bg-blue-500 rounded-sm"></div>
                        Satellite
                        <div class="ml-auto">
                            <div id="satellite-check" class="w-4 h-4 text-blue-500">
                                <svg fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        {% if branches %}
            <div id="map" class="w-full h-full">
                <!-- Fallback iframe that loads immediately -->
                <iframe
                    id="fallback-map"
                    width="100%"
                    height="100%"
                    style="border:0;"
                    allowfullscreen=""
                    referrerpolicy="no-referrer-when-downgrade"
                    src="https://maps.google.com/maps?q={% for branch in branches %}{{ branch.latitude }},{{ branch.longitude }}{% if not forloop.last %}|{% endif %}{% endfor %}&output=embed&z=8&t=k&iwloc=near&gestureHandling=cooperative&hl=en&controls=1&disableDefaultUI=false&fullscreenControl=true&streetViewControl=false">
                </iframe>
            </div>
        {% else %}
            <iframe
                id="default-map"
                width="100%"
                height="100%"
                style="border:0;"
                allowfullscreen=""
                referrerpolicy="no-referrer-when-downgrade"
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3457.5!2d31.207!3d30.037!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14583fa60b21beeb%3A0x79dfb296e8423bba!2sHussein%20Wassel%20St%2C%20Ad%20Doqi%2C%20Dokki%2C%20Giza%20Governorate%2C%20Egypt!5e1!3m2!1sen!2seg!4v1640995200000!5m2!1sen!2seg&gestureHandling=cooperative&hl=en&controls=1&disableDefaultUI=false&fullscreenControl=true&streetViewControl=false">
            </iframe>
        {% endif %}
    </div>

    <!-- CSS to hide Street View and Camera controls -->
    <style>
        /* Hide Street View Pegman button */
        button.gm-svpc,
        .gm-svpc,
        [data-value="Street View"],
        .gm-style .gm-svpc {
            display: none !important;
            visibility: hidden !important;
        }

        /* Hide Map camera controls button */
        button.gm-control-active,
        .gm-control-active,
        [aria-label*="Map camera controls"],
        .gm-style .gm-control-active {
            display: none !important;
            visibility: hidden !important;
        }

        /* Hide any Street View related controls */
        .gm-style .gm-style-mtc button[title*="Street View"],
        .gm-style .gm-style-mtc button[title*="Pegman"],
        .gm-style button[data-value="Street View"],
        .gm-style button[aria-label*="Street View"],
        .gm-style button[aria-label*="Pegman"] {
            display: none !important;
            visibility: hidden !important;
        }

        /* Ensure fullscreen button remains visible */
        button[title*="Toggle fullscreen view"],
        button[aria-label*="Toggle fullscreen view"],
        .gm-fullscreen-control {
            display: block !important;
            visibility: visible !important;
        }
    </style>


    <!-- Contact Form Section -->
    <div class="bg-white py-12 md:py-16">
        <div class="container mx-auto px-4 max-w-4xl">
            <h2 class="text-3xl md:text-4xl font-bold text-green-600 mb-6 md:mb-8 text-left">Contact Us Now</h2>
            
            <form id="contactForm" method="post" action="{% url 'GB_FARM:submit_external_contact' %}" class="space-y-4 md:space-y-6">
                {% csrf_token %}
                <input type="hidden" name="source_page" value="external_contact">
                
                <!-- First Row: Full Name -->
                <div>
                    <input type="text" id="fullName" name="full_name" placeholder="Your Full Name*" required
                           class="w-full px-3 py-2 md:px-4 md:py-3 border border-gray-300 rounded-md focus:border-green-500 focus:outline-none text-sm md:text-base bg-gray-50">
                </div>
                
                <!-- Second Row: Company Name -->
                <div>
                    <input type="text" id="companyName" name="company_name" placeholder="Company Name*" required
                           class="w-full px-3 py-2 md:px-4 md:py-3 border border-gray-300 rounded-md focus:border-green-500 focus:outline-none text-sm md:text-base bg-gray-50">
                </div>

                <!-- Third Row: Email -->
                <div>
                    <input type="email" id="email" name="email" placeholder="Your Email*" required
                           class="w-full px-3 py-2 md:px-4 md:py-3 border border-gray-300 rounded-md focus:border-green-500 focus:outline-none text-sm md:text-base bg-gray-50">
                </div>
                
                <!-- Fourth Row: Tel -->
                <div>
                    <input type="tel" id="tel" name="tel" placeholder="Tel*" required
                           class="w-full px-3 py-2 md:px-4 md:py-3 border border-gray-300 rounded-md focus:border-green-500 focus:outline-none text-sm md:text-base bg-gray-50">
                </div>

                <!-- Business Type Question -->
                <div class="mt-6 md:mt-8">
                    <h3 class="text-base md:text-lg font-medium text-green-600 mb-3 md:mb-4">What type of business do you present? *</h3>
                    <div class="space-y-2 md:space-y-3">
                        <label class="flex items-center space-x-2 md:space-x-3 cursor-pointer">
                            <input type="radio" name="business_type" value="supermarkets" required
                                   class="w-4 h-4 text-green-600 border-2 border-gray-400 focus:ring-green-500">
                            <span class="text-sm md:text-base text-green-600 font-medium">Supermarkets</span>
                        </label>
                        <label class="flex items-center space-x-2 md:space-x-3 cursor-pointer">
                            <input type="radio" name="business_type" value="wholesaler" required
                                   class="w-4 h-4 text-green-600 border-2 border-gray-400 focus:ring-green-500">
                            <span class="text-sm md:text-base text-green-600 font-medium">Wholesaler</span>
                        </label>
                        <label class="flex items-center space-x-2 md:space-x-3 cursor-pointer">
                            <input type="radio" name="business_type" value="distributor" required
                                   class="w-4 h-4 text-green-600 border-2 border-gray-400 focus:ring-green-500">
                            <span class="text-sm md:text-base text-green-600 font-medium">Distributor</span>
                        </label>
                    </div>
                </div>

                <!-- How did you find us Question -->
                <div class="mt-6 md:mt-8">
                    <h3 class="text-base md:text-lg font-medium text-green-600 mb-3 md:mb-4">How did you find out about Ghabbour Farms? *</h3>
                    <div class="space-y-2 md:space-y-3">
                        <label class="flex items-center space-x-2 md:space-x-3 cursor-pointer">
                            <input type="radio" name="find_out_method" value="internet" required
                                   class="w-4 h-4 text-green-600 border-2 border-gray-400 focus:ring-green-500">
                            <span class="text-sm md:text-base text-green-600 font-medium">Internet</span>
                        </label>
                        <label class="flex items-center space-x-2 md:space-x-3 cursor-pointer">
                            <input type="radio" name="find_out_method" value="exhibitions" required
                                   class="w-4 h-4 text-green-600 border-2 border-gray-400 focus:ring-green-500">
                            <span class="text-sm md:text-base text-green-600 font-medium">Exhibitions</span>
                        </label>
                        <label class="flex items-center space-x-2 md:space-x-3 cursor-pointer">
                            <input type="radio" name="find_out_method" value="search_engine" required
                                   class="w-4 h-4 text-green-600 border-2 border-gray-400 focus:ring-green-500">
                            <span class="text-sm md:text-base text-green-600 font-medium">Search Engine</span>
                        </label>
                    </div>
                </div>

                <!-- Subject -->
                <div class="mt-6 md:mt-8">
                    <input type="text" id="subject" name="subject" placeholder="Subject*" required
                           class="w-full px-3 py-2 md:px-4 md:py-3 border border-gray-300 rounded-md focus:border-green-500 focus:outline-none text-sm md:text-base bg-gray-50">
                </div>

                <!-- Message -->
                <div>
                    <textarea id="message" name="message" rows="4" placeholder="Your Message*" required
                              class="w-full px-3 py-2 md:px-4 md:py-3 border border-gray-300 rounded-md focus:border-green-500 focus:outline-none text-sm md:text-base bg-gray-50 resize-none md:rows-5"></textarea>
                </div>

                <!-- Submit Button -->
                <div class="mt-4 md:mt-6">
                    <button type="submit" 
                            class="bg-green-600 text-white py-2 px-6 md:py-3 md:px-8 rounded-md hover:bg-green-700 transition duration-300 font-bold text-sm md:text-base uppercase">
                        SEND
                    </button>
                </div>
            </form>
        </div>
    </div>
</main>

<!-- JavaScript Functions -->
<script>
// Function to hide unwanted map controls
function hideMapControls() {
    // Get the iframe and its content
    const mapIframe = document.getElementById('fallback-map') || document.getElementById('default-map');
    if (!mapIframe) return;

    try {
        // Try to access iframe content (may be blocked by CORS)
        const iframeDoc = mapIframe.contentDocument || mapIframe.contentWindow.document;
        if (iframeDoc) {
            // Hide Street View Pegman button
            const streetViewButtons = iframeDoc.querySelectorAll('button.gm-svpc, .gm-svpc, button[data-value="Street View"]');
            streetViewButtons.forEach(btn => {
                btn.style.display = 'none';
                btn.style.visibility = 'hidden';
            });

            // Hide Map camera controls button
            const cameraButtons = iframeDoc.querySelectorAll('button.gm-control-active, .gm-control-active');
            cameraButtons.forEach(btn => {
                btn.style.display = 'none';
                btn.style.visibility = 'hidden';
            });

            console.log('Map controls hidden successfully');
        }
    } catch (e) {
        // CORS will prevent access to iframe content, so CSS should handle it
        console.log('Using CSS to hide controls (CORS restriction)');
    }
}

// Function to continuously monitor and hide controls
function startControlMonitoring() {
    // Monitor the map container for any new controls
    const mapContainer = document.getElementById('map');
    if (!mapContainer) return;

    // Set up interval to periodically check and hide controls
    setInterval(function() {
        hideMapControls();
    }, 2000);

    console.log('Control monitoring started');
}
// Simple map loading check and enhancement
document.addEventListener('DOMContentLoaded', function() {
    const mapIframe = document.getElementById('fallback-map') || document.getElementById('default-map');

    if (mapIframe) {
        // Add loading event handler
        mapIframe.onload = function() {
            console.log('Map loaded successfully');
            // Add a subtle loading indicator removal
            const mapContainer = this.parentElement;
            if (mapContainer) {
                mapContainer.style.opacity = '1';
            }

            // Hide Street View and Camera controls after map loads
            setTimeout(function() {
                hideMapControls();
                // Set up continuous monitoring for dynamically added controls
                startControlMonitoring();
            }, 1000);
        };

        // Add error handler
        mapIframe.onerror = function() {
            console.log('Map failed to load, trying alternative');
            {% if branches %}
                {% with first_branch=branches.0 %}
                this.src = 'https://maps.google.com/maps?q={{ first_branch.latitude }},{{ first_branch.longitude }}&output=embed&z=12&t=k&gestureHandling=cooperative&hl=en&controls=1&disableDefaultUI=false&fullscreenControl=true&streetViewControl=false';
                {% endwith %}
            {% else %}
                this.src = 'https://maps.google.com/maps?q=30.037,31.207&output=embed&z=15&t=k&gestureHandling=cooperative&hl=en&controls=1&disableDefaultUI=false&fullscreenControl=true&streetViewControl=false';
            {% endif %}
        };

        // Set initial loading state
        const mapContainer = mapIframe.parentElement;
        if (mapContainer) {
            mapContainer.style.opacity = '0.8';
            mapContainer.style.transition = 'opacity 0.5s ease-in-out';
        }
    }

    // Layers control functionality
    const layersBtn = document.getElementById('layers-btn');
    const layersMenu = document.getElementById('layers-menu');
    const mapOption = document.getElementById('map-option');
    const satelliteOption = document.getElementById('satellite-option');
    const satelliteCheck = document.getElementById('satellite-check');

    // Current map type (default is satellite)
    let currentMapType = 'satellite';
    let menuOpen = false;

    // Branch coordinates for URL generation
    const branchCoords = {% if branches %}"{% for branch in branches %}{{ branch.latitude }},{{ branch.longitude }}{% if not forloop.last %}|{% endif %}{% endfor %}"{% else %}"30.037,31.207"{% endif %};

    // Base URLs for different map types
    const mapUrls = {
        {% if branches %}
        satellite: `https://maps.google.com/maps?q=${branchCoords}&output=embed&z=8&t=k&iwloc=near&gestureHandling=cooperative&hl=en&controls=1&disableDefaultUI=false&fullscreenControl=true&streetViewControl=false`,
        roadmap: `https://maps.google.com/maps?q=${branchCoords}&output=embed&z=8&t=m&iwloc=near&gestureHandling=cooperative&hl=en&controls=1&disableDefaultUI=false&fullscreenControl=true&streetViewControl=false`
        {% else %}
        satellite: 'https://maps.google.com/maps?q=30.037,31.207&output=embed&z=15&t=k&iwloc=near&gestureHandling=cooperative&hl=en&controls=1&disableDefaultUI=false&fullscreenControl=true&streetViewControl=false',
        roadmap: 'https://maps.google.com/maps?q=30.037,31.207&output=embed&z=15&t=m&iwloc=near&gestureHandling=cooperative&hl=en&controls=1&disableDefaultUI=false&fullscreenControl=true&streetViewControl=false'
        {% endif %}
    };

    // Update check mark visibility
    function updateCheckMark(activeType) {
        if (activeType === 'satellite') {
            satelliteCheck.style.display = 'block';
            // Remove any existing check from map option
            const mapCheck = mapOption.querySelector('.ml-auto');
            if (mapCheck) mapCheck.style.display = 'none';
        } else {
            satelliteCheck.style.display = 'none';
            // Add check to map option if it doesn't exist
            let mapCheck = mapOption.querySelector('.ml-auto');
            if (!mapCheck) {
                mapCheck = document.createElement('div');
                mapCheck.className = 'ml-auto';
                mapCheck.innerHTML = `
                    <div class="w-4 h-4 text-blue-500">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                `;
                mapOption.appendChild(mapCheck);
            }
            mapCheck.style.display = 'block';
        }
    }

    // Toggle layers menu
    if (layersBtn) {
        layersBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            menuOpen = !menuOpen;
            if (menuOpen) {
                layersMenu.classList.remove('hidden');
            } else {
                layersMenu.classList.add('hidden');
            }
        });
    }

    // Close menu when clicking outside
    document.addEventListener('click', function() {
        if (menuOpen) {
            layersMenu.classList.add('hidden');
            menuOpen = false;
        }
    });

    // Switch to satellite view
    if (satelliteOption) {
        satelliteOption.addEventListener('click', function(e) {
            e.stopPropagation();
            if (currentMapType !== 'satellite' && mapIframe) {
                mapIframe.src = mapUrls.satellite;
                currentMapType = 'satellite';
                updateCheckMark('satellite');
            }
            layersMenu.classList.add('hidden');
            menuOpen = false;
        });
    }

    // Switch to map view
    if (mapOption) {
        mapOption.addEventListener('click', function(e) {
            e.stopPropagation();
            if (currentMapType !== 'roadmap' && mapIframe) {
                mapIframe.src = mapUrls.roadmap;
                currentMapType = 'roadmap';
                updateCheckMark('roadmap');
            }
            layersMenu.classList.add('hidden');
            menuOpen = false;
        });
    }

    // Initialize check mark
    updateCheckMark('satellite');


});

// Optional: Try to load Google Maps API for enhanced features
function tryLoadGoogleMaps() {
    if (window.google && window.google.maps) {
        // Google Maps is already loaded
        return;
    }

    // Try to load Google Maps API
    const script = document.createElement('script');
    script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyC5rTlSYAshYxaPIkHklskW1fNh9Wcd-HU&callback=enhanceMap';
    script.async = true;
    script.defer = true;
    document.head.appendChild(script);
}

// Enhanced map function (optional upgrade)
function enhanceMap() {
    const mapDiv = document.getElementById('map');
    const fallbackIframe = document.getElementById('fallback-map');

    if (!mapDiv || !window.google) return;

    // Branch data
    const branches = [
        {% for branch in branches %}
        {
            name: "{{ branch.name|escapejs }}",
            nameAr: "{{ branch.name_ar|escapejs }}",
            lat: {{ branch.latitude }},
            lng: {{ branch.longitude }},
            googleMapsUrl: "{{ branch.get_google_maps_url|escapejs }}"
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];

    if (branches.length === 0) return;

    // Hide iframe and show interactive map
    if (fallbackIframe) {
        fallbackIframe.style.display = 'none';
    }

    // Calculate center
    let centerLat = 0, centerLng = 0;
    branches.forEach(branch => {
        centerLat += branch.lat;
        centerLng += branch.lng;
    });
    centerLat /= branches.length;
    centerLng /= branches.length;

    // Create map
    const map = new google.maps.Map(mapDiv, {
        zoom: branches.length === 1 ? 15 : 8,
        center: { lat: centerLat, lng: centerLng },
        mapTypeId: 'satellite'
    });

    // Add markers
    branches.forEach(branch => {
        const marker = new google.maps.Marker({
            position: { lat: branch.lat, lng: branch.lng },
            map: map,
            title: branch.name
        });

        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="padding: 10px;">
                    <h3 style="margin: 0 0 5px 0; color: #16a34a;">${branch.name}</h3>
                    ${branch.nameAr ? `<p style="margin: 0 0 10px 0; color: #666;">${branch.nameAr}</p>` : ''}
                    <a href="${branch.googleMapsUrl}" target="_blank"
                       style="color: #16a34a; text-decoration: none; font-weight: bold;">
                        View in Google Maps →
                    </a>
                </div>
            `
        });

        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
    });

    // Fit bounds if multiple branches
    if (branches.length > 1) {
        const bounds = new google.maps.LatLngBounds();
        branches.forEach(branch => {
            bounds.extend(new google.maps.LatLng(branch.lat, branch.lng));
        });
        map.fitBounds(bounds);
    }
}

// Try to enhance the map after a short delay
setTimeout(tryLoadGoogleMaps, 1000);
</script>

<!-- Additional Styling for Radio Buttons and Map -->
<style>
    /* Map Container Styling */
    #map {
        min-height: 256px !important;
        width: 100% !important;
        z-index: 1;
        display: block;
    }
    
    @media (min-width: 768px) {
        #map {
            min-height: 384px !important;
        }
    }
    
    .map-container {
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
    }
    
    /* Radio Button Styling */
    input[type="radio"] {
        appearance: none;
        -webkit-appearance: none;
        border-radius: 50%;
        display: inline-block;
        position: relative;
        border: 2px solid #6b7280;
        background-color: white;
    }
    
    input[type="radio"]:checked {
        background-color: #16a34a;
        border-color: #16a34a;
    }
    
    input[type="radio"]:checked::after {
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: white;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    
    /* Form Input Styling */
    input[type="text"], input[type="email"], input[type="tel"], textarea {
        transition: border-color 0.2s ease-in-out;
    }
    
    input[type="text"]:focus, input[type="email"]:focus, input[type="tel"]:focus, textarea:focus {
        border-color: #16a34a !important;
        box-shadow: none;
    }
    
    /* Responsive textarea rows */
    @media (min-width: 768px) {
        textarea {
            min-height: 120px;
        }
    }
</style>
{% endblock %}

{% block footer %}
    {% include "Extrnal.html/footer.html" %}
{% endblock %}