{% extends 'GB_FARM/base.html' %}

{% block title %}Search Results{% endblock %}

{% block content %}
<div class="container">
    <h2 class="mb-4">Search Results for "{{ query }}"</h2>
    
    {% if products %}
        <div class="row">
            {% for product in products %}
                <div class="col-md-4 mb-4">
                    <div class="card product-card h-100">
                        {% if product.image %}
                            <img src="{{ product.image.url }}" class="card-img-top" alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                        {% else %}
                            <div class="bg-light text-center py-5">
                                <span class="text-muted">No image available</span>
                            </div>
                        {% endif %}
                        <div class="card-body">
                            <h5 class="card-title">{{ product.name }}</h5>
                            <p class="card-text text-truncate">{{ product.description }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-success fw-bold">EGP{{ product.price }}</span>
                                <span class="badge bg-{% if product.stock > 10 %}success{% elif product.stock > 0 %}warning{% else %}danger{% endif %}">
                                    {% if product.stock > 0 %}{{ product.stock }} in stock{% else %}Out of stock{% endif %}
                                </span>
                            </div>
                        </div>
                        <div class="card-footer bg-white border-top-0">
                            <div class="d-grid">
                                <a href="{% url 'GB_FARM:product_detail' product.id %}" class="btn btn-outline-primary mb-2">
                                    View Details
                                </a>
                                <a href="{% url 'GB_FARM:add_to_cart' product.id %}" class="btn btn-outline-success" {% if product.stock <= 0 %}disabled{% endif %}>
                                    Add to Cart
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-info">
            <p>No products found matching your search criteria.</p>
        </div>
        <div class="text-center my-5">
            <a href="{% url 'GB_FARM:product_list' %}" class="btn btn-primary">Browse All Products</a>
        </div>
    {% endif %}
</div>
{% endblock %} 