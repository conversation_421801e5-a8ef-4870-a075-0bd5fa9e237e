{% extends 'GB_FARM/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% if category %}{{ category.name }}{% else %}{% trans "Products" %}{% endif %}{% endblock %}

{% block carousel_title %}{% if category %}{{ category.name }}{% else %}{% trans "Our Products" %}{% endif %}{% endblock %}

{% block carousel_description %}{% if category %}{{ category.description }}{% else %}{% trans "Discover our wide range of fresh and organic products" %}{% endif %}{% endblock %}

{% block content %}
<!-- Products Section -->
<section class="products-section py-5" data-aos="fade-up">
    <div class="container">
        <!-- Search and Filter Section -->
        <div class="row mb-4">
            <div class="col-md-6 mx-auto">
                <div class="input-group">
                    <input type="text" name="q" class="form-control" placeholder="{% trans 'Search products...' %}" value="{{ search_query }}" id="searchInput">
                    <div class="spinner-border spinner-border-sm text-primary ms-2 d-none" role="status" id="searchSpinner">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                <div class="text-center mt-2 text-muted small">
                    <i class="fas fa-info-circle me-1"></i> {% trans 'Type to search products instantly' %}
                </div>
            </div>
        </div>

        <!-- Categories -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="text-center mb-4">{% trans 'Categories' %}</h2>
                <div class="d-flex flex-wrap gap-2 justify-content-center">
                    <a href="{% url 'GB_FARM:product_list' %}" class="btn btn-outline-primary {% if not category %}active{% endif %}">
                        {% trans 'All Products' %}
                    </a>
                    {% for cat in categories %}
                    <a href="{% url 'GB_FARM:category_products' cat.id %}" 
                       class="btn btn-outline-primary {% if category.id == cat.id %}active{% endif %}">
                        {{ cat.name }}
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="row row-cols-1 row-cols-md-3 g-4">
            {% if products %}
                {% for product in products %}
                <div class="col">
                    <div class="card h-100 shadow-sm product-card">
                        <div class="position-relative">
                            {% if product.video %}
                            <a href="{% url 'GB_FARM:product_detail' product.id %}" class="card-img-top product-image video-thumbnail">
                                <div class="video-overlay">
                                    <i class="fas fa-play-circle fa-3x"></i>
                                </div>
                                <video class="card-img-top product-image" muted preload="metadata">
                                    <source src="{{ product.video.url }}#t=0.1" type="video/mp4">
                                </video>
                            </a>
                            {% elif product.image %}
                            <img src="{{ product.image.url }}" class="card-img-top product-image" alt="{{ product.name }}">
                            {% else %}
                            <div class="card-img-top bg-light d-flex align-items-center justify-center product-image">
                                <i class="bi bi-image text-muted fs-1"></i>
                            </div>
                            {% endif %}
                            {% if product.category %}
                            <span class="category-badge">
                                <i class="fas fa-tag"></i> {{ product.category.name }}
                            </span>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">{{ product.name }}</h5>
                            <p class="card-text">{{ product.description|truncatewords:20 }}</p>
                            <!-- Stock Status -->
                            <div class="mb-3">
                                {% if product.stock > 0 %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check-circle"></i> {% trans "In Stock" %}
                                </span>
                                <small class="text-muted ms-2">{% trans "Only" %} {{ product.stock }} {% trans "left" %}</small>
                                {% else %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-times-circle"></i> {% trans "Out of Stock" %}
                                </span>
                                {% endif %}
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="h5 mb-0">EGP {{ product.price|floatformat:2 }}</span>
                                <div class="btn-group">
                                    <a href="{% url 'GB_FARM:product_detail' product.id %}" class="btn btn-outline-primary">
                                        {% trans 'View Details' %}
                                    </a>
                                    {% if product.stock > 0 %}
                                    <button onclick="addToCart({{ product.id }})" class="btn btn-success ms-2">
                                        <i class="fas fa-shopping-cart"></i> {% trans "Add to Cart" %}
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12 p-4">
                    <div class="alert alert-info">
                        <h4 class="alert-heading">No products found</h4>
                        <p>
                            Debug info:<br>
                            Total products in database: {{ products.paginator.count }}<br>
                            Query params: {{ request.GET }}
                        </p>
                        <hr>
                        <p class="mb-0">We're sorry, but no products match your criteria. Please try different search terms or categories.</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Pagination -->
        {% if products.has_other_pages %}
        <div class="row mt-4">
            <div class="col-12">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if products.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ products.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if category %}&category={{ category.id }}{% endif %}">
                                {% trans 'Previous' %}
                            </a>
                        </li>
                        {% endif %}

                        {% for num in products.paginator.page_range %}
                        <li class="page-item {% if products.number == num %}active{% endif %}">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&q={{ search_query }}{% endif %}{% if category %}&category={{ category.id }}{% endif %}">
                                {{ num }}
                            </a>
                        </li>
                        {% endfor %}

                        {% if products.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ products.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if category %}&category={{ category.id }}{% endif %}">
                                {% trans 'Next' %}
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% endif %}
    </div>
</section>

<!-- Toast Container for Notifications -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="cartToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i class="fas fa-shopping-cart me-2"></i>
            {% if cart_count > 0 %}
            <span class="badge rounded-pill bg-secondary" style="font-size: 0.75rem;">
                {{ cart_count }}
            </span>
            {% endif %}
            <strong class="me-auto">{% trans "Cart" %}</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body"></div>
    </div>
</div>

<script>
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function showToast(message, success = true) {
    const toast = document.getElementById('cartToast');
    const toastBody = toast.querySelector('.toast-body');
    
    // Set message and style
    toastBody.textContent = message;
    toast.classList.remove('bg-success', 'bg-danger', 'text-white');
    if (success) {
        toast.classList.add('bg-success', 'text-white');
    } else {
        toast.classList.add('bg-danger', 'text-white');
    }
    
    // Show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}

function updateCartCount(count) {
    // Update all cart badges on the page (both in toast and navigation)
    const cartBadges = document.querySelectorAll('.fa-shopping-cart + .badge');
    
    if (count > 0) {
        // If badges exist, update them
        if (cartBadges.length > 0) {
            cartBadges.forEach(badge => {
                badge.textContent = count;
            });
        } else {
            // Create new badges if they don't exist
            document.querySelectorAll('.fa-shopping-cart').forEach(cartIcon => {
                const newBadge = document.createElement('span');
                newBadge.className = 'badge rounded-pill bg-secondary ms-1';
                newBadge.style.fontSize = '0.75rem';
                newBadge.textContent = count;
                cartIcon.parentNode.insertBefore(newBadge, cartIcon.nextSibling);
            });
        }
    } else {
        // Remove badges if count is 0
        cartBadges.forEach(badge => {
            badge.remove();
        });
    }
    
    // Update any cart count elements that might be in the header/navigation
    const cartCountElements = document.querySelectorAll('.cart-count');
    cartCountElements.forEach(element => {
        element.textContent = count;
        if (count > 0) {
            element.classList.remove('d-none');
        } else {
            element.classList.add('d-none');
        }
    });
}

async function addToCart(productId) {
    try {
        const response = await fetch(`{% url 'GB_FARM:add_to_cart' 0 %}`.replace('0', productId), {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        });

        const data = await response.json();
        
        if (response.ok) {
            showToast('{% trans "Product added to cart successfully!" %}', true);
            // Ensure cart_count is properly handled - if it's undefined, default to 1 or increment
            if (data.cart_count !== undefined) {
                updateCartCount(data.cart_count);
            } else {
                // If no cart_count in response, try to get current count and increment
                const currentBadge = document.querySelector('.fa-shopping-cart + .badge');
                const currentCount = currentBadge ? parseInt(currentBadge.textContent) : 0;
                updateCartCount(currentCount + 1);
            }
        } else {
            showToast(data.error || '{% trans "Error adding product to cart" %}', false);
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('{% trans "Error adding product to cart" %}', false);
    }
}
</script>

<style>
/* Override main container styles for this page */
main.container {
    background: rgba(255, 255, 255, 0.45);
    backdrop-filter: blur(3px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    margin: 2rem auto;
    padding: 2rem;
    position: relative;
    z-index: 1;
}

/* Products section styles */
.products-section {
    background: transparent;
    padding: 1rem;
    position: relative;
    z-index: 2;
}

.products-section .container {
    background: transparent;
    box-shadow: none;
    padding: 0;
    margin: 0;
}

.product-card {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(5px);
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.95);
}

.product-image {
    height: 200px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image {
    transform: scale(1.1);
}

.category-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    z-index: 1;
}

.btn-outline-primary {
    border-color: #28a745;
    color: #28a745;
}

.btn-outline-primary:hover,
.btn-outline-primary.active {
    background-color: #28a745;
    color: white;
}

.pagination .page-link {
    color: #28a745;
    border-color: #28a745;
}

.pagination .page-item.active .page-link {
    background-color: #28a745;
    border-color: #28a745;
}

/* Add to Cart button styles */
.btn-group {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-group form {
    margin: 0;
}

.btn-group .btn {
    white-space: nowrap;
    transition: all 0.3s ease;
}

.btn-group .btn:hover {
    transform: translateY(-2px);
}

/* Stock badge styles */
.badge {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.badge i {
    margin-right: 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .products-section {
        padding: 1rem;
        margin: 1rem;
    }
    
    .product-card {
        margin-bottom: 1.5rem;
    }
    
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        width: 100%;
        margin: 0.25rem 0;
    }
}

/* Toast Styles */
.toast-container {
    z-index: 1050;
}

.toast {
    min-width: 250px;
}

.toast.bg-success,
.toast.bg-danger {
    color: white;
}

.toast .toast-header {
    background-color: rgba(255, 255, 255, 0.85);
}

.toast .btn-close {
    filter: brightness(0) invert(1);
}

/* Video thumbnail styling */
.video-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
    display: block;
    text-decoration: none;
    border-radius: 10px 10px 0 0;
}

.video-thumbnail video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 1;
    transition: all 0.3s ease;
}

.video-overlay i {
    opacity: 0.8;
    transition: all 0.3s ease;
    filter: drop-shadow(0 0 5px rgba(0,0,0,0.5));
    font-size: 3.5rem;
}

.video-thumbnail:hover .video-overlay {
    background-color: rgba(0, 0, 0, 0.2);
}

.video-thumbnail:hover .video-overlay i {
    opacity: 1;
    transform: scale(1.2);
    color: #ffcc00;
}

/* Video badge */
.video-thumbnail::after {
    content: "VIDEO";
    position: absolute;
    bottom: 10px;
    left: 10px;
    background-color: rgba(255, 0, 0, 0.7);
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: bold;
    z-index: 2;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const searchInput = document.getElementById('searchInput');
    const searchSpinner = document.getElementById('searchSpinner');
    let searchTimeout;
    
    if (searchInput) {
        // Create a form dynamically
        const form = document.createElement('form');
        form.method = 'get';
        form.action = '{% url "GB_FARM:product_list" %}';
        form.id = 'searchForm';
        form.style.display = 'none';
        document.body.appendChild(form);
        
        // Set focus on search input when page loads if not on mobile
        if (window.innerWidth > 768) {
            searchInput.focus();
        }
        
        // Listen for input events
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            
            // Clear previous timeout
            clearTimeout(searchTimeout);
            
            if (query.length > 0) {
                // Show spinner
                searchSpinner.classList.remove('d-none');
                
                // Set timeout to submit search after 500ms
                searchTimeout = setTimeout(() => {
                    // Create/update hidden input for the form
                    let hiddenInput = document.getElementById('hiddenSearchInput');
                    if (!hiddenInput) {
                        hiddenInput = document.createElement('input');
                        hiddenInput.type = 'hidden';
                        hiddenInput.name = 'q';
                        hiddenInput.id = 'hiddenSearchInput';
                        form.appendChild(hiddenInput);
                    }
                    hiddenInput.value = query;
                    
                    // Submit the form
                    form.submit();
                }, 500);
            } else if (window.location.search.includes('q=')) {
                // If search is empty and we're on a search results page, 
                // redirect to all products
                searchSpinner.classList.remove('d-none');
                window.location.href = '{% url "GB_FARM:product_list" %}';
            }
        });
        
        // Listen for enter key
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                clearTimeout(searchTimeout);
                
                // Show spinner
                searchSpinner.classList.remove('d-none');
                
                // Create a form and submit
                const query = this.value.trim();
                const form = document.createElement('form');
                form.method = 'get';
                form.action = '{% url "GB_FARM:product_list" %}';
                
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'q';
                input.value = query;
                
                form.appendChild(input);
                document.body.appendChild(form);
                form.submit();
            }
        });
    }
});
</script>
{% endblock %} 