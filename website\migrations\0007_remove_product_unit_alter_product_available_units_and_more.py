# Generated by Django 4.2.20 on 2025-05-06 11:20

from django.db import migrations, models

# Add RunSQL operation to set a default JSON value for existing rows
# Use '[]' for an empty list
set_default_sql = "UPDATE website_product SET available_units = '[]' WHERE available_units IS NULL OR available_units = '';"

# SQL to revert (optional, can be no-op if reverse is not needed/complex)
reverse_default_sql = migrations.RunSQL.noop

class Migration(migrations.Migration):

    dependencies = [
        ('website', '0006_product_available_units_alter_product_unit_category'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='product',
            name='unit',
        ),
        # Run SQL to set default before AlterField applies stricter constraints
        migrations.RunSQL(set_default_sql, reverse_default_sql),
        migrations.AlterField(
            model_name='product',
            name='available_units',
            field=models.JSONField(blank=True, default=list), # This operation might trigger the check
        ),
        migrations.AlterField(
            model_name='product',
            name='unit_category',
            field=models.CharField(choices=[('FREE WEIGHT', 'Free Weight'), ('KILOGRAM', 'Kilogram'), ('500G', '500g'), ('250G', '250g'), ('PIECE', 'Piece'), ('LITER', 'Liter')], default='PIECE', max_length=20),
        ),
    ]
