# Generated by Django 4.2.20 on 2025-06-18 17:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0024_calendarevent_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProdCat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Name')),
                ('name_ar', models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Name')),
                ('date_from', models.DateField(verbose_name='Date From')),
                ('date_to', models.DateField(verbose_name='Date To')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Product Calendar',
                'verbose_name_plural': 'Product Calendars',
                'ordering': ['date_from', 'name'],
            },
        ),
        migrations.RemoveField(
            model_name='calendarevent',
            name='product_type',
        ),
        migrations.RemoveIndex(
            model_name='productavailability',
            name='website_pro_year_fbadfc_idx',
        ),
        migrations.RemoveIndex(
            model_name='productavailability',
            name='website_pro_product_ab15ba_idx',
        ),
        migrations.AddIndex(
            model_name='productavailability',
            index=models.Index(fields=['product_type', 'year', 'month'], name='website_pro_product_e2815f_idx'),
        ),
        migrations.AddIndex(
            model_name='productavailability',
            index=models.Index(fields=['status'], name='website_pro_status_c9d5f0_idx'),
        ),
        migrations.AddIndex(
            model_name='productavailability',
            index=models.Index(fields=['year'], name='website_pro_year_354454_idx'),
        ),
        migrations.DeleteModel(
            name='CalendarEvent',
        ),
        migrations.AddIndex(
            model_name='prodcat',
            index=models.Index(fields=['date_from', 'date_to'], name='website_pro_date_fr_747995_idx'),
        ),
        migrations.AddIndex(
            model_name='prodcat',
            index=models.Index(fields=['name'], name='website_pro_name_1ae0c8_idx'),
        ),
        migrations.AddIndex(
            model_name='prodcat',
            index=models.Index(fields=['created_at'], name='website_pro_created_f28731_idx'),
        ),
    ]
