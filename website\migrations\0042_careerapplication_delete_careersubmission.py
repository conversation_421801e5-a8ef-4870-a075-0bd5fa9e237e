# Generated by Django 4.2.20 on 2025-06-25 15:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0041_careersubmission'),
    ]

    operations = [
        migrations.CreateModel(
            name='CareerApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('cv', models.FileField(upload_to='cvs/')),
                ('subject', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('submitted_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Career Application',
                'verbose_name_plural': 'Career Applications',
                'ordering': ['-submitted_at'],
            },
        ),
        migrations.DeleteModel(
            name='CareerSubmission',
        ),
    ]
