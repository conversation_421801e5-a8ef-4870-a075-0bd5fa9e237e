# Generated by Django 4.2.20 on 2025-07-22 13:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0068_auto_20250722_1330'),
    ]

    operations = [
        # Remove foreign key relationships first
        migrations.AlterUniqueTogether(
            name='excategoryproduct',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='excategoryproduct',
            name='excategory',
        ),
        migrations.RemoveField(
            model_name='excategoryproduct',
            name='exproduct',
        ),
        migrations.RemoveField(
            model_name='exproduct',
            name='varieties',
        ),
        migrations.AlterUniqueTogether(
            name='exproductvariety',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='exproductvariety',
            name='exproduct',
        ),
        migrations.RemoveField(
            model_name='exproductvariety',
            name='exvariety',
        ),

        # Update NewProduct model options and fields
        migrations.AlterModelOptions(
            name='newcatgroup',
            options={'verbose_name': 'Category Group', 'verbose_name_plural': 'Category Groups'},
        ),
        migrations.AlterModelOptions(
            name='newcatgroupdescription',
            options={'verbose_name': 'Group Description', 'verbose_name_plural': 'Group Descriptions'},
        ),
        migrations.AlterModelOptions(
            name='newproduct',
            options={'verbose_name': 'Product', 'verbose_name_plural': 'Products'},
        ),
        migrations.AlterModelOptions(
            name='newvariety',
            options={'verbose_name': 'Variety', 'verbose_name_plural': 'Varieties'},
        ),
        migrations.RemoveField(
            model_name='newproduct',
            name='image',
        ),
        migrations.AlterField(
            model_name='newcatgroupdescription',
            name='text',
            field=models.TextField(verbose_name='Description Text'),
        ),
        migrations.AlterField(
            model_name='newproduct',
            name='end_date',
            field=models.DateField(verbose_name='End Date'),
        ),
        migrations.AlterField(
            model_name='newproduct',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='Is Active?'),
        ),
        migrations.AlterField(
            model_name='newproduct',
            name='name',
            field=models.CharField(max_length=100, verbose_name='Product Name'),
        ),
        migrations.AlterField(
            model_name='newproduct',
            name='start_date',
            field=models.DateField(verbose_name='Start Date'),
        ),
        migrations.AlterField(
            model_name='newproduct',
            name='weight_from',
            field=models.PositiveIntegerField(verbose_name='Min Weight'),
        ),
        migrations.AlterField(
            model_name='newproduct',
            name='weight_to',
            field=models.PositiveIntegerField(verbose_name='Max Weight'),
        ),
        migrations.AlterField(
            model_name='newvariety',
            name='end_date',
            field=models.DateField(verbose_name='End Date'),
        ),
        migrations.AlterField(
            model_name='newvariety',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='Is Active?'),
        ),
        migrations.AlterField(
            model_name='newvariety',
            name='name',
            field=models.CharField(max_length=100, verbose_name='Variety Name'),
        ),
        migrations.AlterField(
            model_name='newvariety',
            name='start_date',
            field=models.DateField(verbose_name='Start Date'),
        ),
        migrations.AlterField(
            model_name='newvariety',
            name='weight_from',
            field=models.PositiveIntegerField(verbose_name='Min Weight'),
        ),
        migrations.AlterField(
            model_name='newvariety',
            name='weight_to',
            field=models.PositiveIntegerField(verbose_name='Max Weight'),
        ),

        # Delete the old models
        migrations.DeleteModel(
            name='ExCategory',
        ),
        migrations.DeleteModel(
            name='ExCategoryProduct',
        ),
        migrations.DeleteModel(
            name='ExProduct',
        ),
        migrations.DeleteModel(
            name='ExProductVariety',
        ),
        migrations.DeleteModel(
            name='ExVariety',
        ),
    ]
