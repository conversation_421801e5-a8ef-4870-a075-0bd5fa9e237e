# Generated by Django 3.2.15 on 2025-06-25 10:46

from django.db import migrations, models
import imagekit.models.fields
import imagekit.processors


def set_default_grape_data(apps, schema_editor):
    """Set default data for existing grape varieties"""
    GrapeVariety = apps.get_model('website', 'GrapeVariety')
    
    # Sample defaults for existing varieties
    for variety in GrapeVariety.objects.all():
        variety.date_from_day = 1
        variety.date_from_month = 'Jun'
        variety.date_to_day = 30
        variety.date_to_month = 'Aug'
        variety.weight_from = 200
        variety.weight_to = 500
        variety.weight_unit = 'gm'
        variety.save()


def reverse_grape_data(apps, schema_editor):
    """Reverse function - does nothing as we can't restore unknown data"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0036_mangovariety'),
    ]

    operations = [
        # Add new fields with defaults
        migrations.AddField(
            model_name='grapevariety',
            name='date_from_day',
            field=models.PositiveIntegerField(default=1, help_text='Day (1-31)'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='grapevariety',
            name='date_from_month',
            field=models.CharField(default='Jun', help_text='Month abbreviation (Jan, Feb, etc.)', max_length=3),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='grapevariety',
            name='date_to_day',
            field=models.PositiveIntegerField(default=30, help_text='Day (1-31)'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='grapevariety',
            name='date_to_month',
            field=models.CharField(default='Aug', help_text='Month abbreviation (Jan, Feb, etc.)', max_length=3),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='grapevariety',
            name='weight_from',
            field=models.PositiveIntegerField(default=200, help_text='Minimum weight'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='grapevariety',
            name='weight_to',
            field=models.PositiveIntegerField(default=500, help_text='Maximum weight'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='grapevariety',
            name='weight_unit',
            field=models.CharField(choices=[('gm', 'Grams'), ('kg', 'Kilograms')], default='gm', max_length=2),
        ),
        migrations.AddField(
            model_name='grapevariety',
            name='image',
            field=imagekit.models.fields.ProcessedImageField(blank=True, null=True, upload_to='grape_varieties/', processors=[imagekit.processors.ResizeToFill(400, 400)], format='PNG', options={'quality': 90}),
        ),
        # Run the data migration
        migrations.RunPython(set_default_grape_data, reverse_grape_data),
    ]
