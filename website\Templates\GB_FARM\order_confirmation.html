{% extends 'GB_FARM/base.html' %}

{% block title %}Order Confirmation{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="text-center mb-4">
        <i class="fas fa-check-circle text-success fa-5x mb-3"></i>
        <h1 class="mb-3">Thank You for Your Order!</h1>
        <p class="lead mb-4">Your order has been placed successfully.</p>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Order #{{ order.id }}</h5>
                        <span class="badge bg-{% if order.status == 'delivered' %}success{% elif order.status == 'cancelled' %}danger{% elif order.status == 'shipped' %}info{% else %}warning{% endif %}">
                            {{ order.get_status_display }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>Order Details</h6>
                            <p class="mb-1"><strong>Date:</strong> {{ order.created_at|date:"F j, Y" }}</p>
                            <p class="mb-1"><strong>Total Amount:</strong> EGP {{ order.total_amount|floatformat:2 }}</p>
                            <p class="mb-0"><strong>Payment Method:</strong> {{ order.payment_set.first.get_payment_method_display }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Shipping Address</h6>
                            <p>{{ order.shipping_address|linebreaks }}</p>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mb-4">
                        <strong>Note:</strong> A confirmation email has been sent to your email address. You can track your order status in the "My Orders" section.
                    </div>
                    
                    <div class="text-center">
                        <a href="{% url 'GB_FARM:order_details' pk=order.id %}" class="btn btn-primary me-2">
                            <i class="fas fa-info-circle me-1"></i> View Order Details
                        </a>
                        <a href="{% url 'GB_FARM:product_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-shopping-bag me-1"></i> Continue Shopping
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 