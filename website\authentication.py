from django.contrib.auth.backends import BaseBackend
from django.contrib.auth import get_user_model
from .models import APIToken
from django.utils import timezone

User = get_user_model()

class APITokenBackend(BaseBackend):
    def authenticate(self, request, token=None):
        if not token:
            return None

        try:
            api_token = APIToken.objects.select_related('user').get(token=token)
            if not api_token.is_valid():
                return None

            api_token.update_last_used()
            return api_token.user
        except APIToken.DoesNotExist:
            return None

    def get_user(self, user_id):
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None 