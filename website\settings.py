# Cache settings
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'PASSWORD': '',  # Set this if your Redis server requires authentication
            'SOCKET_CONNECT_TIMEOUT': 5,
            'SOCKET_TIMEOUT': 5,
        }
    }
}

# Cache timeouts (in seconds)
CACHE_TTL = {
    'PRODUCT_LIST': 60 * 15,  # 15 minutes
    'PRODUCT_DETAIL': 60 * 30,  # 30 minutes
    'CATEGORY_LIST': 60 * 60,  # 1 hour
    'USER_PROFILE': 60 * 5,  # 5 minutes
}

# Cache timeouts (in seconds)
PRODUCT_LIST_CACHE_TIMEOUT = 900  # 15 minutes
PRODUCT_DETAIL_CACHE_TIMEOUT = 1800  # 30 minutes
CATEGORY_CACHE_TIMEOUT = 3600  # 1 hour
FEATURED_PRODUCTS_CACHE_TIMEOUT = 3600  # 1 hour

# Database optimization settings
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'gb_farm_db',
        'USER': 'postgres',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
        'CONN_MAX_AGE': 60,  # Keep database connections alive for 60 seconds
        'OPTIONS': {
            'client_encoding': 'UTF8',
        }
    }
}

# Image optimization settings
IMAGEKIT_PRIVATE_KEY = 'your_private_key'
IMAGEKIT_PUBLIC_KEY = 'your_public_key'
IMAGEKIT_URL_ENDPOINT = 'https://ik.imagekit.io/your_endpoint'

# Session cache settings
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'

# Celery Configuration
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'Africa/Cairo'

# Celery Beat Schedule
CELERY_BEAT_SCHEDULE = {
    'update-product-stock': {
        'task': 'website.tasks.update_product_stock',
        'schedule': 3600.0,  # Every hour
    },
    'cleanup-expired-offers': {
        'task': 'website.tasks.cleanup_expired_offers',
        'schedule': 86400.0,  # Daily
    },
    'generate-daily-sales-report': {
        'task': 'website.tasks.generate_daily_sales_report',
        'schedule': 86400.0,  # Daily at midnight
    },
}

# Logging Configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'ERROR',
            'class': 'logging.FileHandler',
            'filename': 'logs/error.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'website': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

# Security Settings
SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
X_FRAME_OPTIONS = 'DENY'

# Rate Limiting Settings
RATE_LIMIT = 100  # requests per minute
RATE_LIMIT_WINDOW = 60  # seconds

# Authentication Backends
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
    'website.authentication.APITokenBackend',
]

# Middleware
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'website.middleware.RateLimitMiddleware',
    'website.middleware.ErrorHandlingMiddleware',
]

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'website',
    'imagekit',
     'nested_admin',
] 