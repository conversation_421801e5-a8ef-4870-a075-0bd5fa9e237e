{% extends 'GB_FARM/base.html' %}
{% load static %}

{% block title %}Invoice Logs{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white text-white">
                    <h5 class="mb-0">Admin Dashboard</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:admin_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i> Orders
                    </a>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-box me-2"></i> Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i> Customers
                    </a>
                    <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                    <a href="{% url 'GB_FARM:admin_invoice_logs' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-file-invoice me-2"></i> Invoice Logs
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <h1 class="mb-4">Invoice Logs</h1>
            
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-white bg-primary mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Total Invoices</h5>
                            <p class="card-text display-6">{{ total_invoices }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Total Views</h5>
                            <p class="card-text display-6">{{ total_access_count }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-info mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Unique Orders</h5>
                            <p class="card-text display-6">{{ unique_orders }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-warning mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Unique Users</h5>
                            <p class="card-text display-6">{{ unique_users }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Filters</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="{% url 'GB_FARM:admin_invoice_logs' %}">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="date_from" class="form-label">Date From</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from|default:'' }}">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="date_to" class="form-label">Date To</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to|default:'' }}">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="order_id" class="form-label">Order ID</label>
                                <input type="text" class="form-control" id="order_id" name="order_id" value="{{ order_id|default:'' }}">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="user_id" class="form-label">User ID</label>
                                <input type="text" class="form-control" id="user_id" name="user_id" value="{{ user_id|default:'' }}">
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="{% url 'GB_FARM:admin_invoice_logs' %}" class="btn btn-outline-secondary">Clear Filters</a>
                            <button type="submit" class="btn btn-primary">Apply Filters</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Invoice Log Table -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Invoice Logs</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Order #</th>
                                    <th>Generated By</th>
                                    <th>Created At</th>
                                    <th>Last Accessed</th>
                                    <th>Access Count</th>
                                    <th>Amount</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in invoice_logs %}
                                <tr>
                                    <td>{{ log.invoice_number }}</td>
                                    <td>
                                        <a href="{% url 'GB_FARM:admin_order_detail' order_id=log.order.id %}">
                                            #{{ log.order.id }}
                                        </a>
                                    </td>
                                    <td>
                                        {% if log.generated_by %}
                                            {% if log.generated_by.is_staff %}
                                                <span class="badge bg-primary">Admin</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Customer</span>
                                            {% endif %}
                                            {{ log.generated_by.username }}
                                        {% else %}
                                            <span class="text-muted">Unknown</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ log.created_at|date:"M d, Y H:i" }}</td>
                                    <td>{{ log.last_accessed|date:"M d, Y H:i" }}</td>
                                    <td>{{ log.access_count }}</td>
                                    <td>EGP {{ log.total_amount|floatformat:2 }}</td>
                                    <td>
                                        <a href="{% url 'GB_FARM:generate_invoice' order_id=log.order.id %}" class="btn btn-sm btn-outline-info" target="_blank">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">No invoice logs found.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if invoice_logs.has_other_pages %}
                    <nav aria-label="Invoice logs pagination">
                        <ul class="pagination justify-content-center mt-4">
                            {% if invoice_logs.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if order_id %}&order_id={{ order_id }}{% endif %}{% if user_id %}&user_id={{ user_id }}{% endif %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ invoice_logs.previous_page_number }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if order_id %}&order_id={{ order_id }}{% endif %}{% if user_id %}&user_id={{ user_id }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for i in invoice_logs.paginator.page_range %}
                                {% if invoice_logs.number == i %}
                                <li class="page-item active">
                                    <span class="page-link">{{ i }}</span>
                                </li>
                                {% elif i > invoice_logs.number|add:'-3' and i < invoice_logs.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ i }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if order_id %}&order_id={{ order_id }}{% endif %}{% if user_id %}&user_id={{ user_id }}{% endif %}">{{ i }}</a>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if invoice_logs.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ invoice_logs.next_page_number }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if order_id %}&order_id={{ order_id }}{% endif %}{% if user_id %}&user_id={{ user_id }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ invoice_logs.paginator.num_pages }}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if order_id %}&order_id={{ order_id }}{% endif %}{% if user_id %}&user_id={{ user_id }}{% endif %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 