{% extends "GB_FARM/admin_base.html" %}
{% load static %}

{% block title %}Mango Varieties Management - Admin Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Mango Varieties Management</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addVarietyModal">
            <i class="fas fa-plus"></i> Add New Variety
        </button>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Varieties</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_varieties }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-seedling fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Varieties</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_varieties }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">International</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ international_varieties }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-globe fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Local</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ local_varieties }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-home fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
        </div>
        <div class="card-body">
            <form method="GET" class="form-inline">
                <div class="form-group mr-3 mb-2">
                    <label for="variety_type" class="sr-only">Variety Type</label>
                    <select name="variety_type" id="variety_type" class="form-control">
                        <option value="">All Types</option>
                        {% for value, label in variety_type_choices %}
                            <option value="{{ value }}" {% if selected_variety_type == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group mr-3 mb-2">
                    <label for="status" class="sr-only">Status</label>
                    <select name="status" id="status" class="form-control">
                        <option value="">All Status</option>
                        <option value="active" {% if selected_status == 'active' %}selected{% endif %}>Active</option>
                        <option value="inactive" {% if selected_status == 'inactive' %}selected{% endif %}>Inactive</option>
                    </select>
                </div>

                <div class="form-group mr-3 mb-2">
                    <label for="q" class="sr-only">Search</label>
                    <input type="text" name="q" id="q" class="form-control" placeholder="Search varieties..." value="{{ search_query }}">
                </div>

                <button type="submit" class="btn btn-primary mb-2 mr-2">Filter</button>
                <a href="{% url 'GB_FARM:admin_mango_varieties_dashboard' %}" class="btn btn-secondary mb-2">Clear</a>
            </form>
        </div>
    </div>

    <!-- Mango Varieties Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Mango Varieties</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Type</th>
                            <th>Date Range</th>
                            <th>Weight Range</th>
                            <th>Order</th>
                            <th>Status</th>
                            <th>Image</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for variety in mango_varieties %}
                        <tr>
                            <td>{{ variety.name }}</td>
                            <td>
                                <span class="badge badge-{% if variety.variety_type == 'international' %}info{% else %}warning{% endif %}">
                                    {{ variety.get_variety_type_display }}
                                </span>
                            </td>
                            <td>{{ variety.date_range_display }}</td>
                            <td>{{ variety.weight_range_display }}</td>
                            <td>{{ variety.order }}</td>
                            <td>
                                {% if variety.is_active %}
                                    <span class="badge badge-success">Active</span>
                                {% else %}
                                    <span class="badge badge-secondary">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if variety.image %}
                                    <img src="{{ variety.image.url }}" alt="{{ variety.name }}" style="width: 50px; height: 50px; object-fit: cover;">
                                {% else %}
                                    <span class="text-muted">No image</span>
                                {% endif %}
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary" 
                                        onclick="editVariety({{ variety.id }}, '{{ variety.name }}', '{{ variety.variety_type }}', {{ variety.date_from_day }}, '{{ variety.date_from_month }}', {{ variety.date_to_day }}, '{{ variety.date_to_month }}', {{ variety.weight_from }}, {{ variety.weight_to }}, '{{ variety.weight_unit }}', {{ variety.order }}, {{ variety.is_active|yesno:'true,false' }})">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button type="button" class="btn btn-sm btn-danger" 
                                        onclick="deleteVariety({{ variety.id }}, '{{ variety.name }}')">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">No mango varieties found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if mango_varieties.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if mango_varieties.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ mango_varieties.previous_page_number }}{% if selected_variety_type %}&variety_type={{ selected_variety_type }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}

                    {% for num in mango_varieties.paginator.page_range %}
                        {% if mango_varieties.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > mango_varieties.number|add:'-3' and num < mango_varieties.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if selected_variety_type %}&variety_type={{ selected_variety_type }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if mango_varieties.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ mango_varieties.next_page_number }}{% if selected_variety_type %}&variety_type={{ selected_variety_type }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<!-- Add Variety Modal -->
<div class="modal fade" id="addVarietyModal" tabindex="-1" role="dialog" aria-labelledby="addVarietyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addVarietyModalLabel">Add New Mango Variety</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                {% csrf_token %}
                <input type="hidden" name="action" value="add">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="add_name">Name</label>
                                <input type="text" class="form-control" id="add_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="add_variety_type">Variety Type</label>
                                <select class="form-control" id="add_variety_type" name="variety_type" required>
                                    <option value="">Select Type</option>
                                    {% for value, label in variety_type_choices %}
                                        <option value="{{ value }}">{{ label }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="add_date_from_day">From Day</label>
                                <input type="number" class="form-control" id="add_date_from_day" name="date_from_day" min="1" max="31" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="add_date_from_month">From Month</label>
                                <select class="form-control" id="add_date_from_month" name="date_from_month" required>
                                    <option value="">Select</option>
                                    <option value="Jan">January</option>
                                    <option value="Feb">February</option>
                                    <option value="Mar">March</option>
                                    <option value="Apr">April</option>
                                    <option value="May">May</option>
                                    <option value="Jun">June</option>
                                    <option value="Jul">July</option>
                                    <option value="Aug">August</option>
                                    <option value="Sep">September</option>
                                    <option value="Oct">October</option>
                                    <option value="Nov">November</option>
                                    <option value="Dec">December</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="add_date_to_day">To Day</label>
                                <input type="number" class="form-control" id="add_date_to_day" name="date_to_day" min="1" max="31" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="add_date_to_month">To Month</label>
                                <select class="form-control" id="add_date_to_month" name="date_to_month" required>
                                    <option value="">Select</option>
                                    <option value="Jan">January</option>
                                    <option value="Feb">February</option>
                                    <option value="Mar">March</option>
                                    <option value="Apr">April</option>
                                    <option value="May">May</option>
                                    <option value="Jun">June</option>
                                    <option value="Jul">July</option>
                                    <option value="Aug">August</option>
                                    <option value="Sep">September</option>
                                    <option value="Oct">October</option>
                                    <option value="Nov">November</option>
                                    <option value="Dec">December</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="add_weight_from">Weight From</label>
                                <input type="number" class="form-control" id="add_weight_from" name="weight_from" min="1" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="add_weight_to">Weight To</label>
                                <input type="number" class="form-control" id="add_weight_to" name="weight_to" min="1" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="add_weight_unit">Weight Unit</label>
                                <select class="form-control" id="add_weight_unit" name="weight_unit">
                                    {% for value, label in weight_unit_choices %}
                                        <option value="{{ value }}">{{ label }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="add_order">Order</label>
                                <input type="number" class="form-control" id="add_order" name="order" value="0" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="add_image">Image</label>
                                <input type="file" class="form-control-file" id="add_image" name="image" accept="image/*">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="add_is_active" name="is_active" checked>
                        <label class="form-check-label" for="add_is_active">
                            Active
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Variety</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editVariety(id, name, variety_type, date_from_day, date_from_month, date_to_day, date_to_month, weight_from, weight_to, weight_unit, order, isActive) {
    // Implementation for edit modal
    alert('Edit functionality would open a modal here');
}

function deleteVariety(id, name) {
    if (confirm(`Are you sure you want to delete the mango variety "${name}"?`)) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            {% csrf_token %}
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="variety_id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %} 