{% load static %}

<!-- Footer -->
<footer class="bg-green-950 text-white pt-10 ">
    <div class="flex flex-col md:flex-row items-center justify-center gap-x-10 ">
        <!-- Left Section: Logo and Copyright -->
        <div class="flex flex-col items-center mb-8 md:mb-0">
            <img src="{% static 'images/gbfarmslogo.png' %}" alt="GB Farms Logo" class="h-20 w-auto mb-4">
        </div>

        <!-- Vertical Line (Desktop Only) -->
        <div class="hidden md:block h-40 w-px bg-white mx-8"></div>

        <!-- Middle Section: Contact Info and Email -->
        <div class="flex flex-col md:flex-row items-center md:items-start mb-8 md:mb-0 space-y-4  md:space-x-8 gap-x-10">
            <!-- Contact Info -->
            <div class="flex flex-col items-center md:items-start text-center md:text-left gap-y-2 pt-10">
                {% if external_footer %}
                <div class="flex items-center mb-2">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684L10.5 9l2.592-2.592a1 1 0 01.684-.948H19a2 2 0 012 2v11a2 2 0 01-2 2h-3.28a1 1 0 01-.948-.684L13.5 15l-2.592 2.592a1 1 0 01-.684.948H5a2 2 0 01-2-2V5z"></path></svg>
                    <span>{{ external_footer.phone }}</span>
                </div>
                <div class="flex items-center mb-2">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8m-9 13v-4.72A2.5 2.5 0 0115.5 14H18a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2h2.5A2.5 2.5 0 019 16.28V21"></path></svg>
                    <span>{{ external_footer.email }}</span>
                </div>
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.828 0L6.343 16.657a4 4 0 115.656-5.656 4 4 0 010 5.656z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>
                    <span>{{ external_footer.address }}</span>
                </div>
                {% endif %}
            </div>

            <!-- Vertical Line (Desktop Only) -->
            <div class="hidden md:block h-40 w-px bg-white mx-8"></div>

            <!-- Subscription Form -->
            <div class="flex flex-col items-center md:items-start text-center md:text-left max-w-sm">
                <h3 class="text-xl font-bold mb-4">KEEP UP TO DATE WITH INSPIRING STORIES, LAUNCHES AND EVENTS FROM GB FARMS</h3>
                <form class="flex flex-col md:flex-row w-full" method="post" action="{% url 'GB_FARM:subscribe_newsletter' %}" id="newsletter-form">
                    {% csrf_token %}
                    <input type="hidden" name="source_page" value="{{ request.resolver_match.url_name|default:'external_index' }}">
                    <input type="email" name="email" placeholder="YOUR MAIL" class="bg-transparent border border-white text-white placeholder-white focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-green-400 rounded-lg px-4 py-3 w-full md:w-68 mb-4 md:mb-0 md:mr-2 transition-all duration-300" required>
                    <button type="submit" class="bg-green-600 hover:bg-green-700 active:bg-green-800 text-white font-bold py-3 px-8 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 w-full md:w-auto border-2 border-green-600 hover:border-green-700">
                        SUBSCRIBE
                    </button>
                </form>
                <p class="text-xs text-white mt-2">
                    *By Signing Up For This Mail, You Are Agreeing To News, Offers, And Information From GB Farms.
                </p>
            </div>
        </div>
    </div>

    <!-- Bottom Section: Copyright and Navigation -->
    <div class="bg-green-700 text-center py-4 mt-8">
        <div class="container mx-auto flex flex-col md:flex-row justify-center items-center text-sm px-4 gap-x-20">
            {% if external_footer %}<p class="mb-2 md:mb-0">{{ external_footer.copyright_text }}</p>{% endif %}
            <div class="flex space-x-4">
                <a href="{% url 'GB_FARM:external_index' %}" class="hover:underline text-white">HOME</a>
                <div class="hidden md:block h-6 w-px bg-white mx-8"></div>
                <a href="{% url 'GB_FARM:external_about' %}" class="hover:underline text-white      ">ABOUT US</a>
                <div class="hidden md:block h-6 w-px bg-white mx-8"></div>
                <a href="{% url 'GB_FARM:ex_all_product' %}" class="hover:underline text-white">PRODUCTS</a>
                <div class="hidden md:block h-6 w-px bg-white mx-8"></div>
                <a href="{% url 'GB_FARM:external_contact' %}" class="hover:underline text-white">CONTACT US</a>
                <div class="hidden md:block h-6 w-px bg-white mx-8"></div>
                <a href="{% url 'GB_FARM:external_career' %}" class="hover:underline text-white">CAREERS</a>   
                <div class="hidden md:block h-6 w-px bg-white mx-8"></div>
                <a href="{% url 'GB_FARM:external_calendar' %}" class="hover:underline text-white">CALENDAR</a>
                <div class="hidden md:block h-6 w-px bg-white mx-8"></div>
            </div>
            {% if external_footer %}
            <div class="flex space-x-4 mt-2 md:mt-0">
                <a href="{{ external_footer.instagram_url|default:'#' }}" class="text-white hover:text-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                    </svg>
                </a>
                <a href="{{ external_footer.youtube_url|default:'#' }}" class="text-white hover:text-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                    </svg>
                </a>
                <a href="{{ external_footer.facebook_url|default:'#' }}" class="text-white hover:text-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                    </svg>
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</footer>

<script>
// Newsletter form submission handling
document.addEventListener('DOMContentLoaded', function() {
    const newsletterForm = document.getElementById('newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            const emailInput = this.querySelector('input[name="email"]');
            const submitButton = this.querySelector('button[type="submit"]');

            if (emailInput && emailInput.value.trim() === '') {
                e.preventDefault();
                alert('Please enter your email address.');
                emailInput.focus();
                return false;
            }

            // Disable submit button to prevent double submission
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.textContent = 'SUBSCRIBING...';

                // Re-enable after 3 seconds in case of error
                setTimeout(function() {
                    submitButton.disabled = false;
                    submitButton.textContent = 'SUBSCRIBE';
                }, 3000);
            }
        });
    }
});
</script>









































