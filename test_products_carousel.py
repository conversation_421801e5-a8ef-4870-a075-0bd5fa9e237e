#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.test import Client

def test_products_carousel():
    """Test that the products carousel is properly implemented"""
    print("🧪 Testing Products Carousel Implementation...")
    print("=" * 60)
    
    client = Client()
    
    try:
        # Test home page loads
        response = client.get('/')
        print(f"✅ Home page loads successfully (Status: {response.status_code})")
        
        content = response.content.decode('utf-8')
        
        # Check for carousel container
        if 'id="products-carousel"' in content:
            print("✅ Products carousel container found")
        else:
            print("❌ Products carousel container NOT found")
        
        # Check for carousel data attributes
        if 'data-carousel="static"' in content:
            print("✅ Carousel data attribute found")
        else:
            print("❌ Carousel data attribute NOT found")
        
        # Check for carousel items
        carousel_items = [
            'data-carousel-item="active"',  # First item (Grapes)
            'data-carousel-item',           # Other items
        ]
        
        for item in carousel_items:
            if item in content:
                print(f"✅ Carousel item found: {item}")
            else:
                print(f"❌ Carousel item NOT found: {item}")
        
        # Check for product links
        product_links = [
            "{% url 'GB_FARM:external_grapes' %}",
            "{% url 'GB_FARM:external_mangoes' %}",
            "{% url 'GB_FARM:external_others' %}"
        ]
        
        for link in product_links:
            if link in content:
                print(f"✅ Product link found: {link}")
            else:
                print(f"❌ Product link NOT found: {link}")
        
        # Check for product images
        product_images = [
            "images/card1.png",    # Grapes
            "images/card3.png",    # Mangoes
            "images/mashmah.png"   # Others
        ]
        
        for image in product_images:
            if image in content:
                print(f"✅ Product image found: {image}")
            else:
                print(f"❌ Product image NOT found: {image}")
        
        # Check for carousel controls
        controls = [
            'data-carousel-prev',  # Previous button
            'data-carousel-next',  # Next button
            'data-carousel-slide-to' # Indicators
        ]
        
        for control in controls:
            if control in content:
                print(f"✅ Carousel control found: {control}")
            else:
                print(f"❌ Carousel control NOT found: {control}")
        
        # Check for product titles
        product_titles = ['Grapes', 'Mangoes', 'Other Products']
        
        for title in product_titles:
            if title in content:
                print(f"✅ Product title found: {title}")
            else:
                print(f"❌ Product title NOT found: {title}")
        
        # Check for auto-play JavaScript
        if 'startAutoPlay' in content:
            print("✅ Auto-play JavaScript found")
        else:
            print("❌ Auto-play JavaScript NOT found")
        
        # Check for hover pause functionality
        if 'mouseenter' in content and 'mouseleave' in content:
            print("✅ Hover pause functionality found")
        else:
            print("❌ Hover pause functionality NOT found")
        
        # Check for Flowbite integration
        if 'flowbite' in content.lower():
            print("✅ Flowbite integration found")
        else:
            print("❌ Flowbite integration NOT found")
        
        print("\n" + "=" * 60)
        print("✅ Products carousel testing completed!")
        
        print("\n💡 Carousel Features:")
        print("   🎠 3 product slides (Grapes, Mangoes, Others)")
        print("   🔄 Auto-play every 4 seconds")
        print("   ⏸️  Pause on hover")
        print("   🎮 Manual navigation controls")
        print("   📍 Slide indicators")
        print("   📱 Responsive design")
        print("   🔗 Clickable product links")
        print("   🎨 Overlay titles with hover effects")
        
    except Exception as e:
        print(f"❌ Error testing carousel: {e}")

if __name__ == "__main__":
    test_products_carousel()
