{% extends 'GB_FARM/base.html' %}
{% load static %}

{% block title %}Admin Customer Dashboard{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white text-white">
                    <h5 class="mb-0">Admin Dashboard</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:admin_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i> Orders
                    </a>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-box me-2"></i> Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-users me-2"></i> Customers
                    </a>
                    <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                    <a href="{% url 'GB_FARM:admin_invoice_logs' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-file-invoice me-2"></i> Invoice Logs
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-body text-center">
                            <h6 class="text-muted">Total Customers</h6>
                            <h2 class="mb-0">{{ total_customers }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-body text-center">
                            <h6 class="text-muted">Active Customers</h6>
                            <h2 class="mb-0">{{ active_customers }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card shadow-sm h-100">
                        <div class="card-body text-center">
                            <h6 class="text-muted">Customers with Orders</h6>
                            <h2 class="mb-0">{{ customers_with_orders }}</h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter and Search -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-5">
                            <label for="q" class="form-label">Search</label>
                            <input type="text" class="form-control" id="q" name="q" placeholder="Search by name, email..." value="{{ search_query }}">
                        </div>
                        <div class="col-md-3">
                            <label for="user_type" class="form-label">User Type</label>
                            <select name="user_type" id="user_type" class="form-select">
                                <option value="">All Types</option>
                                <option value="customer" {% if user_type == 'customer' %}selected{% endif %}>Customer</option>
                                <option value="admin" {% if user_type == 'admin' %}selected{% endif %}>Admin</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="order_count" class="form-label">Order Status</label>
                            <select name="order_count" id="order_count" class="form-select">
                                <option value="">All Customers</option>
                                <option value="none" {% if order_count == 'none' %}selected{% endif %}>No Orders</option>
                                <option value="one" {% if order_count == 'one' %}selected{% endif %}>One Order</option>
                                <option value="multiple" {% if order_count == 'multiple' %}selected{% endif %}>Multiple Orders</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-1"></i> Apply Filters
                            </button>
                            <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-sync-alt me-1"></i> Reset
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Customers Table -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Customers</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Username</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Joined</th>
                                    <th>Orders</th>
                                    <th>Total Spent</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in customers %}
                                <tr>
                                    <td>{{ customer.id }}</td>
                                    <td>{{ customer.username }}</td>
                                    <td>{{ customer.get_full_name|default:"-" }}</td>
                                    <td>{{ customer.email|default:"-" }}</td>
                                    <td>{{ customer.date_joined|date:"M d, Y" }}</td>
                                    <td>{{ customer.total_orders }}</td>
                                    <td>EGP {{ customer.total_spent|default:0|floatformat:2 }}</td>
                                    <td>
                                        <a href="{% url 'GB_FARM:admin_customer_detail' customer.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center py-4">No customers found</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if customers.has_other_pages %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if customers.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&q={{ search_query }}{% endif %}{% if user_type %}&user_type={{ user_type }}{% endif %}{% if order_count %}&order_count={{ order_count }}{% endif %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ customers.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if user_type %}&user_type={{ user_type }}{% endif %}{% if order_count %}&order_count={{ order_count }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for i in customers.paginator.page_range %}
                                {% if customers.number == i %}
                                <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                                {% elif i > customers.number|add:'-3' and i < customers.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ i }}{% if search_query %}&q={{ search_query }}{% endif %}{% if user_type %}&user_type={{ user_type }}{% endif %}{% if order_count %}&order_count={{ order_count }}{% endif %}">
                                        {{ i }}
                                    </a>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if customers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ customers.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if user_type %}&user_type={{ user_type }}{% endif %}{% if order_count %}&order_count={{ order_count }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ customers.paginator.num_pages }}{% if search_query %}&q={{ search_query }}{% endif %}{% if user_type %}&user_type={{ user_type }}{% endif %}{% if order_count %}&order_count={{ order_count }}{% endif %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 