{% extends "Extrnal.html/EX.base.html" %}
{% load static %}

{% block head %}
    <title>Grapes - GB Farms</title>
    <style>
        .grapes-hero-bg {
            background-image: url("{% static 'images/prodect graps/headr.jpeg' %}");
            background-size: cover;
            background-position: center;
        }
    </style>
{% endblock %}

{% block header %}
    {% include "Extrnal.html/navebar.html" %}
{% endblock %}

{% block content %}

    <!-- Main Section - Grapes Product Page -->
    <main class="relative h-[80vh] md:h-[90vh] w-full flex items-center justify-start grapes-hero-bg">
        <div class="pl-4 pt-20 md:pl-20 lg:pl-40 md:pt-24 max-w-5xl z-10">
            {% include 'Extrnal.html/includes/product_hero_content.html' %}
        </div>
    </main>

    <!-- second section -->
    <section class="relative h-auto md:h-[70vh] w-full bg-white flex flex-col items-center justify-center mb-16 md:mb-32 gap-y-6 md:gap-y-10 py-16 md:py-0">
        <!-- Background pattern -->
        <div
            class="absolute inset-0 bg-[url('https://flowbite.s3.amazonaws.com/docs/jumbotron/hero-pattern.svg')] opacity-10">
        </div>

        <!-- Product cards container -->
        <div class="flex flex-col md:flex-row justify-between gap-4 z-10 w-full px-4 md:px-48">
            <!-- Grapes Card -->
            <a href="{% url 'GB_FARM:external_grapes' %}" class="group">
                <img src="{% static 'images/card1.png' %}" alt="Grapes"
                    class="w-full md:w-96 h-64 md:h-full rounded-xl transition-transform duration-300 group-hover:scale-105 object-cover">
            </a>

            <!-- Mangoes Card -->
            <a href="{% url 'GB_FARM:external_mangoes' %}" class="group">
                <img src="{% static 'images/card3.png' %}" alt="Mangoes"
                    class="w-full md:w-96 h-64 md:h-full rounded-xl transition-transform duration-300 group-hover:scale-105 object-cover">
            </a>

            <!-- Others Card -->
            <a href="{% url 'GB_FARM:external_others' %}" class="group">
                <img src="{% static 'images/mashmah.png' %}" alt="Other Products"
                    class="w-full md:w-96 h-64 md:h-full rounded-xl transition-transform duration-300 group-hover:scale-105 object-cover">
            </a>
        </div>
    </section>

    <!--Third section-->
    <section class="h-auto w-full bg-gray-50 flex flex-col items-center py-16 md:py-20 relative overflow-hidden">
        <!-- Background curved line SVG -->
        <div class="absolute top-0 right-0 w-full h-full opacity-20">
            <svg viewBox="0 0 1200 600" class="w-full h-full" preserveAspectRatio="none">
                <!-- Curved line matching the image -->
                <path d="M400 100 Q 800 50, 1100 200 Q 1150 250, 1200 350" 
                      stroke="#22c55e" 
                      stroke-width="3" 
                      fill="none" 
                      stroke-dasharray="10,5"/>
                <!-- Additional curved element -->
                <path d="M300 400 Q 600 350, 900 450 Q 1000 480, 1100 500" 
                      stroke="#22c55e" 
                      stroke-width="2" 
                      fill="none" 
                      opacity="0.6"/>
            </svg>
        </div>

        <div class="container mx-auto px-4 md:px-8 relative z-10">
            <!-- Title with decorative line -->
            <div class="relative mb-12 md:mb-20">
                <h2 class="text-green-500 text-4xl md:text-8xl font-bold">Grapes</h2>
                <!-- Decorative line extending from title -->
                <div class="absolute top-6 md:top-12 left-32 md:left-80 w-1/3 md:w-2/3 h-1 bg-green-500"></div>
                <!-- Extended decorative line -->
                <div class="absolute top-6 md:top-12 left-40 md:left-[32rem] w-1/3 md:w-2/3 h-1 bg-green-500 opacity-60"></div>
            </div>
            
            <!-- Content layout matching the image -->
            <div class="flex flex-wrap items-start">
                <!-- Left column text -->
                <div class="w-full md:w-1/2 md:pr-12 space-y-6 mb-8 md:mb-0">
                    <p class="text-green-500 text-base md:text-lg leading-relaxed">
                        We can't be prouder of our grape vineyards. Covering 
                        an area of 650 acres, our grape production has reached 
                        more than 3,000 tons.
                    </p>
                    
                    <p class="text-green-500 text-base md:text-lg leading-relaxed">
                        Alongside our team of highly skilled technical 
                        engineers, the assistance of world-class production, 
                        post-harvest consultants, and our fully dedicated 
                        management, our
                    </p>
                </div>
                
                <!-- Right column text -->
                <div class="w-full md:w-1/2 md:pl-12 space-y-6">
                    <p class="text-green-500 text-base md:text-lg leading-relaxed">
                        grape exports have gained enormous success 
                        throughout the years.
                    </p>
                    
                    <p class="text-green-500 text-base md:text-lg leading-relaxed">
                        The growing demand for our grape exports drove us to 
                        expand our plantation and focus on varietal 
                        development to meet the new market demands. Hence 
                        our initiative to have the new 200 acres that are already 
                        in our pipelines to be fully complete by 2025.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!--White Seedless Section-->
    <section class="h-auto w-full bg-gray-50 py-16 md:py-20 relative overflow-hidden">
        <!-- Background curved line SVG -->
        <div class="absolute bottom-0 left-0 w-full h-full opacity-10">
            <svg viewBox="0 0 1200 600" class="w-full h-full" preserveAspectRatio="none">
                <!-- Main curved line from bottom left -->
                <path d="M0 500 Q 300 400, 600 350 Q 900 300, 1200 200" 
                      stroke="#22c55e" 
                      stroke-width="3" 
                      fill="none" 
                      stroke-dasharray="15,8"/>
            </svg>
        </div>

        <div class="container mx-auto px-4 md:px-8 relative z-10">
            <div class="flex flex-wrap items-start">
                <!-- Left side - Varieties content -->
                <div class="w-full md:w-2/3 md:pr-12 mb-8 md:mb-0">
                    <!-- Title -->
                    <div class="mb-12 md:mb-16">
                        <h2 class="text-pink-500 text-3xl md:text-5xl font-bold leading-tight">
                            White<br>
                            <span class="text-pink-400">Seedless</span>
                        </h2>
                    </div>
                    
                    <!-- Dynamic White Varieties Grid -->
                    <div class="space-y-6 md:space-y-8">
                        {% if grape_varieties.white %}
                            {% for variety in grape_varieties.white %}
                                {% if forloop.counter0|divisibleby:3 %}
                                    {% if not forloop.first %}</div>{% endif %}
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 {% if not forloop.last %}pb-6 md:pb-8 border-b-2 border-pink-500{% endif %}">
                                {% endif %}
                                
                                <div class="text-center {% if not forloop.counter0|divisibleby:3 and not forloop.last %}md:border-l  border-gray-300 md:px-4{% endif %}">
                                    <h3 class="text-pink-500 text-xl md:text-2xl font-bold mb-2">{{ variety.name }}</h3>
                                    <p class="text-gray-600 text-sm mb-1">{{ variety.date_range_display }}</p>
                                    <p class="text-gray-600 text-sm">{{ variety.weight_range_display }}</p>
                                </div>
                                
                                {% if forloop.last %}
                                    </div>
                                {% endif %}
                                    {% endfor %}
                            {% else %}
                            <div class="text-center py-8">
                                <p class="text-gray-500">No white seedless varieties available.</p>
                            </div>
                            {% endif %}
                        </div>
                </div>
                
                <!-- Right side - Image -->
                <div class="w-full md:w-1/3 flex justify-center items-start">
                    <div class="relative">
                        <img src="{% static 'images/prodect graps/white .png' %}" 
                             alt="White Seedless Grapes" 
                             class="h-auto max-w-full w-64 md:w-80 drop-shadow-2xl">
                        <!-- Optional decorative element -->
                        <div class="absolute -bottom-4 -right-4 w-20 h-20 bg-pink-100 rounded-full opacity-20"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Red Seedless Section -->
    <section class="h-auto w-full bg-gray-50 py-16 md:py-20 relative overflow-hidden">
        <!-- Background curved line SVG -->
        <div class="absolute top-0 left-0 w-full h-full opacity-10">
            <svg viewBox="0 0 1200 600" class="w-full h-full" preserveAspectRatio="none">
                <!-- Main curved line from top right -->
                <path d="M1200 100 Q 900 150, 600 250 Q 300 350, 0 400" 
                      stroke="#3b82f6" 
                      stroke-width="3" 
                      fill="none" 
                      stroke-dasharray="15,8"/>
            </svg>
        </div>

        <div class="container mx-auto px-4 md:px-8 relative z-10">
            <div class="flex flex-wrap items-start">
                <!-- Left side - Image -->
                <div class="w-full md:w-1/3 flex justify-center items-start mb-8 md:mb-0 order-2 md:order-1">
                    <div class="relative">
                        <img src="{% static 'images/prodect graps/red.png' %}" 
                             alt="Red Seedless Grapes" 
                             class="h-auto max-w-full w-64 md:w-80 drop-shadow-2xl">
                        <!-- Decorative circle (like in the image) -->
                        <div class="absolute -bottom-4 -left-4 w-20 h-20 bg-blue-100 rounded-full opacity-20"></div>
                    </div>
                </div>
                
                <!-- Right side - Content -->
                <div class="w-full md:w-2/3 md:pl-12 order-1 md:order-2 mb-8 md:mb-0">
                    <div class="mb-12 md:mb-16">
                        <h2 class="text-blue-600 text-3xl md:text-5xl font-bold leading-tight text-center md:text-right">
                            Red<br>
                            <span class="text-blue-400">Seedless</span>
                        </h2>
                    </div>
                    
                    <!-- Dynamic Red Varieties Grid -->
                    <div class="space-y-6 md:space-y-8">
                        {% if grape_varieties.red %}
                            {% for variety in grape_varieties.red %}
                                {% if forloop.counter0|divisibleby:3 %}
                                    {% if not forloop.first %}</div>{% endif %}
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 {% if not forloop.last %}pb-6 md:pb-8 border-b-2 border-blue-600{% endif %}">
                                {% endif %}
                                
                                <div class="text-center {% if not forloop.counter0|divisibleby:3 and not forloop.last %}md:border-l  border-gray-300 md:px-4{% endif %}">
                                    <h3 class="text-blue-600 text-xl md:text-2xl font-bold mb-2">{{ variety.name }}</h3>
                                    <p class="text-gray-600 text-sm mb-1">{{ variety.date_range_display }}</p>
                                    <p class="text-gray-600 text-sm">{{ variety.weight_range_display }}</p>
                                </div>
                                
                                {% if forloop.last %}
                                    </div>
                                {% endif %}
                                    {% endfor %}
                            {% else %}
                            <div class="text-center py-8">
                                <p class="text-gray-500">No red seedless varieties available.</p>
                            </div>
                            {% endif %}
                        </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Black Seedless Section -->
    <section class="h-auto w-full bg-gray-50 py-16 md:py-20 relative overflow-hidden">
        <!-- Background curved line SVG -->
        <div class="absolute bottom-0 left-0 w-full h-full opacity-10">
            <svg viewBox="0 0 1200 600" class="w-full h-full" preserveAspectRatio="none">
                <!-- Main curved line from bottom left -->
                <path d="M0 500 Q 300 400, 600 350 Q 900 300, 1200 200" 
                      stroke="#eab308" 
                      stroke-width="3" 
                      fill="none" 
                      stroke-dasharray="15,8"/>
            </svg>
        </div>

        <div class="container mx-auto px-4 md:px-8 relative z-10">
            <div class="flex flex-wrap items-start">
                <!-- Left side - Varieties content -->
                <div class="w-full md:w-2/3 md:pr-12 mb-8 md:mb-0">
                    <!-- Title -->
                    <div class="mb-12 md:mb-16">
                        <h2 class="text-yellow-500 text-3xl md:text-5xl font-bold leading-tight">
                            Black<br>
                            <span class="text-yellow-400">Seedless</span>
                        </h2>
                    </div>
                    
                    <!-- Dynamic Black Varieties Grid -->
                    <div class="space-y-6 md:space-y-8">
                        {% if grape_varieties.black %}
                            {% for variety in grape_varieties.black %}
                                {% if forloop.counter0|divisibleby:3 %}
                                    {% if not forloop.first %}</div>{% endif %}
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 {% if not forloop.last %}pb-6 md:pb-8 border-b-2 border-yellow-500{% endif %}    ">
                                {% endif %}
                                
                                <div class="text-center {% if not forloop.counter0|divisibleby:3 and not forloop.last %}md:border-l  border-gray-300 md:px-4{% endif %}">
                                    <h3 class="text-yellow-500 text-xl md:text-2xl font-bold mb-2">{{ variety.name }}</h3>
                                    <p class="text-gray-600 text-sm mb-1">{{ variety.date_range_display }}</p>
                                    <p class="text-gray-600 text-sm">{{ variety.weight_range_display }}</p>
                                </div>
                                
                                {% if forloop.last %}
                                    </div>
                                {% endif %}
                                    {% endfor %}
                            {% else %}
                            <div class="text-center py-8">
                                <p class="text-gray-500">No black seedless varieties available.</p>
                            </div>
                            {% endif %}
                        </div>
                </div>
                
                <!-- Right side - Image -->
                <div class="w-full md:w-1/3 flex justify-center items-start">
                    <div class="relative">
                        <img src="{% static 'images/prodect graps/black.png' %}" 
                             alt="Black Seedless Grapes" 
                             class="h-auto max-w-full w-64 md:w-80 drop-shadow-2xl">
                        <!-- Optional decorative element -->
                        <div class="absolute -bottom-4 -right-4 w-20 h-20 bg-yellow-100 rounded-full opacity-20"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block footer %}
    {% include "Extrnal.html/footer.html" %}
{% endblock %}