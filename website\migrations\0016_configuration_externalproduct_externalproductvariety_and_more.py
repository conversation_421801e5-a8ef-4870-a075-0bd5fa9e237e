# Generated by Django 4.2.20 on 2025-06-02 11:05

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import imagekit.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0015_externalpeopleproduct'),
    ]

    operations = [
        migrations.CreateModel(
            name='Configuration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_on', models.DateTimeField(auto_now=True, verbose_name='Updated On')),
                ('leasing_short_description', models.TextField(blank=True, help_text='Short description for Leasing in header', null=True, verbose_name='Leasing Short Description')),
                ('factoring_short_description', models.TextField(blank=True, help_text='Short description for Factoring in header', null=True, verbose_name='Factoring Short Description')),
                ('company_overview_description', models.TextField(blank=True, null=True, verbose_name='Company Overview Description')),
                ('company_overview_image1', models.ImageField(blank=True, null=True, upload_to='company_overview/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'ico', 'svg', 'gif'])], verbose_name='Company Overview IMG1')),
                ('company_overview_image2', models.ImageField(blank=True, null=True, upload_to='company_overview/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'ico', 'svg', 'gif'])], verbose_name='Company Overview IMG2')),
                ('governance_description', models.TextField(blank=True, null=True, verbose_name='Governance Description')),
                ('about_us_short_description', models.TextField(blank=True, help_text='Short description for About Us in header', null=True, verbose_name='About Us Short Description')),
                ('linkedin_url', models.URLField(blank=True, null=True, verbose_name='LinkedIn URL')),
                ('emails', models.TextField(blank=True, help_text='Emails For Requests', null=True, verbose_name='Emails')),
                ('home_SEO_description', models.TextField(blank=True, help_text='Home page SEO Description', null=True, verbose_name='Home SEO Description')),
                ('home_SEO_key_words', models.TextField(blank=True, help_text='Home page SEO Key Words', null=True, verbose_name='Home SEO Key Words')),
                ('about_SEO_description', models.TextField(blank=True, help_text='About page SEO Description', null=True, verbose_name='About SEO Description')),
                ('about_SEO_key_words', models.TextField(blank=True, help_text='About page SEO Key Words', null=True, verbose_name='About SEO Key Words')),
                ('contact_SEO_description', models.TextField(blank=True, help_text='Contact page SEO Description', null=True, verbose_name='Contact SEO Description')),
                ('contact_SEO_key_words', models.TextField(blank=True, help_text='Contact page SEO Key Words', null=True, verbose_name='Contact SEO Key Words')),
                ('news_SEO_description', models.TextField(blank=True, help_text='News page SEO Description', null=True, verbose_name='News SEO Description')),
                ('news_SEO_key_words', models.TextField(blank=True, help_text='News page SEO Key Words', null=True, verbose_name='News SEO Key Words')),
                ('careers_SEO_description', models.TextField(blank=True, help_text='Careers page SEO Description', null=True, verbose_name='Careers SEO Description')),
                ('careers_SEO_key_words', models.TextField(blank=True, help_text='Careers page SEO Key Words', null=True, verbose_name='Careers SEO Key Words')),
                ('leasing_benefits_SEO_description', models.TextField(blank=True, help_text='Leasing benefits page SEO Description', null=True, verbose_name='Leasing Benefits SEO Description')),
                ('leasing_benefits_SEO_key_words', models.TextField(blank=True, help_text='Leasing benefits page SEO Key Words', null=True, verbose_name='Leasing Benefits SEO Key Words')),
                ('factoring_benefits_SEO_description', models.TextField(blank=True, help_text='Factoring benefits page SEO Description', null=True, verbose_name='Factoring Benefits SEO Description')),
                ('factoring_benefits_SEO_key_words', models.TextField(blank=True, help_text='Factoring benefits page SEO Key Words', null=True, verbose_name='Factoring Benefits SEO Key Words')),
            ],
            options={
                'verbose_name': 'Configuration',
                'verbose_name_plural': 'Configuration',
            },
        ),
        migrations.CreateModel(
            name='ExternalProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('name_ar', models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Name')),
                ('description', models.TextField(blank=True, null=True)),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('image', imagekit.models.fields.ProcessedImageField(blank=True, null=True, upload_to='external_products/')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'External Product',
                'verbose_name_plural': 'External Products',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ExternalProductVariety',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('name_ar', models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Name')),
                ('description', models.TextField(blank=True, null=True)),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('image', imagekit.models.fields.ProcessedImageField(blank=True, null=True, upload_to='external_product_varieties/')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('external_product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='varieties', to='website.externalproduct')),
            ],
            options={
                'verbose_name': 'External Product Variety',
                'verbose_name_plural': 'External Product Varieties',
                'ordering': ['name'],
                'unique_together': {('external_product', 'name')},
            },
        ),
        migrations.DeleteModel(
            name='ExternalPeopleProduct',
        ),
    ]
