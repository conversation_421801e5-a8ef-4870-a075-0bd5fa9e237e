{% extends 'GB_FARM/admin_base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}Order #{{ order.id }} Messages{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Messages for Order #{{ order.id }}</h5>
                    <a href="{% url 'GB_FARM:admin_order_detail' order_id=order.id %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Order Details
                    </a>
                </div>
                <div class="card-body">
                    <!-- Order Info Summary -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Order Details</h6>
                                    <p class="mb-1"><strong>Customer:</strong> {{ order.user.username }}</p>
                                    <p class="mb-1"><strong>Email:</strong> {{ order.user.email }}</p>
                                    <p class="mb-1"><strong>Date:</strong> {{ order.created_at|date:"F d, Y H:i" }}</p>
                                    <p class="mb-1"><strong>Amount:</strong> EGP {{ order.total_amount }}</p>
                                    <p class="mb-1"><strong>Status:</strong> 
                                        <span class="badge bg-{% if order.status == 'delivered' %}success{% elif order.status == 'cancelled' %}danger{% elif order.status == 'shipped' %}info{% else %}warning{% endif %}">
                                            {{ order.get_status_display }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Shipping Address</h6>
                                    <p>{{ order.shipping_address|linebreaks }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Items -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Order Items</h6>
                                </div>
                                <div class="card-body p-0">
                                    <div class="table-responsive">
                                        <table class="table mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Product</th>
                                                    <th>Price</th>
                                                    <th>Quantity</th>
                                                    <th>Total</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for item in order_items %}
                                                <tr>
                                                    <td>{{ item.product.name }}</td>
                                                    <td>EGP {{ item.price }}</td>
                                                    <td>{{ item.quantity }}</td>
                                                    <td>EGP {{ item.price|multiply:item.quantity|floatformat:2 }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Message Form -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Send Message to Customer</h6>
                                </div>
                                <div class="card-body">
                                    <form method="post">
                                        {% csrf_token %}
                                        <div class="mb-3">
                                            <label for="message" class="form-label">Message</label>
                                            <textarea class="form-control" id="message" name="message" rows="4" required></textarea>
                                            <small class="form-text text-muted">Include information like order amount, estimated delivery, office number, or special instructions.</small>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Send Message</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Message History -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Message History</h6>
                                </div>
                                <div class="card-body">
                                    {% if order_messages %}
                                        <div class="message-history">
                                            {% for message in order_messages %}
                                                <div class="message-item mb-3 {% if message.sender == request.user %}sent{% else %}received{% endif %}">
                                                    <div class="message-content p-3 rounded {% if message.sender == request.user %}bg-primary text-white{% else %}bg-light{% endif %}">
                                                        <p class="mb-1">{{ message.message }}</p>
                                                        <div class="message-meta d-flex justify-content-between align-items-center mt-2">
                                                            <small>{{ message.sender.username }}</small>
                                                            <small>{{ message.created_at|date:"M d, Y H:i" }}</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <p class="text-muted">No messages yet.</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Add template helpers if needed
    });
</script>
{% endblock %} 