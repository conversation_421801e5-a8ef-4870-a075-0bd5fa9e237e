{% extends 'GB_FARM/admin_base.html' %}
{% load static %}

{% block title %}Edit Product - {{ product.name }}{% endblock %}

{% block admin_content %}
<div class="card shadow-sm">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Edit Product</h5>
        <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Products
        </a>
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data" id="productForm">
            {% csrf_token %}
            
            {% if messages %}
            <div class="messages mb-3">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {{ message }}
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <div class="row">
                <!-- Basic Information -->
                <div class="col-md-8">
                    <h6 class="mb-3">Basic Information</h6>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Product Name</label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ product.name }}" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="4">{{ product.description }}</textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">Price (EGP)</label>
                                <input type="number" class="form-control" id="price" name="price" value="{{ product.price }}" step="0.01" min="0" required onchange="updateSubtotal()">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="stock" class="form-label">Stock</label>
                                <input type="number" class="form-control" id="stock" name="stock" value="{{ product.stock }}" min="0" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">Select Category</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}" {% if category == product.category %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="unit" class="form-label">Unit</label>
                                <select class="form-select" id="unit" name="unit">
                                    {% for unit_value, unit_label in unit_choices %}
                                    <option value="{{ unit_value }}" {% if unit_value == product.unit %}selected{% endif %}>
                                        {{ unit_label }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Quantity and Subtotal Calculator -->
                    <div class="card mt-3 mb-3">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Quick Calculator</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="calc_quantity" class="form-label">Quantity</label>
                                        <input type="number" class="form-control" id="calc_quantity" value="1" min="1" onchange="updateSubtotal()">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="calc_subtotal" class="form-label">Subtotal (EGP)</label>
                                        <input type="text" class="form-control" id="calc_subtotal" readonly>
                                    </div>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="button" class="btn btn-outline-primary w-100" onclick="resetCalculator()">
                                        Reset Calculator
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Image Upload -->
                <div class="col-md-4">
                    <h6 class="mb-3">Product Media</h6>
                    
                    <div class="mb-3">
                        <label class="form-label">Media Type</label>
                        <div class="btn-group w-100 mb-3" role="group">
                            <input type="radio" class="btn-check" name="media_type" id="media_type_image" value="image" {% if product.image and not product.video %}checked{% endif %}>
                            <label class="btn btn-outline-primary" for="media_type_image">Image</label>
                            
                            <input type="radio" class="btn-check" name="media_type" id="media_type_video" value="video" {% if product.video %}checked{% endif %}>
                            <label class="btn btn-outline-primary" for="media_type_video">Video</label>
                        </div>
                        
                        <!-- Image Section -->
                        <div id="image_upload_section" {% if product.video %}style="display: none;"{% endif %}>
                            {% if product.image %}
                            <div class="mb-3">
                                <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-fluid rounded">
                            </div>
                            {% endif %}
                            
                            <label for="image" class="form-label">Upload New Image</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*">
                            <div class="form-text">Recommended size: 800x800px</div>
                        </div>
                        
                        <!-- Video Section -->
                        <div id="video_upload_section" {% if not product.video %}style="display: none;"{% endif %}>
                            {% if product.video %}
                            <div class="mb-3">
                                <video controls class="img-fluid rounded">
                                    <source src="{{ product.video.url }}" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                            </div>
                            {% endif %}
                            
                            <label for="video" class="form-label">Upload New Video</label>
                            <input type="file" class="form-control" id="video" name="video" accept="video/*">
                            <div class="form-text">Recommended format: MP4, max 100MB</div>
                        </div>
                    </div>
                </div>
            </div>

            <hr>

            <!-- Additional Fields -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="status" class="form-label">Product Status</label>
                        <select class="form-select" id="status" name="status">
                            {% for status_value, status_label in status_choices %}
                            <option value="{{ status_value }}" {% if status_value == product.status %}selected{% endif %}>
                                {{ status_label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <div class="form-check form-switch mt-4">
                            <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" {% if product.is_featured %}checked{% endif %}>
                            <label class="form-check-label" for="is_featured">
                                Feature this product on homepage
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-end mt-4">
                <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="btn btn-outline-secondary me-2">Cancel</a>
                <button type="submit" class="btn btn-primary">Save Changes</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block admin_extra_css %}
<style>
    .form-control, .form-select {
        border-radius: 0.375rem;
    }
    
    textarea.form-control {
        min-height: 120px;
    }
    
    .form-switch .form-check-input {
        width: 3em;
    }
    
    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }
</style>
{% endblock %}

{% block admin_extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Media type toggle functionality
    document.getElementById('media_type_image').addEventListener('change', function() {
        if (this.checked) {
            document.getElementById('image_upload_section').style.display = 'block';
            document.getElementById('video_upload_section').style.display = 'none';
        }
    });
    
    document.getElementById('media_type_video').addEventListener('change', function() {
        if (this.checked) {
            document.getElementById('image_upload_section').style.display = 'none';
            document.getElementById('video_upload_section').style.display = 'block';
        }
    });
    
    // Image preview
    const imageInput = document.querySelector('#image');
    const imagePreviewContainer = document.querySelector('#image_upload_section .img-fluid');
    
    if (imageInput && imagePreviewContainer) {
        imageInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    imagePreviewContainer.src = e.target.result;
                }
                reader.readAsDataURL(file);
            }
        });
    }
    
    // Video preview
    const videoInput = document.querySelector('#video');
    const videoPreviewContainer = document.querySelector('#video_upload_section .img-fluid');
    
    if (videoInput && videoPreviewContainer) {
        videoInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    videoPreviewContainer.querySelector('source').src = e.target.result;
                    videoPreviewContainer.load();
                }
                reader.readAsDataURL(file);
            }
        });
    }

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const price = document.querySelector('#price').value;
        const stock = document.querySelector('#stock').value;
        
        if (price < 0) {
            e.preventDefault();
            alert('Price cannot be negative');
        }
        
        if (stock < 0) {
            e.preventDefault();
            alert('Stock cannot be negative');
        }
    });

    // Initialize subtotal calculator
    updateSubtotal();
});

// Function to update subtotal
function updateSubtotal() {
    const price = parseFloat(document.getElementById('price').value) || 0;
    const quantity = parseInt(document.getElementById('calc_quantity').value) || 0;
    const subtotal = price * quantity;
    document.getElementById('calc_subtotal').value = subtotal.toFixed(2);
}

// Function to reset calculator
function resetCalculator() {
    document.getElementById('calc_quantity').value = 1;
    updateSubtotal();
}
</script>
{% endblock %} 