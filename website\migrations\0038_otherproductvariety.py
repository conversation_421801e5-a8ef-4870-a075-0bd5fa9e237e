# Generated by Django 4.2.20 on 2025-06-25 11:04

from django.db import migrations, models
import imagekit.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0037_auto_20250625_1046'),
    ]

    operations = [
        migrations.CreateModel(
            name='OtherProductVariety',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('date_from_day', models.PositiveIntegerField(help_text='Day (1-31)')),
                ('date_from_month', models.CharField(help_text='Month abbreviation (Jan, Feb, etc.)', max_length=3)),
                ('date_to_day', models.PositiveIntegerField(help_text='Day (1-31)')),
                ('date_to_month', models.CharField(help_text='Month abbreviation (Jan, Feb, etc.)', max_length=3)),
                ('weight_from', models.PositiveIntegerField(help_text='Minimum weight')),
                ('weight_to', models.PositiveIntegerField(help_text='Maximum weight')),
                ('weight_unit', models.CharField(choices=[('gm', 'Grams'), ('kg', 'Kilograms')], default='gm', max_length=2)),
                ('image', imagekit.models.fields.ProcessedImageField(blank=True, null=True, upload_to='other_varieties/')),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Other Product Variety',
                'verbose_name_plural': 'Other Product Varieties',
                'ordering': ['order', 'name'],
            },
        ),
    ]
