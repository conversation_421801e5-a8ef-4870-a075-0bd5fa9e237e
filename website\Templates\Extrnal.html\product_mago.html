{% extends "Extrnal.html/EX.base.html" %}
{% load static %}

{% block head %}
    <title>Mango - GB Farms</title>
    <style>
        .mango-hero-bg {
            background-image: url("{% static 'images/prodect mango/bg.jpg' %}");
            background-size: cover;
            background-position: center;
        }
    </style>
{% endblock %}

{% block header %}
    {% include "Extrnal.html/navebar.html" %}
{% endblock %}

{% block content %}

    <!-- Main Section - Mangoes Product Page -->
    <main class="relative h-[80vh] md:h-[90vh] w-full flex items-center justify-start mango-hero-bg">
        <div class="pl-4 pt-20 md:pl-20 lg:pl-40 md:pt-24 max-w-5xl z-10">
            {% include 'Extrnal.html/includes/product_hero_content.html' %}
        </div>
    </main>

    <!-- second section -->
    <section class="relative h-auto md:h-[70vh] w-full bg-white flex flex-col items-center justify-center mb-16 md:mb-32 gap-y-6 md:gap-y-10 py-16 md:py-0">
        <!-- Background pattern -->
        <div
            class="absolute inset-0 bg-[url('https://flowbite.s3.amazonaws.com/docs/jumbotron/hero-pattern.svg')] opacity-10">
        </div>

        <!-- Product cards container -->
        <div class="flex flex-col md:flex-row justify-between gap-4 z-10 w-full px-4 md:px-48">
            <!-- Grapes Card -->
            <a href="{% url 'GB_FARM:external_grapes' %}" class="group">
                <img src="{% static 'images/card1.png' %}" alt="Grapes"
                    class="w-full md:w-96 h-64 md:h-full rounded-xl transition-transform duration-300 group-hover:scale-105 object-cover">
            </a>

            <!-- Mangoes Card -->
            <a href="{% url 'GB_FARM:external_mangoes' %}" class="group">
                <img src="{% static 'images/card3.png' %}" alt="Mangoes"
                    class="w-full md:w-96 h-64 md:h-full rounded-xl transition-transform duration-300 group-hover:scale-105 object-cover">
            </a>

            <!-- Others Card -->
            <a href="{% url 'GB_FARM:external_others' %}" class="group">
                <img src="{% static 'images/mashmah.png' %}" alt="Other Products"
                    class="w-full md:w-96 h-64 md:h-full rounded-xl transition-transform duration-300 group-hover:scale-105 object-cover">
            </a>
        </div>
    </section>

    <!--Third section-->
    <section class="h-auto w-full bg-gray-50 py-16 md:py-24 relative">
        <div class="container mx-auto px-4 md:px-8 relative z-10">
            <!-- Title with decorative line -->
            <div class="relative mb-12 md:mb-20">
                <h2 class="text-yellow-500 text-4xl md:text-8xl font-bold">Mango</h2>
                <!-- Decorative line extending from title -->
                <div class="absolute top-6 md:top-12 left-32 md:left-80 w-1/3 md:w-2/3 h-1 bg-yellow-500"></div>
                <!-- Extended decorative line -->
                <div class="absolute top-6 md:top-12 left-40 md:left-[32rem] w-1/3 md:w-2/3 h-1 bg-yellow-500 opacity-60"></div>
            </div>
            
            <!-- Content layout matching the grapes page -->
            <div class="flex flex-wrap items-start">
                <!-- Left column text -->
                <div class="w-full md:w-1/2 md:pr-12 space-y-6 mb-8 md:mb-0">
                    <p class="text-yellow-500 text-base md:text-lg leading-relaxed">
                        The sweet summer taste of our mango is what makes us so passionate about our orchards.
                        Growing more than 25 varieties since 1991, including the famous Kent and Keitt, Ostein, Naomi, and Maya, our mango field has reached 600 acres yielding more than 4000 tons.
                        Deeming us one of the largest mango farms in the country.
                    </p>
                    
                    <p class="text-yellow-500 text-base md:text-lg leading-relaxed">
                        Our post-harvest treatment enables us to send our mangoes overseas for over 2 weeks with intact fruit on arrival, a goal most mango exporters have failed to achieve by witness of most of our clients.
                    </p>
                </div>
                
                <!-- Right column text -->
                <div class="w-full md:w-1/2 md:pl-12 space-y-6">
                    <p class="text-yellow-500 text-base md:text-lg leading-relaxed">
                        What makes GB Mango stand out in the local and international market
                        is our continuous research and development to excel in the plantation, pruning, harvesting and post-harvesting treatment of our produce to ensure a healthy strong mango in the hand of our customers.
                    </p>
                    
                    <p class="text-yellow-500 text-base md:text-lg leading-relaxed">
                        Mastering sea shipments over the years, we became one of the biggest mango exporters in Egypt.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!--International Varieties Section-->
    <section class="h-auto w-full bg-gray-50 py-16 md:py-20 relative overflow-hidden">
        <!-- Background curved line SVG -->
        <div class="absolute bottom-0 left-0 w-full h-full opacity-10">
            <svg viewBox="0 0 1200 600" class="w-full h-full" preserveAspectRatio="none">
                <!-- Main curved line from bottom left -->
                <path d="M0 500 Q 300 400, 600 350 Q 900 300, 1200 200" 
                      stroke="#dc2626" 
                      stroke-width="3" 
                      fill="none" 
                      stroke-dasharray="15,8"/>
                <!-- Secondary curved line -->
                <path d="M0 520 Q 400 420, 800 380 Q 1000 360, 1200 300" 
                      stroke="#dc2626" 
                      stroke-width="2" 
                      fill="none" 
                      opacity="0.5"/>
            </svg>
        </div>

        <div class="container mx-auto px-4 md:px-8 relative z-10">
            <div class="flex flex-wrap items-start">
                <!-- Left side - Varieties content -->
                <div class="w-full md:w-2/3 md:pr-12 mb-8 md:mb-0">
                    <!-- Title -->
                    <div class="mb-12 md:mb-16">
                        <h2 class="text-red-600 text-3xl md:text-5xl font-bold leading-tight">
                            International<br>
                            <span class="text-red-700">Varieties</span>
                        </h2>
                    </div>

                    <!-- Dynamic International Varieties Grid -->
                    <div class="space-y-6 md:space-y-8">
                        {% if international_varieties %}
                            {% for variety in international_varieties %}
                                {% if forloop.counter0|divisibleby:3 %}
                                    {% if not forloop.first %}</div>{% endif %}
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 {% if not forloop.last %}pb-6 md:pb-8 border-b-2 border-red-600{% endif %}">
                                {% endif %}
                                
                                <div class="text-center {% if not forloop.counter0|divisibleby:3 and not forloop.last %}md:border-l  border-gray-300 md:px-4{% endif %}">
                                    <h3 class="text-red-700 text-xl md:text-2xl font-bold mb-2">{{ variety.name }}</h3>
                                    <p class="text-gray-600 text-sm mb-1">{{ variety.date_range_display }}</p>
                                    <p class="text-gray-600 text-sm">{{ variety.weight_range_display }}</p>
                                </div>
                                
                                {% if forloop.last %}
                                    </div>
                                {% endif %}
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-8">
                                <p class="text-gray-500">No international varieties available.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Right side - Mango image -->
                <div class="w-full md:w-1/3 flex justify-center items-start">
                    <div class="relative">
                        <img src="{% static 'images/prodect mango/123.png' %}" 
                             alt="Mango Varieties" 
                             class="h-auto max-w-full w-64 md:w-80 drop-shadow-2xl">
                        <!-- Optional decorative element -->
                        <div class="absolute -bottom-4 -right-4 w-20 h-20 bg-red-100 rounded-full opacity-20"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Local Varieties Section -->
    <section class="h-auto w-full bg-gray-50 py-16 md:py-20 relative overflow-hidden">
        <!-- Background curved line SVG -->
        <div class="absolute bottom-0 right-0 w-full h-full opacity-10">
            <svg viewBox="0 0 1200 600" class="w-full h-full" preserveAspectRatio="none">
                <!-- Main curved line from bottom right -->
                <path d="M1200 500 Q 900 400, 600 350 Q 300 300, 0 200" 
                      stroke="#ea580c" 
                      stroke-width="3" 
                      fill="none" 
                      stroke-dasharray="15,8"/>
                <!-- Secondary curved line -->
                <path d="M1200 520 Q 800 420, 400 380 Q 200 360, 0 300" 
                      stroke="#ea580c" 
                      stroke-width="2" 
                      fill="none" 
                      opacity="0.5"/>
            </svg>
        </div>

        <div class="container mx-auto px-4 md:px-8 relative z-10">
            <div class="flex flex-wrap items-start">
                <!-- Left side - Mango image -->
                <div class="w-full md:w-1/3 flex justify-center items-start mb-8 md:mb-0 order-2 md:order-1">
                    <div class="relative">
                        <img src="{% static 'images/prodect mango/321.png' %}" 
                             alt="Local Mango Varieties" 
                             class="h-auto max-w-full w-64 md:w-80 drop-shadow-2xl">
                        <!-- Optional decorative element -->
                        <div class="absolute -bottom-4 -left-4 w-20 h-20 bg-orange-100 rounded-full opacity-20"></div>
                    </div>
                </div>

                <!-- Right side - Varieties content -->
                <div class="w-full md:w-2/3 md:pl-12 order-1 md:order-2 mb-8 md:mb-0">
                    <!-- Title -->
                    <div class="mb-12 md:mb-16">
                        <h2 class="text-orange-500 text-3xl md:text-5xl font-bold leading-tight text-center md:text-left">
                            Local<br>
                            <span class="text-orange-600">Varieties</span>
                        </h2>
                    </div>

                    <!-- Dynamic Local Varieties Grid -->
                    <div class="space-y-6 md:space-y-8">
                        {% if local_varieties %}
                            {% for variety in local_varieties %}
                                {% if forloop.counter0|divisibleby:3 %}
                                    {% if not forloop.first %}</div>{% endif %}
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 {% if not forloop.last %}pb-6 md:pb-8 border-b-2 border-orange-500{% endif %}">
                                {% endif %}
                                
                                <div class="text-center {% if not forloop.counter0|divisibleby:3 and not forloop.last %}md:border-l   border-gray-300 md:px-4{% endif %}">
                                    <h3 class="text-orange-600 text-xl md:text-2xl font-bold mb-2">{{ variety.name }}</h3>
                                    <p class="text-gray-600 text-sm mb-1">{{ variety.date_range_display }}</p>
                                    <p class="text-gray-600 text-sm">{{ variety.weight_range_display }}</p>
                                </div>
                                
                                {% if forloop.last %}
                                    </div>
                                {% endif %}
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-8">
                                <p class="text-gray-500">No local varieties available.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block footer %}
    {% include "Extrnal.html/footer.html" %}
{% endblock %}