#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from website.models import NewsletterSubscription, ExternalFooter
from django.test import Client
from django.urls import reverse

def test_newsletter_functionality():
    """Test all aspects of newsletter functionality"""
    print("🧪 Testing Newsletter Functionality...")
    print("=" * 50)
    
    # Test 1: Model functionality
    print("\n1️⃣ Testing NewsletterSubscription Model...")
    try:
        # Create a test subscription
        test_email = "<EMAIL>"
        subscription = NewsletterSubscription.objects.create(
            email=test_email,
            source_page="external_index"
        )
        print(f"✅ Model creation successful: {subscription}")
        
        # Test string representation
        str_repr = str(subscription)
        print(f"✅ String representation: {str_repr}")
        
        # Test model fields
        print(f"✅ Email: {subscription.email}")
        print(f"✅ Source page: {subscription.source_page}")
        print(f"✅ Is active: {subscription.is_active}")
        print(f"✅ Created at: {subscription.created_at}")
        
        # Clean up
        subscription.delete()
        print("✅ Test subscription cleaned up")
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False
    
    # Test 2: URL configuration
    print("\n2️⃣ Testing URL Configuration...")
    try:
        url = reverse('GB_FARM:subscribe_newsletter')
        print(f"✅ Newsletter URL resolved: {url}")
    except Exception as e:
        print(f"❌ URL resolution failed: {e}")
        return False
    
    # Test 3: View functionality
    print("\n3️⃣ Testing View Functionality...")
    try:
        client = Client()
        
        # Test POST request
        response = client.post('/subscribe_newsletter', {
            'email': '<EMAIL>',
            'source_page': 'external_index'
        })
        
        print(f"✅ POST request status: {response.status_code}")
        
        # Check if subscription was created
        subscription = NewsletterSubscription.objects.filter(email='<EMAIL>').first()
        if subscription:
            print(f"✅ Subscription created successfully: {subscription.email}")
            subscription.delete()  # Clean up
        else:
            print("❌ Subscription was not created")
            
    except Exception as e:
        print(f"❌ View test failed: {e}")
        return False
    
    # Test 4: Footer data
    print("\n4️⃣ Testing Footer Data...")
    try:
        footer = ExternalFooter.objects.first()
        if footer:
            print(f"✅ Footer data exists:")
            print(f"   📞 Phone: {footer.phone}")
            print(f"   📧 Email: {footer.email}")
            print(f"   📍 Address: {footer.address}")
        else:
            print("⚠️ No footer data found - you may need to add it in admin")
            
    except Exception as e:
        print(f"❌ Footer test failed: {e}")
    
    # Test 5: Admin functionality
    print("\n5️⃣ Testing Admin Configuration...")
    try:
        from django.contrib import admin
        from website.admin import NewsletterSubscriptionAdmin
        
        # Check if admin is registered
        if NewsletterSubscription in admin.site._registry:
            print("✅ NewsletterSubscription is registered in admin")
            admin_class = admin.site._registry[NewsletterSubscription]
            print(f"✅ Admin class: {admin_class.__class__.__name__}")
        else:
            print("❌ NewsletterSubscription is not registered in admin")
            
    except Exception as e:
        print(f"❌ Admin test failed: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Newsletter functionality test completed!")
    return True

if __name__ == "__main__":
    test_newsletter_functionality()
