# Generated by Django 4.2.20 on 2025-06-01 17:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0011_userprofile_profile_picture'),
    ]

    operations = [
        migrations.CreateModel(
            name='Career',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Title')),
                ('department', models.Char<PERSON>ield(max_length=100)),
                ('department_ar', models.Char<PERSON>ield(blank=True, max_length=100, null=True, verbose_name='Arabic Department')),
                ('location', models.Char<PERSON>ield(max_length=100)),
                ('location_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Arabic Location')),
                ('job_type', models.Char<PERSON>ield(choices=[('full_time', 'Full Time'), ('part_time', 'Part Time'), ('contract', 'Contract'), ('internship', 'Internship')], max_length=20)),
                ('experience_level', models.CharField(choices=[('entry', 'Entry Level'), ('mid', 'Mid Level'), ('senior', 'Senior Level'), ('lead', 'Lead Level'), ('manager', 'Manager Level')], max_length=20)),
                ('description', models.TextField()),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('requirements', models.TextField()),
                ('requirements_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Requirements')),
                ('responsibilities', models.TextField()),
                ('responsibilities_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Responsibilities')),
                ('benefits', models.TextField(blank=True, null=True)),
                ('benefits_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Benefits')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Career',
                'verbose_name_plural': 'Careers',
                'ordering': ['-created_at'],
            },
        ),
    ]
