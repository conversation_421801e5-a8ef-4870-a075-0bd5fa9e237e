{% extends 'GB_FARM/admin_base.html' %}

{% block title %}Edit Zone{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Admin Dashboard</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:admin_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i> Orders
                    </a>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-box me-2"></i> Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i> Customers
                    </a>
                    <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h4 class="mb-0">Edit Zone</h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Zone Name *</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.name.errors }}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="price" class="form-label">Delivery Price (EGP) *</label>
                            {{ form.price }}
                            {% if form.price.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.price.errors }}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="is_active">
                                    Active Zone
                                </label>
                            </div>
                            {% if form.is_active.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.is_active.errors }}
                            </div>
                            {% endif %}
                        </div>

                        {% if form.errors %}
                        <div class="alert alert-danger">
                            Please correct the errors below.
                            {{ form.non_field_errors }}
                        </div>
                        {% endif %}
                        
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'GB_FARM:admin_zone_list' %}" class="btn btn-outline-secondary me-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 