{% extends 'GB_FARM/admin_base.html' %}
{% load static %}

{% block title %}Mark Submission as Processed | Admin Dashboard{% endblock %}

{% block admin_content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h6>Mark Submission as Processed</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h4 class="alert-heading">Confirm Processing</h4>
                        <p>Are you sure you want to mark this submission as processed? This action indicates that you have reviewed the submission and taken appropriate action.</p>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Submission Details</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Name:</strong> {{ submission.full_name }}</p>
                                    <p><strong>Email:</strong> {{ submission.email }}</p>
                                    <p><strong>Date:</strong> {{ submission.created_at }}</p>
                                    <p><strong>Source Page:</strong> {{ submission.get_source_page_display }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Message</h6>
                                </div>
                                <div class="card-body">
                                    <p>{{ submission.message|linebreaks }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 text-center">
                            <form method="post">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-success">Mark as Processed</button>
                                <a href="{% url 'GB_FARM:admin_submissions_dashboard' %}" class="btn btn-secondary ms-2">Cancel</a>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 