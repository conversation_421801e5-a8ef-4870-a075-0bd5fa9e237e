from django.core.cache import cache
from django.db.models import Prefetch, Count, Avg, Sum, F, Q
from django.db.models.functions import TruncDate
from django.utils import timezone
from .models import Product, Order, Review, Category
import logging

logger = logging.getLogger(__name__)

def get_product_list():
    """Get optimized product list with caching"""
    cache_key = 'product_list'
    products = cache.get(cache_key)

    if products is None:
        try:
            products = Product.objects.select_related('category').annotate(
                avg_rating=Avg('reviews__rating'),
                review_count=Count('reviews')
            ).filter(status='active').order_by('-created_at')

            # Cache for 15 minutes
            cache.set(cache_key, products, 900)
        except Exception as e:
            logger.error(f"Error fetching product list: {str(e)}")
            products = []

    return products

def get_product_detail(product_id):
    """Get optimized product detail with caching"""
    cache_key = f'product_detail_{product_id}'
    product = cache.get(cache_key)

    if product is None:
        try:
            product = Product.objects.select_related('category').prefetch_related(
                'reviews', 'reviews__user'
            ).annotate(
                avg_rating=Avg('reviews__rating'),
                review_count=Count('reviews')
            ).get(id=product_id)

            # Cache for 30 minutes
            cache.set(cache_key, product, 1800)
        except Product.DoesNotExist:
            logger.error(f"Product not found: {product_id}")
            raise
        except Exception as e:
            logger.error(f"Error fetching product detail: {str(e)}")
            raise

    return product

def get_category_products(category_id):
    """Get optimized category products with caching"""
    cache_key = f'category_products_{category_id}'
    products = cache.get(cache_key)

    if products is None:
        try:
            products = Product.objects.select_related('category').annotate(
                avg_rating=Avg('reviews__rating'),
                review_count=Count('reviews')
            ).filter(
                category_id=category_id,
                status='active'
            ).order_by('-created_at')

            # Cache for 1 hour
            cache.set(cache_key, products, 3600)
        except Exception as e:
            logger.error(f"Error fetching category products: {str(e)}")
            products = []

    return products

def get_featured_products():
    """Get optimized featured products with caching"""
    cache_key = 'featured_products'
    products = cache.get(cache_key)

    if products is None:
        try:
            products = Product.objects.select_related('category').annotate(
                avg_rating=Avg('reviews__rating'),
                review_count=Count('reviews')
            ).filter(
                is_featured=True,
                status='active'
            ).order_by('-created_at')[:8]

            # Cache for 1 hour
            cache.set(cache_key, products, 3600)
        except Exception as e:
            logger.error(f"Error fetching featured products: {str(e)}")
            products = []

    return products

def get_category_list():
    """Get optimized category list with caching"""
    cache_key = 'category_list'
    categories = cache.get(cache_key)

    if categories is None:
        try:
            categories = Category.objects.annotate(
                product_count=Count('products', filter=Q(products__status='active'))
            ).order_by('name')

            # Cache for 1 hour
            cache.set(cache_key, categories, 3600)
        except Exception as e:
            logger.error(f"Error fetching category list: {str(e)}")
            categories = []

    return categories

def get_daily_sales():
    """Get daily sales data without caching"""
    try:
        today = timezone.now().date()
        return Order.objects.filter(
            created_at__date=today,
            status='completed'
        ).aggregate(
            total_sales=Sum('total_amount'),
            order_count=Count('id')
        )
    except Exception as e:
        logger.error(f"Error fetching daily sales: {str(e)}")
        return {'total_sales': 0, 'order_count': 0}

def clear_product_cache(product_id=None):
    """Clear product-related cache"""
    try:
        if product_id:
            # Clear specific product cache
            cache.delete(f'product_detail_{product_id}')
        else:
            # Clear all product-related cache
            cache.delete('product_list')
            cache.delete('featured_products')
            cache.delete('category_list')
    except Exception as e:
        logger.error(f"Error clearing product cache: {str(e)}")