#app
SQL_HOST=
SQL_PORT=

#db
POSTGRES_DB=postgres
POSTGRES_PASSWORD=
POSTGRES_USER=
#nginx

#settings
ENV=TEST #( LOCAL, TEST, PRODUCTION )
DEBUG=True
SECRET_KEY=
SQL_ENGINE=django.db.backends.postgresql
SQL_DATABASE=postgres
SQL_USER=
SQL_PASSWORD=
EMAIL_HOST=
EMAIL_PORT=
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=
SENTRY_DSN=

#constants

EBS_USER=
EBS_PASSWORD=
VODAFONE_ACCOUNT_ID=
VODAFONE_PASSWORD=
RN_USER=
RN_PASSWORD=
CX_USER=
CX_PASSWORD=
PAYMOB_API_KEY=
PAYMOB_HMAC_KEY=