# Generated by Django 4.2.20 on 2025-06-18 15:11

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0022_newslettersubscription'),
    ]

    operations = [
        migrations.CreateModel(
            name='CalendarSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_year', models.PositiveIntegerField(default=2024)),
                ('show_legend', models.BooleanField(default=True)),
                ('enable_filtering', models.BooleanField(default=True)),
                ('default_view', models.CharField(choices=[('all', 'All Products'), ('grapes', 'Grapes'), ('mangoes', 'Mangoes'), ('others', 'Others')], default='all', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Calendar Settings',
                'verbose_name_plural': 'Calendar Settings',
            },
        ),
        migrations.CreateModel(
            name='ProductType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('name_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Arabic Name')),
                ('slug', models.SlugField(unique=True)),
                ('color', models.CharField(default='#22c55e', help_text='Hex color code for calendar display', max_length=7)),
                ('description', models.TextField(blank=True, null=True)),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('is_active', models.BooleanField(default=True)),
                ('order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Product Type',
                'verbose_name_plural': 'Product Types',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.AlterField(
            model_name='externalcontactsubmission',
            name='source_page',
            field=models.CharField(choices=[('external_index', 'Home Page'), ('external_about', 'About Page'), ('external_mangoes', 'Mangoes Page'), ('external_grapes', 'Grapes Page'), ('external_others', 'Others Page'), ('external_contact', 'Contact Page')], default='external_index', max_length=30),
        ),
        migrations.AlterField(
            model_name='newslettersubscription',
            name='source_page',
            field=models.CharField(choices=[('external_index', 'Home Page'), ('external_about', 'About Page'), ('external_mangoes', 'Mangoes Page'), ('external_grapes', 'Grapes Page'), ('external_others', 'Others Page'), ('external_contact', 'Contact Page')], default='external_index', max_length=30),
        ),
        migrations.CreateModel(
            name='ProductAvailability',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.PositiveIntegerField()),
                ('month', models.PositiveIntegerField(choices=[(1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'), (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'), (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')])),
                ('status', models.CharField(choices=[('available', 'Available'), ('limited', 'Limited Availability'), ('none', 'Not Available'), ('peak', 'Peak Season')], max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Additional notes about availability', null=True)),
                ('notes_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availabilities', to='website.producttype')),
            ],
            options={
                'verbose_name': 'Product Availability',
                'verbose_name_plural': 'Product Availabilities',
                'ordering': ['year', 'month', 'product_type'],
                'indexes': [models.Index(fields=['year', 'month'], name='website_pro_year_fbadfc_idx'), models.Index(fields=['product_type', 'year'], name='website_pro_product_c8d61a_idx')],
                'unique_together': {('product_type', 'year', 'month')},
            },
        ),
    ]
