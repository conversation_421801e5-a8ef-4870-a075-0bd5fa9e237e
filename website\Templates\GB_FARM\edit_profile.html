{% extends 'GB_FARM/base.html' %}

{% block title %}Edit Profile{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Account Menu</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:profile' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-person-circle me-2"></i> My Profile
                    </a>
                    <a href="{% url 'GB_FARM:edit_profile' %}" class="list-group-item list-group-item-action active">
                        <i class="bi bi-pencil-square me-2"></i> Edit Profile
                    </a>
                    <a href="{% url 'GB_FARM:change_password' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-key me-2"></i> Change Password
                    </a>
                    <a href="{% url 'GB_FARM:order_history' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-bag me-2"></i> Order History
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Edit Profile</h5>
                </div>
                <div class="card-body">
                    {% if messages %}
                    <div class="messages mb-4">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            {{ message }}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <form method="post" class="needs-validation" enctype="multipart/form-data" novalidate>
                        {% csrf_token %}
                        
                        <div class="row mb-4">
                            <div class="col-md-12 mb-3 text-center">
                                <div class="profile-img-container mb-3">
                                    <div class="position-relative d-inline-block">
                                        {% if profile.profile_picture %}
                                        <img src="{{ profile.profile_picture.url }}" alt="Profile Picture" id="profile-preview" class="img-thumbnail rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                                        {% else %}
                                        <div id="profile-preview" class="profile-img-placeholder bg-light d-flex align-items-center justify-content-center" style="width: 150px; height: 150px; border-radius: 50%; margin: 0 auto;">
                                            <i class="bi bi-person-fill" style="font-size: 5rem; color: #ccc;"></i>
                                        </div>
                                        {% endif %}
                                        <div class="upload-overlay position-absolute bottom-0 start-50 translate-middle-x mb-2" style="width: 100%;">
                                            <label for="profile_picture" class="btn btn-sm btn-light shadow-sm">
                                                <i class="bi bi-camera"></i> Change Photo
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <input class="form-control d-none" type="file" id="profile_picture" name="profile_picture" accept="image/*">
                                    <div class="form-text text-muted">Supported formats: JPG, PNG, GIF (Max size: 10MB)</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" value="{{ user.username }}" disabled readonly>
                                <div class="form-text text-muted">Username cannot be changed</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-secondary">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}" required>
                                <div class="invalid-feedback">
                                    Please provide a valid email address.
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" value="{{ user.first_name }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" value="{{ user.last_name }}">
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="{{ user.user_phone }}">
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <h5 class="mb-3">Default Building Office Address</h5>
                        <div class="row mb-3">
                            <div class="col-md-12 mb-3">
                                <label for="shipping_address" class="form-label">office </label>
                                <textarea class="form-control" id="shipping_address" name="shipping_address" rows="3">{{ shipping_address }}</textarea>
                                <div class="form-text text-muted">This address will be pre-filled during checkout</div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'GB_FARM:profile' %}" class="btn btn-outline-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-img-container {
    position: relative;
    display: inline-block;
}

.upload-overlay {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.profile-img-container:hover .upload-overlay {
    opacity: 1;
}

.profile-img-placeholder {
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
}
</style>

<script>
// Form validation script
(function () {
    'use strict'
    
    // Fetch all the forms we want to apply custom Bootstrap validation styles to
    var forms = document.querySelectorAll('.needs-validation')
    
    // Loop over them and prevent submission
    Array.prototype.slice.call(forms)
        .forEach(function (form) {
            form.addEventListener('submit', function (event) {
                if (!form.checkValidity()) {
                    event.preventDefault()
                    event.stopPropagation()
                }
                
                form.classList.add('was-validated')
            }, false)
        })
})()

// Image preview functionality
document.getElementById('profile_picture').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Validate file size (5MB max)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB');
            this.value = '';
            return;
        }
        
        // Validate file type
        const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!validTypes.includes(file.type)) {
            alert('Please select a valid image file (JPG, PNG, or GIF)');
            this.value = '';
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('profile-preview');
            if (preview.tagName === 'IMG') {
                preview.src = e.target.result;
            } else {
                // Replace div with img
                const img = document.createElement('img');
                img.src = e.target.result;
                img.id = 'profile-preview';
                img.className = 'img-thumbnail rounded-circle';
                img.style.width = '150px';
                img.style.height = '150px';
                img.style.objectFit = 'cover';
                preview.parentNode.replaceChild(img, preview);
            }
        }
        reader.readAsDataURL(file);
    }
});

// Trigger file input when clicking the change photo button
document.querySelector('.upload-overlay label').addEventListener('click', function(e) {
    e.preventDefault();
    document.getElementById('profile_picture').click();
});
</script>
{% endblock %} 