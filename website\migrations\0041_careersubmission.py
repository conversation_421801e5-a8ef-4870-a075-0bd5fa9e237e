# Generated by Django 4.2.20 on 2025-06-25 15:05

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0040_remove_image_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='CareerSubmission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('subject', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('cv', models.FileField(blank=True, null=True, upload_to='career_cvs/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'doc', 'docx'])])),
                ('submitted_at', models.DateT<PERSON><PERSON><PERSON>(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Career Submission',
                'verbose_name_plural': 'Career Submissions',
                'ordering': ['-submitted_at'],
            },
        ),
    ]
