{% extends "Extrnal.html/EX.base.html" %}
{% load static %}

{% block head %}
<title>All Products - GB Farms</title>
<style>
    .others-hero-bg {
        background-image: url("{% static 'images/prodect mango/close-up-orange-tree-with-many-leaves.jpg' %}");
        background-size: cover;
        background-position: center;
    }
</style>
{% endblock %}

{% block header %}
{% include "Extrnal.html/navebar.html" %}
{% endblock %}

{% block content %}
<main class="relative h-[80vh] md:h-[90vh] w-full flex items-center justify-start others-hero-bg">
    <div class="pl-4 pt-32 md:pl-20 lg:pl-40 md:pt-24 max-w-5xl z-10">
        {% include 'Extrnal.html/includes/product_hero_content.html' %}
    </div>
</main>

<!-- Product cards container -->

<!-- Main Title Section -->
<section class="h-auto w-full bg-gray-50 py-16 md:py-20 relative overflow-hidden">
    <!-- Background curved line SVG -->
    <div class="absolute top-0 right-0 w-full h-full opacity-10">
        <svg viewBox="0 0 1200 600" class="w-full h-full" preserveAspectRatio="none">
            <!-- Curved line matching the design -->
            <path d="M400 100 Q 800 50, 1100 200 Q 1150 250, 1200 350"
                  stroke="#ef4444"
                  stroke-width="3"
                  fill="none"
                  stroke-dasharray="10,5"/>
            <!-- Additional curved element -->
            <path d="M300 400 Q 600 350, 900 450 Q 1000 480, 1100 500"
                  stroke="#ef4444"
                  stroke-width="2"
                  fill="none"
                  opacity="0.6"/>
        </svg>
    </div>

    <div class="container mx-auto px-4 md:px-8 relative z-10">
        <!-- Main Title with underline - Mangoes Style -->
        <div class="relative mb-8">
            <h1 class="text-red-500 text-4xl md:text-6xl font-bold leading-tight">All Products</h1>
            <div class="w-full h-1 bg-red-500 mt-2"></div>
        </div>

        <!-- Main Description - Two Column Layout -->
        <div class="flex flex-wrap items-start mb-8">
            <!-- Left column -->
            <div class="w-full md:w-1/2 md:pr-12 space-y-6 mb-8 md:mb-0">
                <p class="text-gray-700 text-base md:text-lg leading-relaxed font-light">
                   At GB Farms, Quality Is Not Just A Standard – It Is A Promise We Uphold In Every Step.
                    Our Journey Begins With Selecting Only The Finest Seeds And Applying Precise Agricultural Practices That Rely On The
                     Latest Techniques To Ensure Healthy And Natural Growth For Every Crop. 
                    AEvery Detail Matters; From Carefully Managed Irrigation And Organic Fertilization To Handpicking Fruits At The Perfect Stage Of Ripeness,
                     Ensuring That Our Products Arrive Fresh And Pure – As If They Were Just Harvested
                </p>
            </div>

            <!-- Right column -->
            <div class="w-full md:w-1/2 md:pl-12 space-y-6">
                <p class="text-gray-700 text-base md:text-lg leading-relaxed font-light">
                    We Believe That Quality Is The Foundation Of Trust, Which Is Why Our Products Undergo Strict Testing And Rigorous Quality Control Standards.
                    Every Fruit We Deliver Is A Symbol Of Natural Taste And High Nutritional Value.
                    At GB Farms, We Don’t Just Deliver Products; We Deliver A True Experience Of Freshness And Excellence You Can Always Rely On.
                                        
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Categories and Products Sections -->
{% for group in groups %}
<section class="h-auto w-full py-16 md:py-20 relative overflow-hidden {% cycle 'bg-white' 'bg-gray-50' %}">
    <!-- Background curved line SVG -->
    <div class="absolute top-0 right-0 w-full h-full opacity-5">
        <svg viewBox="0 0 1200 600" class="w-full h-full" preserveAspectRatio="none">
            <!-- Curved line matching the design -->
            <path d="M400 100 Q 800 50, 1100 200 Q 1150 250, 1200 350"
                  stroke="#ef4444"
                  stroke-width="3"
                  fill="none"
                  stroke-dasharray="10,5"/>
            <!-- Additional curved element -->
            <path d="M300 400 Q 600 350, 900 450 Q 1000 480, 1100 500"
                  stroke="#ef4444"
                  stroke-width="2"
                  fill="none"
                  opacity="0.6"/>
        </svg>
    </div>

    <div class="container mx-auto px-4 md:px-8 relative z-10">

        <!-- Category Title with underline - Mangoes Style -->
        {% if group.title %}
        <div class="relative mb-8">
            <h2 class="text-4xl md:text-6xl font-bold leading-tight" style="color: {{ group.title_color|default:'#ef4444' }}">{{ group.title }}</h2>
            <div class="w-full h-1 mt-2" style="background-color: {{ group.underline_color|default:'#ef4444' }}"></div>
        </div>
        {% endif %}

        <!-- Group Descriptions - Two Column Layout like Mangoes -->
        {% if group.descriptions.all %}
        <div class="flex flex-wrap items-start mb-16">
            <!-- Left column -->
            <div class="w-full md:w-1/2 md:pr-12 space-y-6 mb-8 md:mb-0">
                {% for desc in group.descriptions.all|slice:":2" %}
                <p class="text-base md:text-lg leading-relaxed font-light" style="color: {{ desc.color|default:'#374151' }}">{{ desc.text }}</p>
                {% endfor %}
            </div>

            <!-- Right column -->
            <div class="w-full md:w-1/2 md:pl-12 space-y-6">
                {% for desc in group.descriptions.all|slice:"2:" %}
                <p class="text-base md:text-lg leading-relaxed font-light" style="color: {{ desc.color|default:'#374151' }}">{{ desc.text }}</p>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Products and Image Layout -->
        <div class="flex flex-wrap items-start">
            <!-- Left side - Products Table -->
            <div class="w-full md:w-2/3 md:pr-12 mb-8 md:mb-0">
                <!-- Products Title -->
                {% if group.table_title or group.title %}
                <div class="mb-8">
                    {% with first_product=group.products.all.0 %}
                    <h3 class="text-3xl md:text-4xl font-bold" style="color: {% if group.table_title_color and group.table_title_color != '#ef4444' %}{{ group.table_title_color }}{% elif first_product.color %}{{ first_product.color }}{% else %}#f472b6{% endif %}">
                        {{ group.table_title|default:group.title }}
                    </h3>
                    {% endwith %}
                </div>
                {% endif %}

                <!-- Products Table Style like Reference -->
                {% if group.products.all %}
                <div class="space-y-6">

                    <!-- First row of products -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-12 pb-8 mb-8" style="border-bottom: 1px solid {{ group.underline_color|default:'#ef4444' }}">
                        {% for product in group.products.all|slice:":3" %}
                        <div class="text-center">
                            <h4 class="font-bold text-xl mb-3" style="color: {{ product.color|default:'#ef4444' }}">
                                {{ product.name }}
                            </h4>
                            <p class="text-gray-600 text-sm mb-1">
                                {{ product.start_date|date:"j M" }} - {{ product.end_date|date:"j M" }}
                            </p>
                            <p class="text-gray-600 text-sm">
                                {{ product.weight_from }} - {{ product.weight_to }} gm
                            </p>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Second row of products -->
                    {% if group.products.all|length > 3 %}
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-12 pb-8 mb-8" style="border-bottom: 1px solid {{ group.underline_color|default:'#ef4444' }}">
                        {% for product in group.products.all|slice:"3:6" %}
                        <div class="text-center">
                            <h4 class="font-bold text-xl mb-3" style="color: {{ product.color|default:'#ef4444' }}">
                                {{ product.name }}
                            </h4>
                            <p class="text-gray-600 text-sm mb-1">
                                {{ product.start_date|date:"j M" }} - {{ product.end_date|date:"j M" }}
                            </p>
                            <p class="text-gray-600 text-sm">
                                {{ product.weight_from }} - {{ product.weight_to }} gm
                            </p>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- Third row of products -->
                    {% if group.products.all|length > 6 %}
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-12 pb-8 mb-8" style="border-bottom: 1px solid {{ group.underline_color|default:'#ef4444' }}">
                        {% for product in group.products.all|slice:"6:9" %}
                        <div class="text-center">
                            <h4 class="font-bold text-xl mb-3" style="color: {{ product.color|default:'#ef4444' }}">
                                {{ product.name }}
                            </h4>
                            <p class="text-gray-600 text-sm mb-1">
                                {{ product.start_date|date:"j M" }} - {{ product.end_date|date:"j M" }}
                            </p>
                            <p class="text-gray-600 text-sm">
                                {{ product.weight_from }} - {{ product.weight_to }} gm
                            </p>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <!-- Additional products if more than 9 -->
                    {% if group.products.all|length > 9 %}
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
                        {% for product in group.products.all|slice:"9:" %}
                        <div class="text-center">
                            <h4 class="font-bold text-xl mb-3" style="color: {{ product.color|default:'#ef4444' }}">
                                {{ product.name }}
                            </h4>
                            <p class="text-gray-600 text-sm mb-1">
                                {{ product.start_date|date:"j M" }} - {{ product.end_date|date:"j M" }}
                            </p>
                            <p class="text-gray-600 text-sm">
                                {{ product.weight_from }} - {{ product.weight_to }} gm
                            </p>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                {% else %}
                <div class="text-center py-12 bg-gray-100 rounded-lg">
                    <p class="text-red-500 text-lg">No products available{% if group.title %} in {{ group.title }}{% endif %}.</p>
                </div>
                {% endif %}
            </div>

            <!-- Right side - Category Image -->
            <div class="w-full md:w-1/3 flex justify-center items-center md:pl-12">
                <div class="relative">
                    {% if group.main_image %}
                        <img src="{{ group.main_image.url }}" alt="{% if group.title %}{{ group.title }}{% else %}Product Image{% endif %}"
                            class="h-auto max-w-full w-72 md:w-96 drop-shadow-2xl object-cover">
                    {% else %}
                        <!-- Default fruit image placeholder -->
                        <div class="h-72 w-72 md:w-96 md:h-96 bg-gradient-to-br from-orange-200 to-red-200 rounded-full flex items-center justify-center shadow-2xl">
                            <div class="text-center">
                                <div class="text-6xl mb-4">🍎</div>
                                {% if group.title %}
                                <p class="text-red-500 text-lg font-bold">{{ group.title }}</p>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>
{% empty %}
<section class="h-auto w-full py-16 md:py-20 bg-gray-50">
    <div class="container mx-auto px-4 md:px-8 text-center">
        <div class="bg-white rounded-lg shadow-lg p-12">
            <h3 class="text-red-500 text-2xl font-bold mb-4">No Categories Available</h3>
            <p class="text-gray-600">Please add some product categories in the admin panel.</p>
        </div>
    </div>
</section>
{% endfor %}
{% endblock content %}

{% block footer %}
{% include "Extrnal.html/footer.html" %}
{% endblock footer %}
