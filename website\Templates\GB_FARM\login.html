{% extends 'GB_FARM/base.html' %}
{% load static %}

{% block extra_css %}
<style>
    /* Custom Login Form Styling */
    .login-card {
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.9);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
        border: none;
    }
    
    .login-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(46, 204, 113, 0.2);
    }
    
    .login-heading {
        color: var(--primary-color);
        font-weight: 700;
        letter-spacing: 0.5px;
        position: relative;
        padding-bottom: 10px;
        margin-bottom: 30px;
    }
    
    .login-heading:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
        border-radius: 3px;
    }
    
    .form-control {
        border-radius: 30px;
        padding: 12px 20px;
        border: 2px solid #eaeaea;
        transition: all 0.3s ease;
        background-color: rgba(255, 255, 255, 0.9);
    }
    
    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.25rem rgba(46, 204, 113, 0.25);
        background-color: #fff;
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--text-color);
        padding-left: 10px;
    }
    
    .login-btn {
        padding: 12px 25px;
        font-weight: 600;
        letter-spacing: 0.5px;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        border: none;
        box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
        transition: all 0.3s ease;
    }
    
    .login-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(46, 204, 113, 0.4);
    }
    
    .login-link {
        color: var(--primary-color);
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
    }
    
    .login-link:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }
    
    .form-container {
        animation: fadeIn 0.6s ease;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .invalid-feedback {
        color: #dc3545;
        font-size: 0.85rem;
        margin-top: 5px;
        padding-left: 15px;
    }
    
    /* Field animation effect */
    .form-group {
        position: relative;
        margin-bottom: 1.5rem;
    }
    
    /* Password field icon */
    .password-toggle {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #6c757d;
        transition: color 0.3s ease;
    }
    
    .password-toggle:hover {
        color: var(--primary-color);
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card login-card shadow">
                <div class="card-body p-5">
                    <h2 class="login-heading text-center mb-4">Login</h2>
                    
                    {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} rounded-pill">
                            {{ message }}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <div class="form-container">
                        <form method="POST" class="needs-validation" novalidate>
                            {% csrf_token %}
                            <div class="form-group mb-4">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                                <div class="invalid-feedback">
                                    Please enter your username.
                                </div>
                            </div>
                            
                            <div class="form-group mb-4 position-relative">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <i class="password-toggle fa fa-eye-slash" id="togglePassword"></i>
                                <div class="invalid-feedback">
                                    Please enter your password.
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 mt-4">
                                <button type="submit" class="btn btn-primary login-btn rounded-pill">Login</button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="text-center mt-4">
                        <p>Don't have an account? <a href="{% url 'GB_FARM:registerpage' %}" class="login-link">Register here</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()

// Password toggle visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordInput = document.getElementById('password');
    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordInput.setAttribute('type', type);
    this.classList.toggle('fa-eye');
    this.classList.toggle('fa-eye-slash');
});
</script>
{% endblock %} 