/* Color Picker Widget Styles */
.color-widget {
    display: flex !important;
    gap: 15px !important;
    align-items: flex-start !important;
    padding: 10px !important;
    border: 1px solid #ddd !important;
    border-radius: 5px !important;
    background: #f9f9f9 !important;
    margin: 5px 0 !important;
}

.color-widget-section {
    display: flex !important;
    flex-direction: column !important;
    gap: 5px !important;
}

.color-widget-label {
    font-weight: bold !important;
    font-size: 12px !important;
    color: #333 !important;
}

.color-widget-picker {
    width: 50px !important;
    height: 35px !important;
    border: none !important;
    border-radius: 3px !important;
    cursor: pointer !important;
}

.color-widget-text {
    padding: 8px !important;
    border: 1px solid #ccc !important;
    border-radius: 3px !important;
    font-family: monospace !important;
    width: 120px !important;
}

.color-widget-preview {
    width: 35px !important;
    height: 35px !important;
    border: 2px solid #333 !important;
    border-radius: 3px !important;
}

.color-widget-presets {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 2px !important;
    width: 120px !important;
}

.color-widget-preset-btn {
    width: 25px !important;
    height: 25px !important;
    border: 1px solid #333 !important;
    border-radius: 3px !important;
    cursor: pointer !important;
    margin: 2px !important;
    transition: all 0.2s ease !important;
}

.color-widget-preset-btn:hover {
    border: 2px solid #000 !important;
    transform: scale(1.1) !important;
}
