#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.test import Client
from django.middleware.csrf import get_token
from django.http import HttpRequest

def test_csrf_functionality():
    """Test CSRF token functionality"""
    print("🧪 Testing CSRF Token Functionality...")
    print("=" * 50)
    
    # Create a test client
    client = Client(enforce_csrf_checks=True)
    
    # Test 1: Get the home page and extract CSRF token
    print("\n1️⃣ Testing CSRF Token Generation...")
    try:
        response = client.get('/')
        print(f"✅ Home page loads successfully (Status: {response.status_code})")
        
        # Check if CSRF token is in the response
        content = response.content.decode('utf-8')
        if 'csrfmiddlewaretoken' in content:
            print("✅ CSRF token found in page content")
        else:
            print("❌ CSRF token NOT found in page content")
            
        # Extract CSRF token from cookies
        csrf_token = None
        if 'csrftoken' in response.cookies:
            csrf_token = response.cookies['csrftoken'].value
            print(f"✅ CSRF token from cookies: {csrf_token[:20]}...")
        else:
            print("❌ CSRF token NOT found in cookies")
            
    except Exception as e:
        print(f"❌ Error getting home page: {e}")
        return False
    
    # Test 2: Test form submission with proper CSRF token
    print("\n2️⃣ Testing Form Submission with CSRF...")
    try:
        # Get CSRF token first
        response = client.get('/')
        csrf_token = response.cookies.get('csrftoken')
        
        if csrf_token:
            # Submit form with CSRF token
            response = client.post('/subscribe_newsletter', {
                'email': '<EMAIL>',
                'source_page': 'external_index',
                'csrfmiddlewaretoken': csrf_token.value
            })
            
            print(f"✅ Form submission status: {response.status_code}")
            
            if response.status_code == 302:
                print("✅ Form submitted successfully (redirected)")
            elif response.status_code == 403:
                print("❌ CSRF verification failed")
            else:
                print(f"⚠️ Unexpected status code: {response.status_code}")
                
        else:
            print("❌ Could not get CSRF token for form submission")
            
    except Exception as e:
        print(f"❌ Error testing form submission: {e}")
    
    # Test 3: Test without CSRF token (should fail)
    print("\n3️⃣ Testing Form Submission WITHOUT CSRF (should fail)...")
    try:
        response = client.post('/subscribe_newsletter', {
            'email': '<EMAIL>',
            'source_page': 'external_index'
        })
        
        print(f"✅ Form submission status: {response.status_code}")
        
        if response.status_code == 403:
            print("✅ CSRF protection working correctly (403 Forbidden)")
        else:
            print(f"⚠️ Unexpected behavior - should be 403, got {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing form without CSRF: {e}")
    
    print("\n" + "=" * 50)
    print("✅ CSRF testing completed!")

if __name__ == "__main__":
    test_csrf_functionality()
