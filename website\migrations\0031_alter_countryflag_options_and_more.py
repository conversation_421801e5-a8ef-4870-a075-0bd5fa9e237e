# Generated by Django 4.2.20 on 2025-06-23 15:10

from django.db import migrations, models
import imagekit.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0030_countryflag'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='countryflag',
            options={'ordering': ['continent', 'country_name'], 'verbose_name': 'Country Flag', 'verbose_name_plural': 'Country Flags'},
        ),
        migrations.AlterUniqueTogether(
            name='countryflag',
            unique_together=set(),
        ),
        migrations.AlterField(
            model_name='countryflag',
            name='continent',
            field=models.CharField(choices=[('europe', 'Europe'), ('africa', 'Africa'), ('gulf', 'Gulf'), ('asia', 'Asia')], max_length=20),
        ),
        migrations.AlterField(
            model_name='countryflag',
            name='flag_image',
            field=imagekit.models.fields.ProcessedImageField(blank=True, null=True, upload_to='flags/'),
        ),
        migrations.RemoveField(
            model_name='countryflag',
            name='country_name_ar',
        ),
        migrations.RemoveField(
            model_name='countryflag',
            name='is_active',
        ),
        migrations.RemoveField(
            model_name='countryflag',
            name='order',
        ),
    ]
