#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.test import Client, override_settings

@override_settings(DEBUG=False, ALLOWED_HOSTS=['testserver', '127.0.0.1', 'localhost'])
def test_real_404_in_production():
    """Test real 404 error with DEBUG=False"""
    print("🧪 Testing Real 404 Error in Production Mode (DEBUG=False)...")
    print("=" * 60)
    
    client = Client()
    
    # Test real 404 error
    response = client.get('/this-page-does-not-exist/')
    print(f"✅ Status Code: {response.status_code}")
    
    content = response.content.decode('utf-8')
    
    # Check if custom 404 page is shown
    if '404' in content:
        print("✅ 404 error code found in response")
    else:
        print("❌ 404 error code NOT found")
    
    if 'GB Farms' in content:
        print("✅ Custom 404 page with GB Farms branding shown")
    else:
        print("❌ Custom 404 page NOT shown")
    
    if 'video' in content.lower():
        print("✅ Video background included")
    else:
        print("❌ Video background NOT included")
    
    if 'Looks like you\'re lost' in content:
        print("✅ Custom 404 message found")
    else:
        print("❌ Custom 404 message NOT found")
    
    # Check that it's NOT Django's debug page
    if 'Using the URLconf defined in' in content:
        print("❌ Still showing Django debug page")
    else:
        print("✅ NOT showing Django debug page - Custom error page working!")
    
    print("\n" + "=" * 60)
    print("✅ Production mode 404 test completed!")

if __name__ == "__main__":
    test_real_404_in_production()
