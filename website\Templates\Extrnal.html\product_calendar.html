{% extends "Extrnal.html/EX.base.html" %}
{% load static %}
{% load custom_filters %}

{% block head %}
    <title>Calendar - GB Farms</title>
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            position: relative;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.02"/><circle cx="50" cy="10" r="0.5" fill="%23ffffff" opacity="0.03"/><circle cx="20" cy="80" r="0.5" fill="%23ffffff" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
            pointer-events: none;
            z-index: -1;
        }

        .calendar-container {
            background: linear-gradient(145deg, rgba(30, 41, 59, 0.95), rgba(15, 23, 42, 0.95));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        }

        .months-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 2rem 0;
        }

        @media (min-width: 768px) {
            .months-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 20px;
            }
        }

        .month-card {
            background: linear-gradient(145deg, #1e293b, #0f172a);
            border-radius: 20px;
            padding: 16px;
            position: relative;
            border: 1px solid rgba(51, 65, 85, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            overflow: hidden;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        @media (min-width: 768px) {
            .month-card {
                padding: 24px;
                min-height: 180px;
            }
        }

        .month-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .month-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            border-color: rgba(34, 197, 94, 0.5);
        }

        .month-card:hover::before {
            opacity: 1;
        }

        .month-name {
            font-size: 1.25rem;
            font-weight: 700;
            color: #e2e8f0;
            margin-bottom: 12px;
            position: relative;
            z-index: 2;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        @media (min-width: 768px) {
            .month-name {
                font-size: 1.5rem;
            }
        }

        .month-card.current-month {
            background: linear-gradient(145deg, #22c55e, #16a34a);
            border-color: #22c55e;
            box-shadow: 0 0 30px rgba(34, 197, 94, 0.4);
        }

        .month-card.current-month .month-name {
            color: white;
            font-size: 1.5rem;
        }

        @media (min-width: 768px) {
            .month-card.current-month .month-name {
                font-size: 1.75rem;
            }
        }

        .month-card.current-month .month-info {
            color: rgba(255, 255, 255, 0.9);
        }

        .month-info {
            font-size: 0.75rem;
            color: #94a3b8;
            position: relative;
            z-index: 2;
            line-height: 1.5;
        }

        @media (min-width: 768px) {
            .month-info {
                font-size: 0.875rem;
            }
        }

        .month-activities {
            margin-top: 12px;
            position: relative;
            z-index: 2;
        }

        .activity-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: linear-gradient(45deg, #22c55e, #16a34a);
            border-radius: 50%;
            margin-right: 6px;
            box-shadow: 0 2px 4px rgba(34, 197, 94, 0.3);
        }

        .farm-info {
            background: linear-gradient(145deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.05));
            backdrop-filter: blur(15px);
            border: 1px solid rgba(34, 197, 94, 0.3);
            border-radius: 20px;
            padding: 20px;
            margin-top: 32px;
            position: relative;
            overflow: hidden;
        }

        @media (min-width: 768px) {
            .farm-info {
                padding: 32px;
            }
        }

        .farm-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.05), rgba(22, 163, 74, 0.02));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .farm-info:hover::before {
            opacity: 1;
        }

        .contact-btn {
            background: linear-gradient(145deg, #22c55e, #16a34a);
            color: white;
            font-weight: 700;
            padding: 12px 24px;
            border-radius: 16px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(34, 197, 94, 0.2);
            position: relative;
            overflow: hidden;
        }

        @media (min-width: 768px) {
            .contact-btn {
                padding: 16px 32px;
            }
        }

        .contact-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .contact-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(34, 197, 94, 0.3);
            background: linear-gradient(145deg, #16a34a, #15803d);
        }

        .contact-btn:hover::before {
            left: 100%;
        }

        .page-title {
            background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .calendar-icon {
            display: inline-block;
            margin-right: 12px;
            font-size: 1em;
            vertical-align: middle;
            color: #22c55e;
            text-shadow: 0 2px 4px rgba(34, 197, 94, 0.3);
            animation: pulse 2s infinite;
            font-family: 'Segoe UI Emoji', 'Apple Color Emoji', 'Noto Color Emoji', sans-serif;
        }
        
        /* Calendar icon styling */
        .calendar-icon {
            display: inline-block;
            margin-right: 12px;
            width: 1.2em;
            height: 1.2em;
            vertical-align: middle;
            animation: pulse 2s infinite;
        }
        
        .calendar-icon svg {
            width: 100%;
            height: 100%;
            fill: #22c55e;
            filter: drop-shadow(0 2px 4px rgba(34, 197, 94, 0.3));
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        /* Modal Styles */
        #product-modal.hidden {
            opacity: 0;
            pointer-events: none;
        }

        #product-modal.hidden #modal-content {
            transform: scale(0.95);
        }

        #modal-content {
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .floating-element {
            position: absolute;
            width: 4px;
            height: 4px;
            background: linear-gradient(45deg, #22c55e, #16a34a);
            border-radius: 50%;
            opacity: 0.3;
            animation: float 10s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.3;
            }
            90% {
                opacity: 0.3;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .season-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            padding: 3px 6px;
            border-radius: 8px;
            font-size: 0.65rem;
            font-weight: 600;
            z-index: 3;
        }

        @media (min-width: 768px) {
            .season-badge {
                top: 12px;
                right: 12px;
                padding: 4px 8px;
                font-size: 0.75rem;
            }
        }

        .season-spring {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .season-summer {
            background: rgba(251, 191, 36, 0.2);
            color: #fbbf24;
            border: 1px solid rgba(251, 191, 36, 0.3);
        }

        .season-autumn {
            background: rgba(249, 115, 22, 0.2);
            color: #f97316;
            border: 1px solid rgba(249, 115, 22, 0.3);
        }

        .season-winter {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'gb-green': '#22c55e',
                        'gb-light-green': '#16a34a',
                        'gb-dark-green': '#15803d',
                    }
                }
            }
        }
    </script>
{% endblock %}

{% block header %}
    {% include "Extrnal.html/navebar.html" %}
{% endblock %}

{% block content %}
    <!-- Floating Elements -->
    <div class="floating-elements">
        <div class="floating-element" style="left: 10%; animation-delay: 0s;"></div>
        <div class="floating-element" style="left: 20%; animation-delay: 2s;"></div>
        <div class="floating-element" style="left: 30%; animation-delay: 4s;"></div>
        <div class="floating-element" style="left: 40%; animation-delay: 6s;"></div>
        <div class="floating-element" style="left: 50%; animation-delay: 8s;"></div>
        <div class="floating-element" style="left: 60%; animation-delay: 1s;"></div>
        <div class="floating-element" style="left: 70%; animation-delay: 3s;"></div>
        <div class="floating-element" style="left: 80%; animation-delay: 5s;"></div>
        <div class="floating-element" style="left: 90%; animation-delay: 7s;"></div>
    </div>

    <!-- Main Content -->
    <main class="pt-24 md:pt-32 pb-16 px-4 md:px-8 relative">
        <div class="max-w-7xl mx-auto">
            <!-- Page Title -->
            <div class="text-center mb-12 md:mb-16">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold page-title mb-4 md:mb-6">
                    <span class="calendar-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
                        </svg>
                    </span> GB Farms Calendar
                </h1>
                <p class="text-base md:text-lg lg:text-xl text-slate-300 max-w-2xl mx-auto leading-relaxed">
                    Explore our year-round farming activities, seasonal harvests, and agricultural planning
                </p>
            </div>

            <!-- Calendar Container -->
            <div class="calendar-container rounded-3xl p-4 md:p-8 shadow-2xl">
                
                <!-- Months Grid -->
                <div class="months-grid">
                    {% for month in months_data %}
                    <div class="month-card {% if month.is_current %}current-month{% endif %}" data-month="{{ month.month_index|add:"-1" }}" data-month-name="{{ month.name }}">
                        <div class="season-badge season-{{ month.season_name }}">{{ month.season_name|title }}</div>
                        <div>
                            <div class="month-name">{{ month.name }}</div>
                            <div class="month-info">
                                {% if month.activities %}
                                    {% for activity in month.activities|slice:":2" %}
                                        {{ activity.name }}{% if not forloop.last %}<br>{% endif %}
                                    {% endfor %}
                                    {% if month.activities|length > 2 %}
                                        ... and more
                                    {% endif %}
                                {% else %}
                                    Season planning
                                {% endif %}
                            </div>
                        </div>
                        <div class="month-activities">
                            {% for _ in month.activities %}
                            <div class="activity-dot"></div>
                            {% endfor %}
                        </div>
                        <div class="hidden-activities" style="display: none;">
                            {% for activity in month.activities %}
                            <span>{{ activity.name }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Farm Information -->
                <div class="farm-info">
                    <div class="text-center">
                        <h3 class="text-2xl md:text-3xl font-bold text-white mb-4">🌱 GB Farms - Year-Round Excellence</h3>
                        <p class="text-slate-300 mb-6 text-base md:text-lg leading-relaxed max-w-3xl mx-auto">
                            Our carefully planned agricultural calendar ensures the finest quality produce throughout the year. 
                            From spring planting to winter planning, every season brings its unique contributions to our sustainable farming practices.
                        </p>
                        <div class="flex flex-wrap justify-center gap-3 md:gap-4 mb-6 md:mb-8">
                            <span class="bg-green-500/20 text-green-400 px-3 py-2 md:px-4 md:py-2 rounded-full text-xs md:text-sm font-medium">🌸 Spring Planting</span>
                            <span class="bg-yellow-500/20 text-yellow-400 px-3 py-2 md:px-4 md:py-2 rounded-full text-xs md:text-sm font-medium">☀️ Summer Harvest</span>
                            <span class="bg-orange-500/20 text-orange-400 px-3 py-2 md:px-4 md:py-2 rounded-full text-xs md:text-sm font-medium">🍂 Autumn Abundance</span>
                            <span class="bg-blue-500/20 text-blue-400 px-3 py-2 md:px-4 md:py-2 rounded-full text-xs md:text-sm font-medium">❄️ Winter Planning</span>
                        </div>
                        <a href="{% url 'GB_FARM:external_contact' %}" class="contact-btn">
                            📞 Contact Our Farm Team
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Product Details Modal -->
    <div id="product-modal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 transition-opacity duration-300 hidden">
        <div id="modal-content" class="bg-slate-800 rounded-2xl shadow-2xl p-6 md:p-8 max-w-lg w-full m-4 relative transform">
            <button id="close-modal" class="absolute top-4 right-4 text-slate-400 hover:text-white transition-colors">
                <svg class="w-6 h-6 md:w-8 md:h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
            </button>
            <h3 id="modal-month-name" class="text-2xl md:text-3xl font-bold text-white mb-4 md:mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-green-400 to-blue-500"></h3>
            <ul id="modal-product-list" class="space-y-3 max-h-96 overflow-y-auto pr-4">
                <!-- Product items will be injected here -->
            </ul>
        </div>
    </div>

    <!-- Enhanced JavaScript for month calendar functionality -->
    <script>
        // Current year tracking
        let currentYear = parseInt('{{ current_year }}', 10);
        const currentMonth = new Date().getMonth(); // 0-based month index

        // Initialize calendar
        document.addEventListener('DOMContentLoaded', function() {
            addMonthInteractivity();
            highlightCurrentMonth();
        });

        // Update calendar display
        function updateCalendarDisplay() {
            document.getElementById('currentYear').textContent = currentYear;
        }

        // Highlight current month
        function highlightCurrentMonth() {
            const monthCards = document.querySelectorAll('.month-card');
            monthCards.forEach((card, index) => {
                if (index === currentMonth && currentYear === new Date().getFullYear()) {
                    card.classList.add('current-month');
                } else {
                    card.classList.remove('current-month');
                }
            });
        }
        
        // Modal handling
        const modal = document.getElementById('product-modal');
        const modalContent = document.getElementById('modal-content');
        const modalMonthName = document.getElementById('modal-month-name');
        const modalProductList = document.getElementById('modal-product-list');
        const closeModalBtn = document.getElementById('close-modal');

        function openModal() {
            modal.classList.remove('hidden');
        }

        function closeModal() {
            modal.classList.add('hidden');
        }

        closeModalBtn.addEventListener('click', closeModal);
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal();
            }
        });
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                closeModal();
            }
        });

        // Add interactivity to month cards
        function addMonthInteractivity() {
            const monthCards = document.querySelectorAll('.month-card');
            
            monthCards.forEach(card => {
                card.addEventListener('click', function() {
                    const monthName = this.dataset.monthName;
                    const activityElements = this.querySelectorAll('.hidden-activities span');
                    const activities = Array.from(activityElements).map(span => span.textContent.trim());

                    if (activities.length > 0) {
                        modalMonthName.textContent = `Products in ${monthName}`;
                        modalProductList.innerHTML = ''; // Clear previous list

                        activities.forEach(activity => {
                            const li = document.createElement('li');
                            li.className = 'bg-slate-700/50 p-4 rounded-lg text-slate-300 flex items-center transition-all duration-300 hover:bg-slate-600/50 hover:text-white';
                            li.innerHTML = `
                                <span class="bg-green-500/20 text-green-400 rounded-full h-6 w-6 flex items-center justify-center mr-4 text-sm flex-shrink-0">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
                                </span>
                                <span>${activity}</span>
                            `;
                            modalProductList.appendChild(li);
                        });

                        openModal();
                    } else {
                        console.log(`No products listed for ${monthName}`);
                    }
                });
            });
        }

        // Add enhanced styling and animations
        const style = document.createElement('style');
        style.textContent = `
            .months-grid {
                transition: opacity 0.3s ease, transform 0.3s ease;
            }

            .month-card:hover .season-badge {
                transform: scale(1.1);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            }

            .season-badge {
                transition: all 0.3s ease;
            }
        `;
        document.head.appendChild(style);
    </script>
{% endblock %}

{% block footer %}
    {% include "Extrnal.html/footer.html" %}
{% endblock %}