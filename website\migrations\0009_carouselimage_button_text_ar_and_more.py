# Generated by Django 4.2.20 on 2025-05-07 10:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0008_alter_product_unit_category'),
    ]

    operations = [
        migrations.AddField(
            model_name='carouselimage',
            name='button_text_ar',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Arabic Button Text'),
        ),
        migrations.AddField(
            model_name='carouselimage',
            name='description_ar',
            field=models.TextField(blank=True, null=True, verbose_name='Arabic Description'),
        ),
        migrations.AddField(
            model_name='carouselimage',
            name='title_ar',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Title'),
        ),
        migrations.AddField(
            model_name='category',
            name='description_ar',
            field=models.TextField(blank=True, null=True, verbose_name='Arabic Description'),
        ),
        migrations.Add<PERSON>ield(
            model_name='category',
            name='name_ar',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Arabic Name'),
        ),
        migrations.AddField(
            model_name='contactinfo',
            name='additional_info_ar',
            field=models.TextField(blank=True, null=True, verbose_name='Arabic Additional Info'),
        ),
        migrations.AddField(
            model_name='contactinfo',
            name='address_ar',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Address'),
        ),
        migrations.AddField(
            model_name='contactinfo',
            name='title_ar',
            field=models.CharField(blank=True, default='اتصل بنا', max_length=100, null=True, verbose_name='Arabic Title'),
        ),
        migrations.AddField(
            model_name='contactinfo',
            name='working_hours_ar',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Arabic Working Hours'),
        ),
        migrations.AddField(
            model_name='delivery',
            name='name_ar',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Name'),
        ),
        migrations.AddField(
            model_name='footer',
            name='address_ar',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Address'),
        ),
        migrations.AddField(
            model_name='offer',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='offer',
            name='description_ar',
            field=models.TextField(blank=True, null=True, verbose_name='Arabic Description'),
        ),
        migrations.AddField(
            model_name='ordermessage',
            name='message_ar',
            field=models.TextField(blank=True, null=True, verbose_name='Arabic Message'),
        ),
        migrations.AddField(
            model_name='product',
            name='description_ar',
            field=models.TextField(blank=True, null=True, verbose_name='Arabic Description'),
        ),
        migrations.AddField(
            model_name='product',
            name='name_ar',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Name'),
        ),
        migrations.AddField(
            model_name='review',
            name='comment_ar',
            field=models.TextField(blank=True, null=True, verbose_name='Arabic Comment'),
        ),
        migrations.AddField(
            model_name='websitevideo',
            name='title_ar',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Title'),
        ),
        migrations.AddField(
            model_name='zone',
            name='name_ar',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Arabic Name'),
        ),
    ]
