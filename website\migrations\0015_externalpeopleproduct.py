# Generated by Django 4.2.20 on 2025-06-02 10:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0014_delete_customerinquiry_alter_user_user_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExternalPeopleProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, help_text="Description of the external person's offering", null=True)),
                ('varieties', models.JSONField(blank=True, default=list, help_text='List of varieties offered by this external person for this product')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('external_person', models.ForeignKey(limit_choices_to={'user_type': 'customer'}, on_delete=django.db.models.deletion.CASCADE, related_name='offered_products', to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='external_offerings', to='website.product')),
            ],
            options={
                'verbose_name': 'External Person Product',
                'verbose_name_plural': 'External People Products',
                'ordering': ['-created_at'],
                'unique_together': {('external_person', 'product')},
            },
        ),
    ]
