{% extends "GB_FARM/internal/base.html" %}
{% load i18n %}

{% block title %}Admin - Career Management{% endblock %}

{% block page_title %}Career Management{% endblock %}

{% block page_actions %}
<a href="{% url 'GB_FARM:admin_career_create' %}" class="btn btn-primary">
    <i class="bi bi-plus-circle"></i> Add New Career
</a>
{% endblock %}

{% block content %}
<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-subtitle text-muted">Total Careers</h6>
                        <h2 class="card-title mb-0">{{ total_careers }}</h2>
                    </div>
                    <div class="icon-box bg-light rounded p-3">
                        <i class="bi bi-briefcase text-primary fs-3"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-subtitle text-muted">Active Postings</h6>
                        <h2 class="card-title mb-0">{{ active_careers }}</h2>
                    </div>
                    <div class="icon-box bg-light rounded p-3">
                        <i class="bi bi-check-circle text-success fs-3"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-subtitle text-muted">Inactive Postings</h6>
                        <h2 class="card-title mb-0">{{ inactive_careers }}</h2>
                    </div>
                    <div class="icon-box bg-light rounded p-3">
                        <i class="bi bi-x-circle text-danger fs-3"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Card -->
<div class="card shadow-sm mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="q" class="form-label">Search</label>
                <input type="text" class="form-control" id="q" name="q" placeholder="Search careers..." value="{{ search_query }}">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="">All Statuses</option>
                    <option value="active" {% if selected_status == 'active' %}selected{% endif %}>Active</option>
                    <option value="inactive" {% if selected_status == 'inactive' %}selected{% endif %}>Inactive</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="job_type" class="form-label">Job Type</label>
                <select name="job_type" id="job_type" class="form-select">
                    <option value="">All Types</option>
                    {% for job_type_value, job_type_display in job_types %}
                    <option value="{{ job_type_value }}" {% if selected_job_type == job_type_value %}selected{% endif %}>{{ job_type_display }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-search"></i> Filter
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Careers Table -->
<div class="card shadow-sm">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Title</th>
                        <th>Department</th>
                        <th>Location</th>
                        <th>Job Type</th>
                        <th>Experience</th>
                        <th>Status</th>
                        <th>Date Posted</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for career in careers %}
                    <tr>
                        <td>
                            <a href="{% url 'GB_FARM:career_detail' id=career.id %}" class="fw-bold text-decoration-none">
                                {{ career.title }}
                            </a>
                        </td>
                        <td>{{ career.department }}</td>
                        <td>{{ career.location }}</td>
                        <td>
                            {% if career.job_type == 'full_time' %}
                            <span class="badge bg-primary">Full Time</span>
                            {% elif career.job_type == 'part_time' %}
                            <span class="badge bg-info">Part Time</span>
                            {% elif career.job_type == 'contract' %}
                            <span class="badge bg-warning">Contract</span>
                            {% elif career.job_type == 'internship' %}
                            <span class="badge bg-secondary">Internship</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if career.experience_level == 'entry' %}
                            <span class="badge bg-success">Entry</span>
                            {% elif career.experience_level == 'mid' %}
                            <span class="badge bg-info">Mid Level</span>
                            {% elif career.experience_level == 'senior' %}
                            <span class="badge bg-primary">Senior</span>
                            {% elif career.experience_level == 'lead' %}
                            <span class="badge bg-warning">Lead</span>
                            {% elif career.experience_level == 'manager' %}
                            <span class="badge bg-danger">Manager</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if career.is_active %}
                            <span class="badge bg-success">Active</span>
                            {% else %}
                            <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </td>
                        <td>{{ career.created_at|date:"M d, Y" }}</td>
                        <td>
                            <div class="d-flex">
                                <a href="{% url 'GB_FARM:admin_career_edit' id=career.id %}" class="btn btn-sm btn-outline-primary me-1">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{% url 'GB_FARM:admin_career_delete' id=career.id %}" class="btn btn-sm btn-outline-danger">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="empty-state">
                                <i class="bi bi-briefcase display-4 text-muted"></i>
                                <p class="mt-3">No career postings found.</p>
                                <a href="{% url 'GB_FARM:admin_career_create' %}" class="btn btn-sm btn-primary">
                                    <i class="bi bi-plus-circle me-1"></i> Add New Career
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if careers.has_other_pages %}
        <nav aria-label="Career pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if careers.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_job_type %}&job_type={{ selected_job_type }}{% endif %}" aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ careers.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_job_type %}&job_type={{ selected_job_type }}{% endif %}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                </li>
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% endif %}
                
                {% for i in careers.paginator.page_range %}
                    {% if careers.number == i %}
                    <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                    {% elif i > careers.number|add:'-3' and i < careers.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ i }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_job_type %}&job_type={{ selected_job_type }}{% endif %}">{{ i }}</a>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if careers.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ careers.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_job_type %}&job_type={{ selected_job_type }}{% endif %}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ careers.paginator.num_pages }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if selected_job_type %}&job_type={{ selected_job_type }}{% endif %}" aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <li class="page-item disabled">
                    <a class="page-link" href="#" aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tooltips initialization if needed
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    [...tooltips].map(tooltip => new bootstrap.Tooltip(tooltip));
});
</script>
{% endblock %} 