{% extends "Extrnal.html/EX.base.html" %}
{% load static %}

{% block head %}
    <title>Other Products - GB Farms</title>
    <style>
        .others-hero-bg {
            background-image: url("{% static 'images/prodect mango/close-up-orange-tree-with-many-leaves.jpg' %}");
            background-size: cover;
            background-position: center;
        }
    </style>
{% endblock %}

{% block header %}
    {% include "Extrnal.html/navebar.html" %}
{% endblock %}

{% block content %}
    <!-- Main Section - Other Products Page -->
    <main class="relative h-[80vh] md:h-[90vh] w-full flex items-center justify-start others-hero-bg">
        <div class="pl-4 pt-20 md:pl-20 lg:pl-40 md:pt-24 max-w-5xl z-10">
            {% include 'Extrnal.html/includes/product_hero_content.html' %}
        </div>
    </main>

    <!-- second section -->
    <section class="relative h-auto md:h-[70vh] w-full bg-white flex flex-col items-center justify-center mb-16 md:mb-32 gap-y-6 md:gap-y-10 py-16 md:py-0">
        <!-- Background pattern -->
        <div
            class="absolute inset-0 bg-[url('https://flowbite.s3.amazonaws.com/docs/jumbotron/hero-pattern.svg')] opacity-10">
        </div>

        <!-- Product cards container -->
        <div class="flex flex-col md:flex-row justify-between gap-4 z-10 w-full px-4 md:px-48">
            <!-- Grapes Card -->
            <a href="{% url 'GB_FARM:external_grapes' %}" class="group">
                <img src="{% static 'images/card1.png' %}" alt="Grapes"
                    class="w-full md:w-96 h-64 md:h-full rounded-xl transition-transform duration-300 group-hover:scale-105 object-cover">
            </a>

            <!-- Mangoes Card -->
            <a href="{% url 'GB_FARM:external_mangoes' %}" class="group">
                <img src="{% static 'images/card3.png' %}" alt="Mangoes"
                    class="w-full md:w-96 h-64 md:h-full rounded-xl transition-transform duration-300 group-hover:scale-105 object-cover">
            </a>

            <!-- Others Card - Highlighted -->
            <a href="{% url 'GB_FARM:external_others' %}" class="group">
                <img src="{% static 'images/mashmah.png' %}" alt="Other Products"
                    class="w-full md:w-96 h-64 md:h-full rounded-xl transition-transform duration-300 group-hover:scale-105 object-cover">
            </a>
        </div>
    </section>

    <!--Third section - Dynamic Other Products-->
    <section class="h-auto w-full bg-gray-50 py-16 md:py-20 relative overflow-hidden">
        <!-- Background curved line SVG -->
        <div class="absolute top-0 right-0 w-full h-full opacity-20">
            <svg viewBox="0 0 1200 600" class="w-full h-full" preserveAspectRatio="none">
                <!-- Curved line matching the image -->
                <path d="M400 100 Q 800 50, 1100 200 Q 1150 250, 1200 350" 
                      stroke="#ef4444" 
                      stroke-width="3" 
                      fill="none" 
                      stroke-dasharray="10,5"/>
                <!-- Additional curved element -->
                <path d="M300 400 Q 600 350, 900 450 Q 1000 480, 1100 500" 
                      stroke="#ef4444" 
                      stroke-width="2" 
                      fill="none" 
                      opacity="0.6"/>
            </svg>
        </div>

        <div class="container mx-auto px-4 md:px-8 relative z-10">
            <!-- Title with decorative line -->
            <div class="relative mb-12 md:mb-20">
                <h2 class="text-red-500 text-4xl md:text-8xl font-bold">Others</h2>
                <!-- Decorative line extending from title -->
                <div class="absolute top-6 md:top-12 left-32 md:left-80 w-1/3 md:w-2/3 h-1 bg-red-500"></div>
                <!-- Extended decorative line -->
                <div class="absolute top-6 md:top-12 left-40 md:left-[32rem] w-1/3 md:w-2/3 h-1 bg-red-500 opacity-60"></div>
            </div>
            
            <!-- Content layout matching the image -->
            <div class="flex flex-wrap items-start">
                <!-- Left column text -->
                <div class="w-full md:w-1/2 md:pr-12 space-y-6 mb-8 md:mb-0">
                    <p class="text-red-500 text-base md:text-lg leading-relaxed">
                        We proudly grow a diverse selection of other quality 
                        agricultural products. Our farms produce various fruits 
                        and vegetables that meet international standards.
                    </p>
                    
                    <p class="text-red-500 text-base md:text-lg leading-relaxed">
                        With our experienced team and modern agricultural 
                        techniques, we ensure the highest quality across all 
                        our product lines, delivering fresh and nutritious 
                        produce to markets worldwide.
                    </p>
                </div>
                
                <!-- Right column text -->
                <div class="w-full md:w-1/2 md:pl-12 space-y-6">
                    <p class="text-red-500 text-base md:text-lg leading-relaxed">
                        Our other products complement our main crops, 
                        providing customers with a comprehensive range 
                        of fresh agricultural products.
                    </p>
                    
                    <p class="text-red-500 text-base md:text-lg leading-relaxed">
                        Each product is carefully cultivated and harvested 
                        at the optimal time to ensure maximum freshness, 
                        flavor, and nutritional value for our customers.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Dynamic Other Products Section -->
    <section class="h-auto w-full bg-gray-50 py-16 md:py-20 relative overflow-hidden">
        <!-- Background curved line SVG -->
        <div class="absolute bottom-0 left-0 w-full h-full opacity-10">
            <svg viewBox="0 0 1200 600" class="w-full h-full" preserveAspectRatio="none">
                <!-- Main curved line from bottom left -->
                <path d="M0 500 Q 300 400, 600 350 Q 900 300, 1200 200" 
                      stroke="#ef4444" 
                      stroke-width="3" 
                      fill="none" 
                      stroke-dasharray="15,8"/>
            </svg>
        </div>

        <div class="container mx-auto px-4 md:px-8 relative z-10">
            <div class="flex flex-wrap items-start">
                <!-- Left side - Varieties content -->
                <div class="w-full md:w-2/3 md:pr-12 mb-8 md:mb-0">
                    <!-- Title -->
                    <div class="mb-12 md:mb-16">
                        <h2 class="text-red-500 text-3xl md:text-5xl font-bold leading-tight">
                            Our<br>
                            <span class="text-red-400">Products</span>
                        </h2>
                    </div>
                    
                    <!-- Dynamic Other Products Grid -->
                    <div class="space-y-6 md:space-y-8">
                        {% if other_products %}
                            {% for product in other_products %}
                                {% if forloop.counter0|divisibleby:3 %}
                                    {% if not forloop.first %}</div>{% endif %}
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 {% if not forloop.last %}pb-6 md:pb-8 border-b-2 border-red-500{% endif %}">
                                {% endif %}
                                
                                <div class="text-center {% if not forloop.counter0|divisibleby:3 and not forloop.last %}md:border-l border-gray-300 md:px-4{% endif %}">
                                    <h3 class="text-red-600 text-xl md:text-2xl font-bold mb-2">{{ product.name }}</h3>
                                    <p class="text-gray-600 text-sm mb-1">{{ product.date_range_display }}</p>
                                    <p class="text-gray-600 text-sm">{{ product.weight_range_display }}</p>
                                </div>
                                
                                {% if forloop.last %}
                                    </div>
                                {% endif %}
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-8">
                                <p class="text-red-500">No other products available.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Right side - Image -->
                <div class="w-full md:w-1/3 flex justify-center items-start">
                    <div class="relative">
                        <img src="{% static 'images/prodect mango/wmremove-transformed.png' %}" 
                             alt="Other Products" 
                             class="h-auto max-w-full w-64 md:w-80 drop-shadow-2xl">
                        <!-- Optional decorative element -->
                        <div class="absolute -bottom-4 -right-4 w-20 h-20 bg-red-100 rounded-full opacity-20"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block footer %}
    {% include "Extrnal.html/footer.html" %}
{% endblock %}