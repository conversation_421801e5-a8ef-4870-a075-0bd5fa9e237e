# Generated by Django 4.2.20 on 2025-06-18 18:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0025_prodcat_remove_calendarevent_product_type_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='prodcat',
            options={'ordering': ['month', 'date_from', 'name'], 'verbose_name': 'Product Calendar', 'verbose_name_plural': 'Product Calendars'},
        ),
        migrations.RemoveField(
            model_name='prodcat',
            name='description',
        ),
        migrations.RemoveField(
            model_name='prodcat',
            name='description_ar',
        ),
        migrations.RemoveField(
            model_name='prodcat',
            name='name_ar',
        ),
        migrations.AddField(
            model_name='prodcat',
            name='month',
            field=models.PositiveIntegerField(choices=[(1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'), (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'), (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')], default='January', verbose_name='Month'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='prodcat',
            name='date_from',
            field=models.DateField(verbose_name='From Date'),
        ),
        migrations.AlterField(
            model_name='prodcat',
            name='date_to',
            field=models.DateField(verbose_name='To Date'),
        ),
        migrations.AlterField(
            model_name='prodcat',
            name='name',
            field=models.CharField(max_length=255, verbose_name='Product Name'),
        ),
        migrations.AddIndex(
            model_name='prodcat',
            index=models.Index(fields=['month'], name='website_pro_month_c75e0c_idx'),
        ),
    ]
