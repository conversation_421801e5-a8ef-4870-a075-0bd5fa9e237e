# Generated by Django 4.2.20 on 2025-06-01 18:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0012_career'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerInquiry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('subject', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('inquiry_type', models.CharField(choices=[('bulk_purchase', 'Bulk Purchase Inquiry'), ('export', 'Export Inquiry'), ('other', 'Other Inquiry')], default='other', max_length=20)),
                ('is_read', models.BooleanField(default=False)),
                ('last_reply', models.TextField(blank=True, null=True)),
                ('replied_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Customer Inquiry',
                'verbose_name_plural': 'Customer Inquiries',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AlterField(
            model_name='user',
            name='user_type',
            field=models.CharField(choices=[('admin', 'admin'), ('employee', 'employee'), ('customer', 'customer')], default='customer', max_length=10),
        ),
    ]
