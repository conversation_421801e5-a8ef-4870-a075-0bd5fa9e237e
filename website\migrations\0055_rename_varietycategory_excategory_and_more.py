# Generated by Django 4.2.20 on 2025-07-20 10:06

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0054_variety_varietyproduct_varietycategory'),
    ]

    operations = [
        migrations.RenameModel(
            old_name='VarietyCategory',
            new_name='ExCategory',
        ),
        migrations.RenameModel(
            old_name='VarietyProduct',
            new_name='ExProduct',
        ),
        migrations.RenameModel(
            old_name='Variety',
            new_name='ExVariety',
        ),
        migrations.AlterModelOptions(
            name='excategory',
            options={'ordering': ['name'], 'verbose_name': 'Category', 'verbose_name_plural': 'Categories'},
        ),
        migrations.AlterModelOptions(
            name='exproduct',
            options={'ordering': ['title'], 'verbose_name': 'Product', 'verbose_name_plural': 'Products'},
        ),
    ]
