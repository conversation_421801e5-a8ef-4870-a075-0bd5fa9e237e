# Generated by Django 4.2.20 on 2025-06-02 14:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0017_contactusmessage'),
    ]

    operations = [
        migrations.AddField(
            model_name='contactusmessage',
            name='business_type',
            field=models.CharField(blank=True, choices=[('supermarket', 'Supermarkets'), ('wholesaler', 'Wholesaler'), ('distributor', 'Distributor')], max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='contactusmessage',
            name='company_name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='contactusmessage',
            name='find_out_source',
            field=models.CharField(blank=True, choices=[('internet', 'Internet'), ('exhibitions', 'Exhibitions'), ('search_engine', 'Search Engine')], max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='contactusmessage',
            name='phone_number',
            field=models.Char<PERSON>ield(blank=True, max_length=20, null=True),
        ),
    ]
