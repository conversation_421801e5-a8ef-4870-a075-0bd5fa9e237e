{% extends 'GB_FARM/admin_base.html' %}

{% block title %}Admin Dashboard{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card shadow-sm mb-4">
                <div class="card-header text-white">
                    <h5 class="mb-0">Admin Dashboard</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:admin_dashboard' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i> Orders
                    </a>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-box me-2"></i> Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i> Customers
                    </a>
                    <a href="{% url 'GB_FARM:admin_calendar_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-calendar-alt me-2"></i> Calendar Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                    <a href="{% url 'GB_FARM:admin_invoice_logs' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-file-invoice me-2"></i> Invoice Logs
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <h1 class="mb-4">Admin Dashboard</h1>
            
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">Total Orders</h6>
                                    <h2 class="mt-2 mb-0">{{ total_orders }}</h2>
                                </div>
                                <i class="fas fa-shopping-cart fa-2x opacity-50"></i>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between bg-primary-dark">
                            <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="text-white text-decoration-none small">View Details</a>
                            <i class="fas fa-arrow-right text-white small"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">Total Products</h6>
                                    <h2 class="mt-2 mb-0">{{ total_products }}</h2>
                                </div>
                                <i class="fas fa-box fa-2x opacity-50"></i>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between bg-success-dark">
                            <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="text-white text-decoration-none small">View Details</a>
                            <i class="fas fa-arrow-right text-white small"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">Total Customers</h6>
                                    <h2 class="mt-2 mb-0">{{ total_customers }}</h2>
                                </div>
                                <i class="fas fa-users fa-2x opacity-50"></i>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between bg-info-dark">
                            <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="text-white text-decoration-none small">View Details</a>
                            <i class="fas fa-arrow-right text-white small"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">Unread Messages</h6>
                                    <h2 class="mt-2 mb-0">{{ unread_messages }}</h2>
                                </div>
                                <i class="fas fa-envelope fa-2x opacity-50"></i>
                            </div>
                        </div>
                        <div class="card-footer d-flex align-items-center justify-content-between bg-warning-dark">
                            <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="text-white text-decoration-none small">View Details</a>
                            <i class="fas fa-arrow-right text-white small"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Order Status -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Order Status</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-warning mb-0">{{ pending_orders }}</h3>
                                        <p class="text-muted">Pending</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-primary mb-0">{{ processing_orders }}</h3>
                                        <p class="text-muted">Processing</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-info mb-0">{{ shipped_orders }}</h3>
                                        <p class="text-muted">Shipped</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h3 class="text-success mb-0">{{ delivered_orders }}</h3>
                                        <p class="text-muted">Delivered</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Recent Orders</h5>
                            <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="btn btn-sm btn-outline-primary">View All</a>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Order #</th>
                                            <th>Customer</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for order in recent_orders %}
                                        <tr>
                                            <td>{{ order.id }}</td>
                                            <td>{{ order.user.username }}</td>
                                            <td>EGP {{ order.total_amount|floatformat:2 }}</td>
                                            <td>
                                                <span class="badge bg-{% if order.status == 'delivered' %}success{% elif order.status == 'cancelled' %}danger{% elif order.status == 'shipped' %}info{% else %}warning{% endif %}">
                                                    {{ order.get_status_display }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="4" class="text-center py-3">No recent orders</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .bg-primary-dark {
        background-color: rgba(0, 0, 0, 0.15);
    }
    .bg-success-dark {
        background-color: rgba(0, 0, 0, 0.15);
    }
    .bg-info-dark {
        background-color: rgba(0, 0, 0, 0.15);
    }
    .bg-warning-dark {
        background-color: rgba(0, 0, 0, 0.15);
    }
</style>
{% endblock %} 