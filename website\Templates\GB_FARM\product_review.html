{% extends 'GB_FARM/base.html' %}
{% load static %}

{% block title %}Reviews for {{ product.name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <!-- Product Header -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        {% if product.image %}
                        <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-fluid rounded me-3" style="width: 100px; height: 100px; object-fit: cover;">
                        {% else %}
                        <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" style="width: 100px; height: 100px;">
                            <i class="fas fa-image text-muted"></i>
                        </div>
                        {% endif %}
                        <div>
                            <h2 class="mb-1">{{ product.name }}</h2>
                            <p class="text-muted mb-0">{{ product.category.name }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Review Form -->
            {% if user.is_authenticated %}
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">{% if user_review %}Update Your Review{% else %}Write a Review{% endif %}</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label class="form-label">Rating</label>
                            <div class="rating">
                                {% for i in "12345"|make_list %}
                                <input type="radio" name="rating" value="{{ i }}" id="star{{ i }}" 
                                       {% if user_review and user_review.rating == i|add:"0" %}checked{% endif %}>
                                <label for="star{{ i }}"><i class="fas fa-star"></i></label>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="comment" class="form-label">Your Review</label>
                            <textarea class="form-control" id="comment" name="comment" rows="4" required>{{ user_review.comment|default:'' }}</textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            {% if user_review %}Update Review{% else %}Submit Review{% endif %}
                        </button>
                    </form>
                </div>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Please <a href="{% url 'GB_FARM:login' %}">login</a> to write a review.
            </div>
            {% endif %}

            <!-- Reviews List -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Customer Reviews</h5>
                </div>
                <div class="card-body">
                    {% if reviews %}
                        {% for review in reviews %}
                        <div class="review-item {% if not forloop.last %}border-bottom pb-3 mb-3{% endif %}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ review.user.username }}</h6>
                                    <div class="rating-display mb-2">
                                        {% for i in "12345"|make_list %}
                                        <i class="fas fa-star {% if i|add:'0' <= review.rating %}text-warning{% else %}text-muted{% endif %}"></i>
                                        {% endfor %}
                                    </div>
                                    <p class="mb-1">{{ review.comment }}</p>
                                    <small class="text-muted">{{ review.created_at|date:"F j, Y" }}</small>
                                </div>
                                {% if user == review.user %}
                                <div class="dropdown">
                                    <button class="btn btn-link text-muted p-0" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li>
                                            <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#editReviewModal{{ review.id }}">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item text-secondary" href="#" data-bs-toggle="modal" data-bs-target="#deleteReviewModal{{ review.id }}">
                                                <i class="fas fa-trash"></i> Delete
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Edit Review Modal -->
                        <div class="modal fade" id="editReviewModal{{ review.id }}" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Edit Review</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <form method="post">
                                        {% csrf_token %}
                                        <div class="modal-body">
                                            <div class="mb-3">
                                                <label class="form-label">Rating</label>
                                                <div class="rating">
                                                    {% for i in "12345"|make_list %}
                                                    <input type="radio" name="rating" value="{{ i }}" id="editStar{{ review.id }}{{ i }}"
                                                           {% if i|add:'0' == review.rating %}checked{% endif %}>
                                                    <label for="editStar{{ review.id }}{{ i }}"><i class="fas fa-star"></i></label>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="editComment{{ review.id }}" class="form-label">Your Review</label>
                                                <textarea class="form-control" id="editComment{{ review.id }}" name="comment" rows="4">{{ review.comment }}</textarea>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                            <button type="submit" class="btn btn-primary">Update Review</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Delete Review Modal -->
                        <div class="modal fade" id="deleteReviewModal{{ review.id }}" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Delete Review</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <p>Are you sure you want to delete your review? This action cannot be undone.</p>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <form method="post" action="{% url 'GB_FARM:delete_review' review.id %}" class="d-inline">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-danger">Delete Review</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted mb-0">No reviews yet. Be the first to review this product!</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.rating {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
}

.rating input {
    display: none;
}

.rating label {
    cursor: pointer;
    font-size: 1.5rem;
    color: #ddd;
    padding: 0 0.2rem;
}

.rating label:hover,
.rating label:hover ~ label,
.rating input:checked ~ label {
    color: #ffd700;
}

.rating-display {
    font-size: 1rem;
}
</style>
{% endblock %} 