{% extends 'GB_FARM/base.html' %}

{% block title %}Order Communication{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Messages for Order #{{ order.id }}</h5>
                    <a href="{% url 'GB_FARM:order_details' pk=order.id %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i> Back to Order
                    </a>
                </div>
                <div class="card-body">
                    <!-- Order Summary -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Order Details</h6>
                                    <p class="mb-1"><strong>Date:</strong> {{ order.created_at|date:"F d, Y" }}</p>
                                    <p class="mb-1"><strong>Total Amount:</strong> EGP {{ order.total_amount }}</p>
                                    <p class="mb-1"><strong>Status:</strong> 
                                        <span class="badge bg-{% if order.status == 'delivered' %}success{% elif order.status == 'cancelled' %}danger{% elif order.status == 'shipped' %}info{% else %}warning{% endif %}">
                                            {{ order.get_status_display }}
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Shipping Address</h6>
                                    <p>{{ order.shipping_address|linebreaks }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Message History -->
                    <div class="message-history mb-4">
                        <h6 class="mb-3">Message History</h6>
                        {% if order_messages %}
                            <div class="message-container">
                                {% for message in order_messages %}
                                    <div class="message-item mb-3 {% if message.sender == request.user %}sent{% else %}received{% endif %}">
                                        <div class="message-content p-3 rounded {% if message.sender == request.user %}bg-primary text-white{% else %}bg-light{% endif %}">
                                            <p class="mb-1">{{ message.message }}</p>
                                            <div class="message-meta d-flex justify-content-between align-items-center mt-2">
                                                <small>
                                                    {% if message.sender.user_type == 'admin' %}
                                                        <i class="fas fa-user-shield me-1"></i> Admin
                                                    {% else %}
                                                        <i class="fas fa-user me-1"></i> You
                                                    {% endif %}
                                                </small>
                                                <small>{{ message.created_at|date:"M d, Y H:i" }}</small>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                No messages for this order yet.
                            </div>
                        {% endif %}
                    </div>

                    <!-- Reply Form -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Reply to Admin</h6>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label for="message" class="form-label">Your Message</label>
                                    <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">Send Reply</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .message-container {
        max-height: 400px;
        overflow-y: auto;
        padding-right: 5px;
    }
    .message-item.sent {
        display: flex;
        justify-content: flex-end;
    }
    .message-item.sent .message-content {
        max-width: 80%;
    }
    .message-item.received .message-content {
        max-width: 80%;
    }
</style>
{% endblock %} 