{% extends 'GB_FARM/base.html' %}
{% load custom_filters %}

{% block title %}Product Reviews{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-md-8">
            <h2 class="mb-4">Reviews for {{ product.name }}</h2>
            
            {% if messages %}
            <div class="messages mb-4">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {{ message }}
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            <!-- Review Form -->
            {% if user.is_authenticated %}
            <div class="card mb-5">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Write a Review</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'GB_FARM:product_review' product.id %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label class="form-label">Rating</label>
                            <div class="rating">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="rating" id="rating1" value="1" required>
                                    <label class="form-check-label" for="rating1">1 ⭐</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="rating" id="rating2" value="2">
                                    <label class="form-check-label" for="rating2">2 ⭐</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="rating" id="rating3" value="3">
                                    <label class="form-check-label" for="rating3">3 ⭐</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="rating" id="rating4" value="4">
                                    <label class="form-check-label" for="rating4">4 ⭐</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="rating" id="rating5" value="5">
                                    <label class="form-check-label" for="rating5">5 ⭐</label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="comment" class="form-label">Your Review</label>
                            <textarea class="form-control" id="comment" name="comment" rows="4" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Submit Review</button>
                    </form>
                </div>
            </div>
            {% else %}
            <div class="alert alert-info mb-4">
                Please <a href="{% url 'GB_FARM:loginpage' %}?next={{ request.path }}">login</a> to write a review.
            </div>
            {% endif %}
            
            <!-- Reviews List -->
            <h3 class="mb-3">Customer Reviews ({{ reviews.count }})</h3>
            
            {% if reviews %}
                {% for review in reviews %}
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <h5 class="mb-0">{{ review.user.username }}</h5>
                                <span class="text-muted">{{ review.created_at|date:"F d, Y" }}</span>
                            </div>
                            <div class="rating">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= review.rating %}
                                    <i class="bi bi-star-fill text-warning"></i>
                                    {% else %}
                                    <i class="bi bi-star text-muted"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <p class="card-text">{{ review.comment }}</p>
                        
                        {% if review.user == user %}
                        <div class="mt-3">
                            <a href="{% url 'GB_FARM:edit_review' product.id review.id %}" class="btn btn-sm btn-outline-primary me-2">Edit</a>
                            <a href="{% url 'GB_FARM:delete_review' review.id %}" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this review?')">Delete</a>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
                
                <!-- Pagination -->
                {% if reviews.has_other_pages %}
                <nav aria-label="Reviews pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if reviews.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ reviews.previous_page_number }}">Previous</a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">Previous</span>
                        </li>
                        {% endif %}
                        
                        {% for i in reviews.paginator.page_range %}
                            {% if reviews.number == i %}
                            <li class="page-item active">
                                <span class="page-link">{{ i }}</span>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if reviews.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ reviews.next_page_number }}">Next</a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">Next</span>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="alert alert-light text-center py-4">
                    <p class="mb-0">No reviews yet. Be the first to review this product!</p>
                </div>
            {% endif %}
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">About This Product</h5>
                    <div class="text-center mb-3">
                        {% if product.image %}
                            <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-fluid mb-3" style="max-height: 200px; object-fit: contain;">
                        {% endif %}
                        <h4>{{ product.name }}</h4>
                        <h6 class="text-success">EGP {{ product.price }}</h6>
                    </div>
                    <p class="card-text">{{ product.description|truncatechars:150 }}</p>
                    <div class="d-grid">
                        <a href="{% url 'GB_FARM:product_detail' product.id %}" class="btn btn-outline-primary">View Product Details</a>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Review Summary</h5>
                    <div class="text-center">
                        <h1 class="display-4">{{ average_rating|floatformat:1 }}</h1>
                        <div class="rating mb-2">
                            {% for i in "12345" %}
                                {% if forloop.counter <= average_rating|floatformat:0 %}
                                <i class="bi bi-star-fill text-warning fs-4"></i>
                                {% elif forloop.counter <= average_rating|add:0.5|floatformat:0 %}
                                <i class="bi bi-star-half text-warning fs-4"></i>
                                {% else %}
                                <i class="bi bi-star text-muted fs-4"></i>
                                {% endif %}
                            {% endfor %}
                        </div>
                        <p class="text-muted mb-0">Based on {{ reviews.count }} reviews</p>
                    </div>
                    
                    <hr>
                    
                    <div class="rating-breakdown">
                        {% for i in "54321" %}
                        {% with index=forloop.counter0 %}
                        <div class="d-flex align-items-center mb-2">
                            <div class="me-2" style="width: 60px;">{{ i }} star</div>
                            <div class="progress flex-grow-1">
                                <div class="progress-bar bg-warning" role="progressbar" style="width: {% widthratio rating_percentages|index:index 1 1 %}%"></div>
                            </div>
                            <div class="ms-2" style="width: 40px; text-align: right;">{{ rating_counts|index:index }}</div>
                        </div>
                        {% endwith %}
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 