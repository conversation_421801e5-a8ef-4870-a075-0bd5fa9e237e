/* Global Styles */
:root {
    --primary-color: #2ecc71;
    --secondary-color: #27ae60;
    --accent-color: #f1c40f;
    --text-color: #2c3e50;
    --light-gray: #f8f9fa;
    --border-radius: 8px;
    --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

body {
    color: var(--text-color);
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    line-height: 1.6;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Cards */
.card {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
}

/* Product Cards */
.product-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card .card-body {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.product-card .card-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.product-card .price {
    color: var(--primary-color);
    font-size: 1.25rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    padding: 0.75rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(46, 204, 113, 0.25);
}

/* Navigation */
.navbar {
    padding: 1rem 0;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem !important;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

/* Cart Badge */
.cart-count {
    position: relative;
    top: -8px;
    right: 5px;
    font-size: 0.75rem;
}

/* Footer */
footer {
    background-color: var(--light-gray);
    padding: 4rem 0 2rem;
}

footer h5 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

footer ul li {
    margin-bottom: 0.5rem;
}

footer a {
    color: var(--text-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--primary-color);
}

.social-links a {
    font-size: 1.5rem;
    margin-right: 1rem;
    transition: transform 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .card-img-top {
        height: 150px;
    }
    
    footer {
        padding: 2rem 0 1rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .navbar-nav {
    padding-right: 0;
}

[dir="rtl"] .dropdown-menu {
    text-align: right;
}

[dir="rtl"] .ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

[dir="rtl"] .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

[dir="rtl"] .ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .ms-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
}

[dir="rtl"] .me-3 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
}

[dir="rtl"] .float-end {
    float: left !important;
}

[dir="rtl"] .float-start {
    float: right !important;
}

[dir="rtl"] .text-end {
    text-align: left !important;
}

[dir="rtl"] .text-start {
    text-align: right !important;
}

[dir="rtl"] .carousel-control-prev {
    right: 0;
    left: auto;
}

[dir="rtl"] .carousel-control-next {
    left: 0;
    right: auto;
}

[dir="rtl"] .fa-arrow-right:before {
    content: "\f060";
}

[dir="rtl"] .fa-arrow-left:before {
    content: "\f061";
} 