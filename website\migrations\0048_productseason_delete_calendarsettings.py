# Generated by Django 4.2.20 on 2025-06-30 11:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0047_alter_carouselimage_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductSeason',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Product Name')),
                ('name_ar', models.CharField(blank=True, max_length=100, null=True, verbose_name='Arabic Product Name')),
                ('start_month', models.PositiveIntegerField(choices=[(1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'), (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'), (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')], verbose_name='From Month')),
                ('end_month', models.PositiveIntegerField(choices=[(1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'), (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'), (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')], verbose_name='To Month')),
                ('description', models.TextField(blank=True, null=True)),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('order', models.PositiveIntegerField(default=0)),
            ],
            options={
                'verbose_name': 'Product Season',
                'verbose_name_plural': 'Product Seasons',
                'ordering': ['order', 'start_month', 'name'],
            },
        ),
        migrations.DeleteModel(
            name='CalendarSettings',
        ),
    ]
