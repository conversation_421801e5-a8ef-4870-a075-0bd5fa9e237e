# Generated by Django 4.2.20 on 2025-07-20 22:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0058_remove_excategory_products_exproduct_categories'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='excategory',
            options={'verbose_name_plural': 'External Categories'},
        ),
        migrations.RemoveField(
            model_name='excategory',
            name='color',
        ),
        migrations.RemoveField(
            model_name='excategory',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='excategory',
            name='description',
        ),
        migrations.RemoveField(
            model_name='excategory',
            name='is_active',
        ),
        migrations.RemoveField(
            model_name='excategory',
            name='updated_at',
        ),
        migrations.RemoveField(
            model_name='exproduct',
            name='categories',
        ),
        migrations.AddField(
            model_name='excategory',
            name='products',
            field=models.ManyToManyField(related_name='categories', through='website.ExCategoryProduct', to='website.exproduct'),
        ),
        migrations.AlterField(
            model_name='excategory',
            name='name',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='exproduct',
            name='varieties',
            field=models.ManyToManyField(related_name='products', through='website.ExProductVariety', to='website.exvariety'),
        ),
    ]
