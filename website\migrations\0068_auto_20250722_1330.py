# Generated by Django 4.2.20 on 2025-07-22 13:30

from django.db import migrations


def cleanup_orphaned_data(apps, schema_editor):
    """Clean up orphaned data before deleting models"""
    db_alias = schema_editor.connection.alias

    # Use raw SQL to clean up the problematic tables
    with schema_editor.connection.cursor() as cursor:
        try:
            # Delete from the many-to-many through table that's causing issues
            cursor.execute("DELETE FROM website_exproduct_varieties")
            print("Deleted all records from website_exproduct_varieties")

            # Delete from other related tables
            cursor.execute("DELETE FROM website_excategoryproduct")
            print("Deleted all records from website_excategoryproduct")

            cursor.execute("DELETE FROM website_exproductvariety")
            print("Deleted all records from website_exproductvariety")

        except Exception as e:
            print(f"Error during SQL cleanup: {e}")
            # Continue anyway

    # Also try using the ORM if models exist
    try:
        ExProductVariety = apps.get_model('website', 'ExProductVariety')
        ExCategoryProduct = apps.get_model('website', 'ExCategoryProduct')

        # Delete all ExProductVariety records (they're causing the foreign key constraint)
        ExProductVariety.objects.using(db_alias).all().delete()
        print("Deleted all ExProductVariety records via ORM")

        # Delete all ExCategoryProduct records
        ExCategoryProduct.objects.using(db_alias).all().delete()
        print("Deleted all ExCategoryProduct records via ORM")

    except Exception as e:
        print(f"Error during ORM cleanup: {e}")
        # Continue anyway, the models might not exist


def reverse_cleanup(apps, schema_editor):
    """Reverse operation - nothing to do"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0067_newvariety_created_at_newvariety_is_active_and_more'),
    ]

    operations = [
        migrations.RunPython(cleanup_orphaned_data, reverse_cleanup),
    ]
