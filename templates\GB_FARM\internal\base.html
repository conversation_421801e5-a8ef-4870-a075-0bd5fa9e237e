<!DOCTYPE html>
{% load static %}
{% load i18n %}
<html lang="{{ request.LANGUAGE_CODE }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}GB Farms Admin{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Custom Admin CSS -->
    <link rel="stylesheet" href="{% static 'css/admin-style.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-light">
    <!-- Admin Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'GB_FARM:admin_dashboard' %}">
                <img src="{% static 'img/logo.svg' %}" alt="GB Farms" height="30" class="d-inline-block align-top">
                <span class="ms-2">Admin Panel</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#adminNavbar">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="adminNavbar">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'admin_dashboard' %}active{% endif %}" 
                           href="{% url 'GB_FARM:admin_dashboard' %}">
                            <i class="bi bi-speedometer2 me-1"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'admin_order' in request.resolver_match.url_name %}active{% endif %}" 
                           href="{% url 'GB_FARM:admin_order_dashboard' %}">
                            <i class="bi bi-bag me-1"></i> Orders
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'admin_product' in request.resolver_match.url_name %}active{% endif %}" 
                           href="{% url 'GB_FARM:admin_product_dashboard' %}">
                            <i class="bi bi-box me-1"></i> Products
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'admin_customer' in request.resolver_match.url_name %}active{% endif %}" 
                           href="{% url 'GB_FARM:admin_customer_dashboard' %}">
                            <i class="bi bi-people me-1"></i> Customers
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'admin_career' in request.resolver_match.url_name %}active{% endif %}" 
                           href="{% url 'GB_FARM:admin_career_dashboard' %}">
                            <i class="bi bi-briefcase me-1"></i> Careers
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'admin_analytics_dashboard' %}active{% endif %}" 
                           href="{% url 'GB_FARM:admin_analytics_dashboard' %}">
                            <i class="bi bi-graph-up me-1"></i> Analytics
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'GB_FARM:home' %}">
                            <i class="bi bi-house me-1"></i> Visit Site
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i> {{ request.user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{% url 'GB_FARM:profile' %}">My Profile</a></li>
                            <li><a class="dropdown-item" href="{% url 'GB_FARM:edit_profile' %}">Edit Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'GB_FARM:logoutuser' %}">Logout</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-globe me-1"></i> {{ current_language|upper }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            {% for lang_code, lang_name in available_languages %}
                            <li>
                                <form action="{% url 'set_language' %}" method="post" class="d-inline">
                                    {% csrf_token %}
                                    <input type="hidden" name="language" value="{{ lang_code }}">
                                    <input type="hidden" name="next" value="{{ request.path }}">
                                    <button type="submit" class="dropdown-item {% if current_language == lang_code %}active{% endif %}">
                                        {{ lang_name }}
                                    </button>
                                </form>
                            </li>
                            {% endfor %}
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="container-fluid py-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-2 d-none d-lg-block">
                <div class="list-group mb-4">
                    <a href="{% url 'GB_FARM:admin_dashboard' %}" 
                       class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'admin_dashboard' %}active{% endif %}">
                        <i class="bi bi-speedometer2 me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'GB_FARM:admin_order_dashboard' %}" 
                       class="list-group-item list-group-item-action {% if 'admin_order' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="bi bi-bag me-2"></i> Orders
                    </a>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" 
                       class="list-group-item list-group-item-action {% if 'admin_product' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="bi bi-box me-2"></i> Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" 
                       class="list-group-item list-group-item-action {% if 'admin_customer' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="bi bi-people me-2"></i> Customers
                    </a>
                    <a href="{% url 'GB_FARM:admin_career_dashboard' %}" 
                       class="list-group-item list-group-item-action {% if 'admin_career' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="bi bi-briefcase me-2"></i> Careers
                    </a>
                    <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" 
                       class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'admin_analytics_dashboard' %}active{% endif %}">
                        <i class="bi bi-graph-up me-2"></i> Analytics
                    </a>
                    <a href="{% url 'GB_FARM:admin_zone_list' %}" 
                       class="list-group-item list-group-item-action {% if 'admin_zone' in request.resolver_match.url_name %}active{% endif %}">
                        <i class="bi bi-geo-alt me-2"></i> Delivery Zones
                    </a>
                    <a href="{% url 'GB_FARM:admin_invoice_logs' %}" 
                       class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'admin_invoice_logs' %}active{% endif %}">
                        <i class="bi bi-receipt me-2"></i> Invoice Logs
                    </a>
                </div>
            </div>
            
            <!-- Main Content Area -->
            <div class="col-lg-10">
                <!-- Alerts -->
                {% if messages %}
                <div class="messages mb-4">
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
                
                <!-- Page Title -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h2 mb-0">{% block page_title %}Dashboard{% endblock %}</h1>
                    <div class="page-actions">
                        {% block page_actions %}{% endblock %}
                    </div>
                </div>
                
                <!-- Page Content -->
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; {% now "Y" %} GB Farms. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>Admin Interface</p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html> 