# Generated by Django 4.2.20 on 2025-07-05 19:42

from django.db import migrations, models
import imagekit.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0052_alter_corporatesocial_options_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='corporatesocial',
            options={'verbose_name': 'Corporate Social Responsibility', 'verbose_name_plural': 'Corporate Social Responsibility'},
        ),
        migrations.AlterField(
            model_name='corporatesocial',
            name='description_part1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='corporatesocial',
            name='description_part2',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='corporatesocial',
            name='image',
            field=imagekit.models.fields.ProcessedImageField(blank=True, null=True, upload_to='corporate_social/'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='corporatesocial',
            name='subtitle',
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='corporatesocial',
            name='title',
            field=models.CharField(max_length=200),
        ),
    ]
