// Color Picker Widget JavaScript
(function() {
    'use strict';
    
    // Initialize color widgets when DOM is ready
    function initColorWidgets() {
        console.log('Initializing color widgets...');
        
        // Find all color widgets
        const colorWidgets = document.querySelectorAll('[id^="colorwidget_"]');
        console.log('Found', colorWidgets.length, 'color widgets');
        
        colorWidgets.forEach(function(widget) {
            const widgetId = widget.id;
            const colorPicker = widget.querySelector('[id^="color_"]');
            const textInput = widget.querySelector('[id^="text_"]');
            const preview = widget.querySelector('[id^="preview_"]');
            
            if (!colorPicker || !textInput || !preview) {
                console.log('Missing elements for widget:', widgetId);
                return;
            }
            
            console.log('Initializing widget:', widgetId);
            
            function updateColor(color) {
                if (color && color.match(/^#[0-9A-F]{6}$/i)) {
                    colorPicker.value = color;
                    textInput.value = color;
                    preview.style.backgroundColor = color;
                    console.log('Updated color to:', color);
                }
            }
            
            // Event listeners
            colorPicker.addEventListener('change', function() {
                updateColor(this.value);
            });
            
            colorPicker.addEventListener('input', function() {
                updateColor(this.value);
            });
            
            textInput.addEventListener('input', function() {
                updateColor(this.value);
            });
            
            textInput.addEventListener('blur', function() {
                updateColor(this.value);
            });
            
            // Initialize with current value
            const currentValue = textInput.value || '#ef4444';
            updateColor(currentValue);
            
            // Handle preset buttons
            const presetButtons = widget.querySelectorAll('button[onclick*="setColorFor"]');
            presetButtons.forEach(function(button) {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const onclick = this.getAttribute('onclick');
                    const colorMatch = onclick.match(/'(#[0-9A-F]{6})'/i);
                    if (colorMatch) {
                        updateColor(colorMatch[1]);
                    }
                });
            });
        });
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initColorWidgets);
    } else {
        initColorWidgets();
    }
    
    // Also initialize when Django admin loads content dynamically
    if (typeof django !== 'undefined' && django.jQuery) {
        django.jQuery(document).ready(function() {
            setTimeout(initColorWidgets, 100);
        });
    }
    
})();
