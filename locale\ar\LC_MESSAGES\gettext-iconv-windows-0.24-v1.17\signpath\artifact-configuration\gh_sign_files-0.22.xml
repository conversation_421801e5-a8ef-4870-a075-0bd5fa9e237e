<?xml version="1.0" encoding="utf-8"?>
<artifact-configuration
    xmlns="http://signpath.io/artifact-configuration/v1"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://signpath.io/artifact-configuration/v1 https://app.signpath.io/Web/artifact-configuration/v1.xsd"
>
    <parameters>
        <parameter name="iconvPEVersion" required="true" />
        <parameter name="gettextPEVersion" required="true" />
        <parameter name="gettextPENameLibGettextLib" required="false" />
        <parameter name="gettextPEVersionLibGettextLib" required="false" />
        <parameter name="gettextPENameLibGettextSrc" required="false" />
        <parameter name="gettextPEVersionLibGettextSrc" required="false" />
        <parameter name="gettextPEVersionLibIntl" required="true" />
        <parameter name="gettextPEVersionLibTextStyle" required="true" />
    </parameters>
    <zip-file>
        <pe-file-set>
            <!-- iconv -->
            <include path="bin/iconv.exe" min-matches="1" max-matches="1" product-name="iconv: character set conversion program" product-version="${iconvPEVersion}" />
            <include path="bin/libiconv-*.dll" min-matches="0" max-matches="1" product-name="libiconv: character set conversion library" product-version="${iconvPEVersion}" />
            <!-- gettext -->
            <include path="bin/envsubst.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/gettext.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/libintl-*.dll" min-matches="0" max-matches="1" product-name="GNU libintl: accessing NLS message catalogs" product-version="${gettextPEVersionLibIntl}" />
            <include path="bin/libtextstyle-*.dll" min-matches="0" max-matches="1" product-name="GNU libtextstyle: Text styling library" product-version="${gettextPEVersionLibTextStyle}" />
            <include path="bin/msgattrib.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/msgcat.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/msgcmp.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/msgcomm.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/msgconv.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/msgen.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/msgexec.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/msgfilter.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/msgfmt.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/msggrep.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/msginit.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/msgmerge.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/msgunfmt.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/msguniq.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/ngettext.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/recode-sr-latin.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="bin/xgettext.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="lib/gettext/cldr-plurals.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="lib/gettext/hostname.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <include path="lib/gettext/urlget.exe" min-matches="1" max-matches="1" product-name="GNU gettext utilities" product-version="${gettextPEVersion}" />
            <!-- Missing metadata: see https://savannah.gnu.org/bugs/?66267
            <include path="bin/libgettextlib-*.dll" min-matches="0" max-matches="1" product-name="${gettextPENameLibGettextLib}" product-version="${gettextPEVersionLibGettextLib}" />
            <include path="bin/libgettextsrc-*.dll" min-matches="0" max-matches="1" product-name="${gettextPENameLibGettextSrc}" product-version="${gettextPEVersionLibGettextSrc}" />
            -->
            <!-- Missing metadata: see https://lists.gnu.org/archive/html/bug-gettext/2024-10/msg00058.html
            <include path="lib/gettext/GNU.Gettext.dll" min-matches="0" max-matches="1" product-name="" product-version="0.0.0.0" />
            <include path="lib/gettext/msgfmt.net.exe" min-matches="0" max-matches="1" product-name="" product-version="0.0.0.0" />
            <include path="lib/gettext/msgunfmt.net.exe" min-matches="0" max-matches="1" product-name="" product-version="0.0.0.0" />
            <include path="lib/GNU.Gettext.dll" min-matches="0" max-matches="1" product-name="" product-version="0.0.0.0" />
            -->
            <for-each>
                <authenticode-sign />
            </for-each>
        </pe-file-set>
    </zip-file>
</artifact-configuration>