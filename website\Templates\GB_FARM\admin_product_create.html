{% extends 'GB_FARM/admin_base.html' %}

{% block title %}Add New Product{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Admin Dashboard</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:admin_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i> Orders
                    </a>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-box me-2"></i> Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i> Customers
                    </a>
                    <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Add New Product</h5>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Products
                    </a>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        {% if messages %}
                        <div class="alert alert-danger">
                            {% for message in messages %}
                                {{ message }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <div class="row">
                            <!-- Basic Product Information -->
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Product Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">Select Category</option>
                                        {% for category in categories %}
                                            <option value="{{ category.id }}">{{ category.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="price" class="form-label">Price (EGP) *</label>
                                            <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="stock" class="form-label">Stock</label>
                                            <input type="number" class="form-control" id="stock" name="stock" min="0" value="0">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="unit_category" class="form-label">Unit Category *</label>
                                            <select class="form-select" id="unit_category" name="unit_category" required>
                                                <option value="PIECE">Piece</option>
                                                <option value="FREE WEIGHT">Free Weight</option>
                                                <option value="KILOGRAM">Kilogram</option>
                                                <option value="LITER">Liter</option>
                                                <option value="500G">500g</option>
                                                <option value="250G">250g</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="unit" class="form-label">Unit</label>
                                            <select class="form-select" id="unit" name="unit">
                                                {% for unit_code, unit_name in unit_choices %}
                                                    <option value="{{ unit_code }}">{{ unit_name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="4"></textarea>
                                </div>
                            </div>
                            
                            <!-- Product Media (Image/Video) -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Media Type</label>
                                    <div class="btn-group w-100 mb-3" role="group">
                                        <input type="radio" class="btn-check" name="media_type" id="media_type_image" value="image" checked>
                                        <label class="btn btn-outline-primary" for="media_type_image">Image</label>
                                        
                                        <input type="radio" class="btn-check" name="media_type" id="media_type_video" value="video">
                                        <label class="btn btn-outline-primary" for="media_type_video">Video</label>
                                    </div>
                                    
                                    <div id="image_upload_section">
                                        <label for="image" class="form-label">Product Image</label>
                                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                        <div class="mt-3" id="imagePreview">
                                            <div class="bg-light text-center p-5 rounded">
                                                <i class="fas fa-image text-muted fa-3x"></i>
                                                <p class="text-muted mt-2">No image selected</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div id="video_upload_section" style="display: none;">
                                        <label for="video" class="form-label">Product Video</label>
                                        <input type="file" class="form-control" id="video" name="video" accept="video/*">
                                        <div class="mt-3" id="videoPreview">
                                            <div class="bg-light text-center p-5 rounded">
                                                <i class="fas fa-video text-muted fa-3x"></i>
                                                <p class="text-muted mt-2">No video selected</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-end">
                            <button type="reset" class="btn btn-outline-secondary me-2">Reset</button>
                            <button type="submit" class="btn btn-primary">Create Product</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    // Media type toggle functionality
    document.getElementById('media_type_image').addEventListener('change', function() {
        if (this.checked) {
            document.getElementById('image_upload_section').style.display = 'block';
            document.getElementById('video_upload_section').style.display = 'none';
        }
    });
    
    document.getElementById('media_type_video').addEventListener('change', function() {
        if (this.checked) {
            document.getElementById('image_upload_section').style.display = 'none';
            document.getElementById('video_upload_section').style.display = 'block';
        }
    });

    // Unit category and unit sync functionality
    document.getElementById('unit_category').addEventListener('change', function() {
        const unitSelect = document.getElementById('unit');
        const unitCategory = this.value;
        
        // Clear existing options
        unitSelect.innerHTML = '';
        
        // Add appropriate options based on category
        if (unitCategory === 'FREE WEIGHT') {
            const freeWeightOptions = [
                { value: 'kg', label: 'Kilogram (kg)' },
                { value: '500g', label: '500 grams' },
                { value: '250g', label: '250 grams' }
            ];
            
            freeWeightOptions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                unitSelect.appendChild(optionElement);
            });
        } else if (unitCategory === 'KILOGRAM') {
            const kilogramOptions = [
                { value: 'kg', label: 'Kilogram (kg)' }
            ];
            
            kilogramOptions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                unitSelect.appendChild(optionElement);
            });
        } else if (unitCategory === 'LITER') {
            const literOptions = [
                { value: 'liter', label: 'Liter (L)' }
            ];
            
            literOptions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                unitSelect.appendChild(optionElement);
            });
        } else if (unitCategory === '500G') {
            const option500gOptions = [
                { value: '500g', label: '500 grams' }
            ];
            
            option500gOptions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                unitSelect.appendChild(optionElement);
            });
        } else if (unitCategory === '250G') {
            const option250gOptions = [
                { value: '250g', label: '250 grams' }
            ];
            
            option250gOptions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                unitSelect.appendChild(optionElement);
            });
        } else {
            // PIECE category
            const pieceOptions = [
                { value: 'piece', label: 'Piece' }
            ];
            
            pieceOptions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.label;
                unitSelect.appendChild(optionElement);
            });
        }
    });
    
    // Trigger unit update on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Populate unit dropdown based on initial unit category
        document.getElementById('unit_category').dispatchEvent(new Event('change'));
    });

    // Image preview functionality
    document.getElementById('image').addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('imagePreview').innerHTML = `
                    <img src="${e.target.result}" class="img-fluid rounded" alt="Product image preview">
                `;
            }
            reader.readAsDataURL(file);
        }
    });
    
    // Video preview functionality
    document.getElementById('video').addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('videoPreview').innerHTML = `
                    <video controls class="img-fluid rounded">
                        <source src="${e.target.result}" type="${file.type}">
                        Your browser does not support the video tag.
                    </video>
                `;
            }
            reader.readAsDataURL(file);
        }
    });
</script>
{% endblock %}
{% endblock %} 