{% extends 'GB_FARM/base.html' %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card mt-5">
                <div class="card-header">
                    {% if page == 'login' %}
                        <h3 class="text-center">Login</h3>
                    {% else %}
                        <h3 class="text-center">Register</h3>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %}" role="alert">
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}

                    {% if page == 'login' %}
                        <form method="POST" action="">
                            {% csrf_token %}
                            <input type="hidden" name="next" value="{{ next }}">
                            <div class="form-group mb-3">
                                <label for="username">Username</label>
                                <input type="text" name="username" class="form-control" placeholder="Enter username" autocomplete="username">
                            </div>
                            <div class="form-group mb-3">
                                <label for="password">Password</label>
                                <input type="password" name="password" class="form-control" placeholder="Enter password" autocomplete="current-password">
                            </div>
                            <button type="submit" class="btn btn-primary btn-block">Login</button>
                        </form>
                        <p class="mt-3 text-center">Don't have an account? <a href="{% url 'GB_FARM:registerpage' %}?next={{ next|urlencode }}">Register</a></p>
                    {% else %}
                        <form method="POST" action="">
                            {% csrf_token %}
                            <input type="hidden" name="next" value="{{ next }}">
                            {% for field in form %}
                                <div class="form-group mb-3">
                                    <label for="{{ field.id_for_label }}">{{ field.label }}</label>
                                    {{ field }}
                                    {% if field.errors %}
                                        <div class="text-secondary">
                                            {{ field.errors }}
                                        </div>
                                    {% endif %}
                                </div>
                            {% endfor %}
                            <button type="submit" class="btn btn-primary btn-block">Register</button>
                        </form>
                        <p class="mt-3 text-center">Already have an account? <a href="{% url 'GB_FARM:loginpage' %}?next={{ next|urlencode }}">Login</a></p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 