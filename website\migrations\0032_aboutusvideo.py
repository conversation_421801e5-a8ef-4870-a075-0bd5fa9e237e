# Generated by Django 4.2.20 on 2025-06-23 15:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0031_alter_countryflag_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AboutUsVideo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='About Us', max_length=200)),
                ('title_ar', models.CharField(blank=True, max_length=200, null=True, verbose_name='Arabic Title')),
                ('description', models.TextField(default='GB Farms was established in 1991 by <PERSON><PERSON>, founder and CEO of GB Auto, a publicly traded company leading the MENA region in automotive manufacturing and distribution, and financial solutions with a workforce of more than 23,000 employees.')),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Arabic Description')),
                ('video', models.<PERSON>Field(blank=True, help_text='Upload video file for About Us section', null=True, upload_to='website_videos/')),
                ('background_color', models.Char<PERSON>ield(default='#d4e10a', help_text='Background color for the text overlay (hex color code)', max_length=7)),
                ('background_opacity', models.FloatField(default=0.85, help_text='Background opacity (0.0 to 1.0)')),
                ('text_color', models.CharField(default='#166534', help_text='Text color (hex color code)', max_length=7)),
                ('read_more_link', models.URLField(blank=True, help_text="URL for 'Read More' link", null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'About Us Video Section',
                'verbose_name_plural': 'About Us Video Section',
            },
        ),
    ]
