# Generated by Django 4.2.20 on 2025-06-25 09:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0034_add_contact_form_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='GrapeVariety',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('color', models.CharField(choices=[('white', 'White Seedless'), ('red', 'Red Seedless'), ('black', 'Black Seedless')], max_length=10)),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Grape Variety',
                'verbose_name_plural': 'Grape Varieties',
                'ordering': ['color', 'order', 'name'],
            },
        ),
    ]
