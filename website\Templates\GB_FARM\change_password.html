{% extends 'GB_FARM/base.html' %}

{% block title %}Change Password{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Account Menu</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:profile' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-person-circle me-2"></i> My Profile
                    </a>
                    <a href="{% url 'GB_FARM:edit_profile' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-pencil-square me-2"></i> Edit Profile
                    </a>
                    <a href="{% url 'GB_FARM:change_password' %}" class="list-group-item list-group-item-action active">
                        <i class="bi bi-key me-2"></i> Change Password
                    </a>
                    <a href="{% url 'GB_FARM:order_history' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-bag me-2"></i> Order History
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Change Password</h5>
                </div>
                <div class="card-body">
                    {% if messages %}
                    <div class="messages mb-4">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            {{ message }}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <div class="alert alert-info mb-4">
                        <i class="bi bi-info-circle me-2"></i>
                        Please enter your current password to verify your identity, then enter and confirm your new password.
                    </div>
                    
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <label for="current_password" class="form-label">Current Password <span class="text-secondary">*</span></label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                            <div class="invalid-feedback">
                                Please enter your current password.
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="new_password" class="form-label">New Password <span class="text-secondary">*</span></label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required 
                                   minlength="8" aria-describedby="passwordHelp">
                            <div class="form-text" id="passwordHelp">
                                Your password must be at least 8 characters long and include a mix of letters, numbers, and special characters.
                            </div>
                            <div class="invalid-feedback">
                                Please enter a valid password (minimum 8 characters).
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="confirm_password" class="form-label">Confirm New Password <span class="text-secondary">*</span></label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div class="invalid-feedback">
                                Please confirm your new password.
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'GB_FARM:profile' %}" class="btn btn-outline-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Change Password</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Form validation script
    (function () {
        'use strict'
        
        // Fetch all the forms we want to apply custom Bootstrap validation styles to
        var forms = document.querySelectorAll('.needs-validation')
        
        // Loop over them and prevent submission
        Array.prototype.slice.call(forms)
            .forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    // Check if passwords match
                    const newPassword = document.getElementById('new_password').value;
                    const confirmPassword = document.getElementById('confirm_password').value;
                    
                    if (newPassword !== confirmPassword) {
                        event.preventDefault();
                        event.stopPropagation();
                        document.getElementById('confirm_password').setCustomValidity('Passwords do not match');
                    } else {
                        document.getElementById('confirm_password').setCustomValidity('');
                    }
                    
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    
                    form.classList.add('was-validated');
                }, false);
                
                // Clear custom validity when typing in confirm password field
                document.getElementById('confirm_password').addEventListener('input', function() {
                    this.setCustomValidity('');
                });
            });
    })();
</script>
{% endblock %} 