from celery import shared_task
from django.core.mail import send_mail
from django.conf import settings
from django.core.cache import cache
from .models import Order, Product, User
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

@shared_task
def send_order_confirmation_email(order_id):
    try:
        order = Order.objects.get(id=order_id)
        user = order.user
        
        subject = f'Order Confirmation - Order #{order.id}'
        message = f'''
        Dear {user.username},
        
        Thank you for your order! Your order has been confirmed and is being processed.
        
        Order Details:
        Order ID: {order.id}
        Total Amount: {order.total_amount} EGP
        Status: {order.get_status_display()}
        
        We'll keep you updated on your order status.
        
        Best regards,
        GB Farm Team
        '''
        
        send_mail(
            subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            [user.email],
            fail_silently=False,
        )
        
        logger.info(f"Order confirmation email sent for order #{order_id}")
    except Exception as e:
        logger.error(f"Failed to send order confirmation email for order #{order_id}: {str(e)}")
        raise

@shared_task
def update_product_stock():
    try:
        # Update stock for products with low inventory
        low_stock_products = Product.objects.filter(stock__lte=10)
        for product in low_stock_products:
            # Here you would typically integrate with your inventory management system
            # For now, we'll just log it
            logger.warning(f"Low stock alert for product: {product.name} (ID: {product.id})")
            
        # Clear product cache
        cache.delete('product_list')
        logger.info("Product stock update completed")
    except Exception as e:
        logger.error(f"Failed to update product stock: {str(e)}")
        raise

@shared_task
def cleanup_expired_offers():
    try:
        # Find and deactivate expired offers
        expired_offers = Offer.objects.filter(
            is_active=True,
            valid_to__lt=datetime.now()
        )
        
        for offer in expired_offers:
            offer.is_active = False
            offer.save()
            logger.info(f"Deactivated expired offer for product: {offer.product.name}")
            
        # Clear product cache
        cache.delete('product_list')
        logger.info("Offer cleanup completed")
    except Exception as e:
        logger.error(f"Failed to cleanup expired offers: {str(e)}")
        raise

@shared_task
def generate_daily_sales_report():
    try:
        yesterday = datetime.now() - timedelta(days=1)
        orders = Order.objects.filter(
            created_at__date=yesterday.date(),
            status='delivered'
        )
        
        total_sales = sum(order.total_amount for order in orders)
        total_orders = orders.count()
        
        # Send report to admin
        admin_users = User.objects.filter(is_staff=True)
        for admin in admin_users:
            subject = f'Daily Sales Report - {yesterday.date()}'
            message = f'''
            Daily Sales Report for {yesterday.date()}
            
            Total Orders: {total_orders}
            Total Sales: {total_sales} EGP
            
            Best regards,
            GB Farm System
            '''
            
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [admin.email],
                fail_silently=False,
            )
            
        logger.info(f"Daily sales report generated for {yesterday.date()}")
    except Exception as e:
        logger.error(f"Failed to generate daily sales report: {str(e)}")
        raise 