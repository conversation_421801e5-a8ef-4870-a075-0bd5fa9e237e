from django import template
from website.models import Cart

register = template.Library()

@register.filter
def lookup(dictionary, key):
    """Template filter to lookup a key in a dictionary"""
    if isinstance(dictionary, dict):
        return dictionary.get(key, [])
    return []

@register.filter
def multiply(value, arg):
    """Multiply the value by the argument"""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def cart_count(user):
    """Get the number of items in the user's cart"""
    if user.is_authenticated:
        return Cart.objects.filter(user=user).count()
    return 0

@register.filter
def divide(value, arg):
    """Divide the value by the argument"""
    try:
        return float(value) / float(arg)
    except (ValueError, ZeroDivisionError):
        return 0

@register.filter
def subtract(value, arg):
    """Subtracts the argument from the value."""
    return value - arg