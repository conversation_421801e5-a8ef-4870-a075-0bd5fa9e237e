{% extends 'GB_FARM/admin_base.html' %}

{% block title %}Delete Product{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Admin Dashboard</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:admin_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i> Orders
                    </a>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-box me-2"></i> Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i> Customers
                    </a>
                    <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Search and Filter Bar -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <form method="get" action="{% url 'GB_FARM:admin_product_dashboard' %}" class="row g-3 admin-search-filter">
                        <div class="col-md-4">
                            <label for="category_filter" class="form-label">Category</label>
                            <select class="form-select" id="category_filter" name="category">
                                <option value="">All Categories</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="status_filter" class="form-label">Status</label>
                            <select class="form-select" id="status_filter" name="status">
                                <option value="">All Statuses</option>
                                <option value="available">Available</option>
                                <option value="unavailable">Unavailable</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" placeholder="">
                        </div>
                        <div class="col-12 text-end">
                            <button type="submit" class="btn btn-primary">Apply Filters</button>
                            <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="btn btn-outline-secondary">Reset</a>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">Confirm Delete</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">Warning!</h5>
                        <p>Are you sure you want to delete the product <strong>"{{ product.name }}"</strong>?</p>
                        <hr>
                        <p class="mb-0">If this product has any associated orders, it will be marked as unavailable instead of being deleted.</p>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-5">
                            {% if product.image %}
                                <img src="{{ product.image.url }}" class="img-fluid rounded" alt="{{ product.name }}">
                            {% else %}
                                <div class="bg-light text-center p-5 rounded">
                                    <i class="fas fa-image text-muted fa-3x"></i>
                                    <p class="text-muted mt-2">No image available</p>
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-7">
                            <h4>{{ product.name }}</h4>
                            <p class="text-muted">
                                {% if product.category %}
                                Category: {{ product.category.name }}
                                {% endif %}
                            </p>
                            <p>Price: EGP {{ product.price|floatformat:2 }}</p>
                            <p>Stock: {{ product.stock }}</p>
                            <p>Unit Category: {{ product.get_unit_category_display }}</p>
                            <p>Available Units: {{ product.get_available_units_display|join:", " }}</p>
                            <p>Status: {{ product.get_status_display }}</p>
                            
                            {% if product.description %}
                            <div class="mt-3">
                                <h6>Description:</h6>
                                <p>{{ product.description }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-end">
                        <a href="{% url 'GB_FARM:admin_product_edit' product_id=product.id %}" class="btn btn-outline-secondary me-2">Cancel</a>
                        <form method="post">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-danger">Confirm Delete</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 