{% extends 'GB_FARM/base.html' %}
{% load static %}

{% block title %}{% if product %}Edit{% else %}Create{% endif %} Product{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Admin Dashboard</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:admin_order_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i> Orders
                    </a>
                    <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-box me-2"></i> Products
                    </a>
                    <a href="{% url 'GB_FARM:admin_customer_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i> Customers
                    </a>
                    <a href="{% url 'GB_FARM:admin_analytics_dashboard' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'GB_FARM:admin_product_dashboard' %}">Products</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{% if product %}Edit{% else %}Create{% endif %} Product</li>
                </ol>
            </nav>

            <!-- Search and Filter Bar -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <form method="get" action="{% url 'GB_FARM:admin_product_dashboard' %}" class="row g-3 admin-search-filter">
                        <div class="col-md-4">
                            <label for="category_filter" class="form-label">Category</label>
                            <select class="form-select" id="category_filter" name="category">
                                <option value="">All Categories</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="status_filter" class="form-label">Status</label>
                            <select class="form-select" id="status_filter" name="status">
                                <option value="">All Statuses</option>
                                <option value="available">Available</option>
                                <option value="unavailable">Unavailable</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" placeholder="">
                        </div>
                        <div class="col-12 text-end">
                            <button type="submit" class="btn btn-primary">Apply Filters</button>
                            <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="btn btn-outline-secondary">Reset</a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Product Form -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">{% if product %}Edit{% else %}Create{% endif %} Product</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" action="{% if product %}{% url 'GB_FARM:admin_product_edit' product.id %}{% else %}{% url 'GB_FARM:admin_product_create' %}{% endif %}">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Product Name*</label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ product.name }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">Select a Category</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}" {% if product.category.id == category.id %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="price" class="form-label">Price (EGP)*</label>
                                <input type="number" step="0.01" min="0" class="form-control" id="price" name="price" value="{{ product.price }}" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="stock" class="form-label">Stock</label>
                                <input type="number" step="1" min="0" class="form-control" id="stock" name="stock" value="{{ product.stock|default:0 }}">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="unit_category" class="form-label">Unit Category*</label>
                                <select class="form-select" id="unit_category" name="unit_category" required>
                                    {% for value, display in unit_categories %}
                                        <option value="{{ value }}" {% if product.unit_category == value %}selected{% endif %}>{{ display }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-8 mb-3">
                                <label class="form-label">Available Units*</label>
                                <div id="available-units-container" class="border p-2 rounded" style="min-height: 50px;">
                                    <!-- Checkboxes will be loaded here by JavaScript -->
                                    <span class="text-muted">Select a Unit Category first.</span>
                                </div>
                                <div class="form-text">Select the units available for customers for the chosen category.</div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="available" {% if product.status == 'available' or not product.status %}selected{% endif %}>Available</option>
                                    <option value="unavailable" {% if product.status == 'unavailable' %}selected{% endif %}>Unavailable</option>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" {% if product.is_featured %}checked{% endif %}>
                                    <label class="form-check-label" for="is_featured">
                                        Featured Product
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="5">{{ product.description }}</textarea>
                        </div>
                        
                        <div class="mb-4">
                            <label for="image" class="form-label">Product Image</label>
                            {% if product.image %}
                            <div class="mb-2">
                                <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-thumbnail" style="max-height: 200px;">
                            </div>
                            {% endif %}
                            <input type="file" class="form-control" id="image" name="image" accept="image/*">
                            <div class="form-text">Maximum file size: 5MB. Recommended dimensions: 800x800px.</div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'GB_FARM:admin_product_dashboard' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Back to Products
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> {% if product %}Update{% else %}Create{% endif %} Product
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{# Moved JavaScript here, inside the content block #}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log("Admin product form JS loaded.");

        const unitCategorySelect = document.getElementById('unit_category');
        const availableUnitsContainer = document.getElementById('available-units-container');

        let allUnitChoices = {};
        let currentAvailableUnits = [];

        try {
            // Safely parse JSON data passed from the Django view
            const choicesDataElement = document.getElementById('all_unit_choices_data');
            const currentUnitsDataElement = document.getElementById('current_available_units_data');

            if (choicesDataElement) {
                 allUnitChoices = JSON.parse(choicesDataElement.textContent);
                 console.log("Parsed ALL_UNIT_CHOICES:", allUnitChoices);
            } else {
                console.error("Could not find all_unit_choices_data element.");
            }

            if (currentUnitsDataElement) {
                currentAvailableUnits = JSON.parse(currentUnitsDataElement.textContent);
                console.log("Parsed currentAvailableUnits:", currentAvailableUnits);
            } else {
                 console.warn("Could not find current_available_units_data element (expected on edit form).");
            }

        } catch (e) {
            console.error("Error parsing JSON data from template:", e);
            // Provide default empty data to prevent further errors
             allUnitChoices = {};
             currentAvailableUnits = [];
        }

        function updateAvailableUnits() {
            const selectedCategory = unitCategorySelect.value;
            console.log(`Unit Category changed to: ${selectedCategory}`);

            const unitsForCategory = allUnitChoices[selectedCategory] || [];
            console.log(`Units for ${selectedCategory}:`, unitsForCategory);

            availableUnitsContainer.innerHTML = ''; // Clear previous content

            if (unitsForCategory.length === 0) {
                availableUnitsContainer.innerHTML = '<span class="text-muted">No specific units defined for this category.</span>';
                return;
            }

            unitsForCategory.forEach(unit => {
                const value = unit[0];
                const display = unit[1];
                // Check if the current unit value exists in the initial list
                const isChecked = currentAvailableUnits.includes(value);

                const div = document.createElement('div');
                div.classList.add('form-check', 'form-check-inline');

                const input = document.createElement('input');
                input.classList.add('form-check-input');
                input.type = 'checkbox';
                input.name = 'available_units'; // Use the same name for all
                input.value = value;
                input.id = `unit-${value}`;
                input.checked = isChecked; // Set checked status based on initial data

                const label = document.createElement('label');
                label.classList.add('form-check-label');
                label.htmlFor = `unit-${value}`;
                label.textContent = display;

                div.appendChild(input);
                div.appendChild(label);
                availableUnitsContainer.appendChild(div);
            });
        }

        if (unitCategorySelect && availableUnitsContainer) {
            // Initial population of checkboxes
            updateAvailableUnits();

            // Add event listener for changes
            unitCategorySelect.addEventListener('change', updateAvailableUnits);
        } else {
            console.error("Required elements (unit_category select or available-units-container) not found.");
        }
    });
</script>

{# Add hidden elements to pass JSON data safely #}
<script id="all_unit_choices_data" type="application/json">
    {{ all_unit_choices_json|safe }}
</script>
<script id="current_available_units_data" type="application/json">
    {{ current_available_units_json|safe }}
</script>

{% endblock %} 