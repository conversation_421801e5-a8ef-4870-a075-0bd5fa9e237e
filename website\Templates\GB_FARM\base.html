<!DOCTYPE html>
{% load static %}
{% load i18n %}
{% load custom_filters %}
<html lang="{{ LANGUAGE_CODE }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- SEO Meta Tags -->
    {% block meta_tags %}
    <meta name="description" content="{% block meta_description %}{% trans 'GB Farm - Premium organic fruits, vegetables, and honey—harvested fresh from our farm and delivered straight to your table for unparalleled quality and taste' %}{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}{% trans 'organic farming, fresh produce, fruits, vegetables, honey, farm products' %}{% endblock %}">
    {% endblock %}
    <meta name="author" content="GB Farm">
    
    <!-- Open Graph / Facebook -->
    {% block open_graph %}
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.build_absolute_uri }}">
    <meta property="og:title" content="{% block og_title %}{% endblock %} - GB Farms">
    <meta property="og:description" content="{% block og_description %}{% trans 'Premium organic fruits, vegetables, and honey—harvested fresh from our farm' %}{% endblock %}">
    <meta property="og:image" content="{% block og_image %}{% static 'logo.png' %}{% endblock %}">
    {% endblock %}
    
    <!-- Twitter -->
    {% block twitter_cards %}
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ request.build_absolute_uri }}">
    <meta property="twitter:title" content="{% block twitter_title %}{% endblock %} - GB Farms">
    <meta property="twitter:description" content="{% block twitter_description %}{% trans 'Premium organic fruits, vegetables, and honey—harvested fresh from our farm' %}{% endblock %}">
    <meta property="twitter:image" content="{% block twitter_image %}{% static 'logo.png' %}{% endblock %}">
    {% endblock %}
    
    <!-- Canonical URL -->
    <link rel="canonical" href="{% block canonical_url %}{{ request.build_absolute_uri }}{% endblock %}">
    
    <title>{% block title %}{% endblock %} - GB Farms</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{% static 'favicon.png' %}">
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    {% block extra_css %}{% endblock %}
    
    {% block extra_head %}{% endblock %}
    
    <style>
        :root {
            --primary-color: #28a745;
            --primary-color: #2ecc71;
            --secondary-color: #27ae60;
            --accent-color: #f1c40f;
            --text-color: #2c3e50;
            --light-bg: #f8f9fa;
            --dark-bg: #2c3e50;
            --transition-speed: 0.3s;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-color);
            overflow-x: hidden;
            position: relative;
            z-index: 1;
            background: transparent;
        }
        
        /* Navbar Styling */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            padding: 1rem 0;
        }
        
        .navbar.scrolled {
            padding: 0.5rem 0;
            background: rgba(255, 255, 255, 0.98) !important;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
            font-size: 1.5rem;
            transition: transform var(--transition-speed) ease;
            margin-left: -15px; /* Add negative margin to move logo left */
        }
        
        .navbar-brand:hover {
            transform: scale(1.05);
        }
        
        .navbar-brand img {
            transition: transform 0.3s ease;
        }
        
        .navbar-brand:hover img {
            transform: scale(1.05);
        }
        
        .nav-link {
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            margin: 0 0.2rem;
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            background: rgba(46, 204, 113, 0.1);
            color: var(--primary-color) !important;
        }
        
        /* Button Styling */
        .btn {
            border-radius: 25px;
            padding: 0.6rem 1.8rem;
            font-weight: 500;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            z-index: -1;
        }
        
        .btn:hover::before {
            width: 100%;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            border: none;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
        }
        
        .btn-outline-success {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-outline-success:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        /* Card Styling */
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all var(--transition-speed) ease;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .card-img-top {
            transition: transform var(--transition-speed) ease;
        }
        
        .card:hover .card-img-top {
            transform: scale(1.05);
        }
        
        /* Loading Spinner */
        .loading-spinner {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(5px);
            z-index: 9999;
        }
        
        .loading-spinner.active {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(46, 204, 113, 0.1);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Footer Styling */
        footer {
            background: green-950;
            backdrop-filter: blur(10px);
            padding: 5rem 0 2rem;
            position: relative;
            z-index: 1;
        }
        
        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(46, 204, 113, 0.3), transparent);
        }
        
        footer h5 {
            font-weight: 600;
            margin-bottom: 1.5rem;
            position: relative;
            padding-bottom: 0.8rem;
        }
        
        footer h5::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 50px;
            height: 2px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
        }
        
        footer a {
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            padding-left: 0;
        }
        
        footer a::before {
            content: '→';
            position: absolute;
            left: -20px;
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        footer a:hover {
            color: var(--primary-color);
            padding-left: 20px;
        }
        
        footer a:hover::before {
            opacity: 1;
            left: 0;
        }
        
        .social-links a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: rgba(46, 204, 113, 0.1);
            color: var(--primary-color);
            border-radius: 50%;
            margin-right: 10px;
            transition: all 0.3s ease;
        }
        
        .social-links a:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-3px);
        }
        
        /* Form Styling */
        .form-control {
            border-radius: 25px;
            padding: 0.8rem 1.5rem;
            border: 2px solid #eee;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(46, 204, 113, 0.15);
        }
        
        /* Badge Styling */
        .badge {
            padding: 0.5em 1em;
            border-radius: 20px;
            font-weight: 500;
        }
        
        /* Alert Styling */
        .alert {
            border-radius: 15px;
            border: none;
            padding: 1rem 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 10px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 5px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        /* Video Background Styles */
        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
            background-color: var(--dark-bg);
            display: block !important;
            visibility: visible !important;
        }

        .video-background video {
            position: absolute;
            top: 50%;
            left: 50%;
            min-width: 100%;
            min-height: 100%;
            width: auto;
            height: auto;
            transform: translateX(-50%) translateY(-50%);
            object-fit: cover;
            transition: opacity 0.5s ease;
            opacity: 1; /* Make video visible by default */
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--dark-bg);
            opacity: 0.1; /* Reduced opacity to match home.html */
            transition: opacity 0.3s ease;
        }

        /* Main Content Area */
        main {
            background: rgba(255, 255, 255, 0.45);
            backdrop-filter: blur(3px);
            border-radius: 20px;
            margin-top: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }

        /* Section Styling */
        section {
            background: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(3px);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* Welcome Section */
        .welcome-section {
            background: rgba(255, 255, 255, 0.45);
            backdrop-filter: blur(3px);
            border-radius: 20px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        /* Featured Products Section */
        .featured-products {
            background: rgba(255, 255, 255, 0.45);
            backdrop-filter: blur(3px);
            border-radius: 20px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        /* Why Choose Us Section */
        .why-choose-us {
            background: rgba(255, 255, 255, 0.45);
            backdrop-filter: blur(3px);
            border-radius: 20px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        /* Experience Badge */
        .experience-badge {
            background: rgba(46, 204, 113, 0.6);
            backdrop-filter: blur(3px);
            border-radius: 50%;
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            position: absolute;
            top: -20px;
            right: -20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* Feature Cards */
        .feature-card {
            background: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(3px);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.6);
        }

        /* Product Cards */
        .product-card {
            background: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(3px);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.6);
        }

        /* Text Enhancement for Better Readability */
        h1, h2, h3, h4, h5, h6 {
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
            color: rgba(44, 62, 80, 0.9);
        }

        p {
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            color: rgba(44, 62, 80, 0.9);
        }

        /* Video Overlay Adjustment */
        .video-overlay {
            opacity: 0.1 !important;
        }

        /* Remove marker from header */
        ::marker {
            display: none;
        }
        
        li {
            list-style-type: none;
        }

        /* RTL Support */
        [dir="rtl"] footer a::before {
            content: '←';
            left: auto;
            right: -20px;
        }
        
        [dir="rtl"] footer a:hover {
            padding-left: 0;
            padding-right: 20px;
        }
        
        [dir="rtl"] footer a:hover::before {
            left: auto;
            right: 0;
        }
        
        [dir="rtl"] .social-links a {
            margin-right: 0;
            margin-left: 10px;
        }
        
        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .navbar {
                padding: 0.5rem 0;
            }
            
            .nav-link {
                padding: 0.5rem !important;
                margin: 0.2rem 0;
            }
            
            footer {
                padding: 3rem 0 1rem;
            }
            
            .social-links {
                margin-top: 1.5rem;
            }
        }

        /* Admin Interface Styling */
        .admin-search-filter .form-control,
        .admin-search-filter .form-select {
            border-radius: 0;
            border: 1px solid #ced4da;
            padding: 0.5rem 0.75rem;
            height: 42px;
        }
        
        .admin-search-filter .form-control::placeholder {
            color: #ced4da;
            opacity: 0.7;
        }
        
        .admin-search-filter .form-control:focus,
        .admin-search-filter .form-select:focus {
            box-shadow: 0 0 0 0.2rem rgba(46, 204, 113, 0.25);
            border-color: var(--primary-color);
        }
        
        .admin-search-filter .btn {
            border-radius: 0;
            padding: 0.5rem 1.5rem;
        }
        
        /* Make search inputs consistent with dropdowns */
        input.form-control,
        select.form-select {
            border-radius: 0 !important;
        }
        
        /* Fix for search inputs */
        .form-control:not([type="checkbox"]):not([type="radio"]) {
            height: auto;
            min-height: 38px;
            padding: 0.375rem 0.75rem;
        }

        /* Enhanced Header Styles */
        .navbar {
            padding: 0.5rem 0;
        }

        .navbar-brand-wrapper {
            padding: 0.5rem 0;
        }

        .navbar-brand img {
            transition: transform 0.3s ease;
        }

        .navbar-brand:hover img {
            transform: scale(1.05);
        }

        /* Primary Navigation */
        .navbar-nav .nav-link {
            padding: 0.5rem 1rem !important;
            margin: 0 0.2rem;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .navbar-nav .nav-link:hover {
            background: rgba(46, 204, 113, 0.1);
            color: var(--primary-color) !important;
        }

        /* Secondary Navigation */
        .nav-secondary {
            gap: 0.5rem;
        }

        /* Search Form */
        .search-wrapper .input-group {
            max-width: 300px;
        }

        .search-wrapper .form-control {
            height: 38px;
            border-radius: 20px 0 0 20px;
        }

        .search-wrapper .btn {
            border-radius: 0 20px 20px 0;
            padding: 0.375rem 0.75rem;
        }

        /* Common Button Styles */
        .nav-secondary .btn {
            width: 38px;
            height: 38px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .nav-secondary .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        /* Cart Button */
        .cart-wrapper .badge {
            font-size: 0.7rem;
            padding: 0.25rem 0.4rem;
            min-width: 18px;
            height: 18px;
            border-radius: 50%;
        }

        /* Tooltips */
        [title] {
            position: relative;
        }

        /* Responsive Adjustments */
        @media (max-width: 991.98px) {
            .nav-secondary {
                margin-top: 0.5rem;
            }
            
            .search-wrapper {
                order: -1;
                width: 100%;
                margin-bottom: 0.5rem;
            }
            
            .search-wrapper .input-group {
                max-width: none;
            }
        }

        @media (max-width: 575.98px) {
            .nav-secondary {
                justify-content: space-between;
                width: 100%;
            }
            
            .nav-secondary > div:not(.search-wrapper) {
                margin: 0;
            }
            
            .search-wrapper {
                margin-bottom: 0.5rem;
            }
        }

        /* Language Toggle Styles */
        .language-toggle-form {
            margin: 0;
            padding: 0;
        }

        .language-toggle {
            width: 38px;
            height: 38px;
            padding: 0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .language-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .language-icon {
            display: flex;
            align-items: center;
            gap: 4px;
            transition: transform 0.3s ease;
        }

        .language-text {
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 2px;
        }

        /* RTL Support */
        .language-toggle.is-ar {
            font-family: 'Arial', sans-serif;
        }

        [dir="rtl"] .language-text {
            margin-left: 0;
            margin-right: 2px;
        }

        /* Animation */
        .language-toggle .language-icon {
            transition: transform 0.3s ease;
        }

        .language-toggle:hover .language-icon {
            transform: scale(1.1);
        }

        .language-toggle:active .language-icon {
            transform: scale(0.95);
        }

        /* Loading State */
        .language-toggle.loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .language-toggle.loading .language-icon {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 575.98px) {
            .language-toggle {
                width: 38px;
                height: 38px;
            }
        }
    </style>
</head>
<body>
    <!-- Video Background -->
    <div class="video-background">
        {% if website_video %}
        <video autoplay muted loop playsinline preload="auto" id="bgVideo">
            <source src="{{ website_video.video.url }}" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <div class="video-overlay"></div>
        {% else %}
        <!-- No video found in database -->
        <div class="video-overlay"></div>
        {% endif %}
    </div>

    <!-- Loading Spinner -->
    <div class="loading-spinner">
        <div class="spinner"></div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <!-- Logo Section -->
            <div class="navbar-brand-wrapper">
                <a class="navbar-brand d-flex align-items-center" href="{% url 'GB_FARM:home' %}">
                    <img src="{% static 'logo2.png' %}" alt="GB Farm Logo" height="40" class="d-inline-block">
                    <img src="{% static 'logo3.png' %}" alt="GB Farm Logo" height="40" class="d-inline-block ms-2">
                </a>
            </div>

            <!-- Mobile Toggle Button -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Main Navigation Content -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Primary Navigation -->
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'GB_FARM:home' %}">
                            <i class="fas fa-home me-1"></i>{% trans "Home" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'GB_FARM:product_list' %}">
                            <i class="fas fa-store me-1"></i>{% trans "Products" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'GB_FARM:about' %}">
                            <i class="fas fa-info-circle me-1"></i>{% trans "About Us" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'GB_FARM:contact' %}">
                            <i class="fas fa-envelope me-1"></i>{% trans "Contact Us" %}
                        </a>
                    </li>
                </ul>
                
                <!-- Secondary Navigation (Search, Cart, User) -->
                <div class="nav-secondary d-flex align-items-center">
                    <!-- Search Form -->
                    <div class="search-wrapper me-2">
                        <form class="d-flex" action="{% url 'GB_FARM:product_search' %}" method="GET">
                            <div class="input-group">
                                <input class="form-control search-input" type="search" name="q" 
                                       placeholder="{% trans 'Search products...' %}" 
                                       aria-label="Search">
                                <button class="btn btn-outline-success" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Cart Button -->
                    <div class="cart-wrapper me-2">
                        <a href="{% url 'GB_FARM:cart' %}" class="btn btn-outline-success position-relative" title="Shopping Cart">
                            <i class="fas fa-shopping-cart"></i>
                            {% if cart_count > 0 %}
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                {{ cart_count }}
                                <span class="visually-hidden">items in cart</span>
                            </span>
                            {% endif %}
                        </a>
                    </div>

                    <!-- User Menu -->
                    <div class="user-menu-wrapper me-2">
                        {% if user.is_authenticated %}
                        <div class="dropdown">
                            <button class="btn btn-outline-success dropdown-toggle" type="button" id="userMenu" data-bs-toggle="dropdown" aria-expanded="false" title="User Menu">
                                <i class="fas fa-user"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow-sm">
                                <li><a class="dropdown-item" href="{% url 'GB_FARM:profile' %}">
                                    <i class="fas fa-user-circle me-2"></i>Profile
                                </a></li>
                                <li><a class="dropdown-item" href="{% url 'GB_FARM:order_history' %}">
                                    <i class="fas fa-history me-2"></i>Orders
                                </a></li>
                                {% if user.is_staff %}
                                <li><a class="dropdown-item" href="{% url 'GB_FARM:admin_order_dashboard' %}">
                                    <i class="fas fa-tachometer-alt me-2"></i>Admin
                                </a></li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'GB_FARM:logoutuser' %}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a></li>
                            </ul>
                        </div>
                        {% else %}
                        <div class="auth-buttons d-flex">
                            <a href="{% url 'GB_FARM:loginpage' %}" class="btn btn-outline-success me-2" title="Login">
                                <i class="fas fa-sign-in-alt"></i>
                                <span class="visually-hidden">Login</span>
                            </a>
                            <a href="{% url 'GB_FARM:registerpage' %}" class="btn btn-success" title="Register">
                                <i class="fas fa-user-plus"></i>
                                <span class="visually-hidden">Register</span>
                            </a>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Language Switcher -->
                    <div class="language-wrapper">
                        <form action="{% url 'set_language' %}" method="post" class="language-toggle-form">
                            {% csrf_token %}
                            <input type="hidden" name="next" value="{{ request.get_full_path }}">
                            <button type="submit" 
                                    name="language" 
                                    value="{% if LANGUAGE_CODE == 'ar' %}en{% else %}ar{% endif %}" 
                                    class="btn btn-outline-success language-toggle {% if LANGUAGE_CODE == 'ar' %}is-ar{% endif %}" 
                                    title="{% if LANGUAGE_CODE == 'ar' %}Switch to English{% else %}التحويل للعربية{% endif %}">
                                <span class="language-icon">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                        <i class="fas fa-flag-usa"></i>
                                        <span class="language-text">EN</span>
                                    {% else %}
                                        <i class="fas fa-globe-africa"></i>
                                        <span class="language-text">ع</span>
                                    {% endif %}
                                </span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container py-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert" data-aos="fade-down">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>
                        <img src="{% static 'logo3.png' %}" alt="GB Farm Logo" height="40" class="mb-2">
                    </h5>
                    <p>Fresh and organic products delivered to your doorstep.</p>
                </div>
                <div class="col-md-4">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'GB_FARM:home' %}">Home</a></li>
                        <li><a href="{% url 'GB_FARM:product_list' %}">Products</a></li>
                        <li><a href="{% url 'GB_FARM:about' %}">About Us</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>Contact Us</h5>
                    <ul class="list-unstyled">
                        {% if footer %}
                            <li><i class="fas fa-map-marker-alt me-2"></i>{{ footer.address }}</li>
                            <li><i class="fas fa-phone me-2"></i>{{ footer.phone }}</li>
                            <li><i class="fas fa-envelope me-2"></i>{{ footer.email }}</li>
                        {% endif %}
                    </ul>
                    <div class="social-links mt-3">
                        {% if footer.facebook_link %}
                            <a href="{{ footer.facebook_link }}" target="_blank"><i class="fab fa-facebook-f"></i></a>
                        {% endif %}
                        {% if footer.instagram_link %}
                            <a href="{{ footer.instagram_link }}" target="_blank"><i class="fab fa-instagram"></i></a>
                        {% endif %}
                        {% if footer.twitter_link %}
                            <a href="{{ footer.twitter_link }}" target="_blank"><i class="fab fa-twitter"></i></a>
                        {% endif %}
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0">&copy; {% now "Y" %} GB Farm. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <script src="{% static 'js/main.js' %}"></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            once: true,
            offset: 100
        });
        
        // Simple video background handler
        document.addEventListener('DOMContentLoaded', function() {
            const video = document.querySelector('#bgVideo');
            if (video) {
                console.log('Video found - initializing');
                
                // Set video properties
                video.muted = true;
                video.autoplay = true;
                video.loop = true;
                video.playsinline = true;
                video.playbackRate = 0.4;
                
                // Force play the video
                setTimeout(function() {
                    console.log('Playing video...');
                    video.play()
                        .then(() => console.log('Video started playing'))
                        .catch(e => console.error('Error playing video:', e));
                }, 100);
                
                // Set playback rate when playing starts
                video.addEventListener('playing', function() {
                    this.playbackRate = 0.4;
                    console.log('Video is now playing at slower rate');
                });
            }
            
            // Handle messages - auto-dismiss and remove duplicates
            const alerts = document.querySelectorAll('.alert');
            
            // Display only unique messages
            const seenMessages = new Set();
            alerts.forEach(alert => {
                const message = alert.textContent.trim();
                if (seenMessages.has(message)) {
                    alert.remove(); // Remove duplicate message
                } else {
                    seenMessages.add(message);
                    
                    // Auto-dismiss after 3 seconds
                    setTimeout(() => {
                        if (alert && alert.parentNode) {
                            // Use bootstrap's dismiss if available, otherwise remove directly
                            if (bootstrap && bootstrap.Alert) {
                                const bsAlert = new bootstrap.Alert(alert);
                                bsAlert.close();
                            } else {
                                alert.remove();
                            }
                        }
                    }, 3000);
                }
            });
        });
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                const video = document.querySelector('#bgVideo');
                if (video) {
                    video.play().catch(e => console.error('Error on visibility change:', e));
                }
            }
        });
        
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
        
        // Loading Spinner
        document.addEventListener('DOMContentLoaded', function() {
            const spinner = document.querySelector('.loading-spinner');
            
            // Show spinner on form submissions
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', () => {
                    spinner.classList.add('active');
                });
            });
            
            // Hide spinner when page is fully loaded
            window.addEventListener('load', () => {
                spinner.classList.remove('active');
            });
        });
        
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    e.preventDefault();
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // Add hover effect to cards
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Language Toggle Enhancement
        document.addEventListener('DOMContentLoaded', function() {
            const languageForm = document.querySelector('.language-toggle-form');
            const languageButton = document.querySelector('.language-toggle');

            if (languageForm && languageButton) {
                languageButton.addEventListener('click', function(e) {
                    // Add loading state
                    this.classList.add('loading');
                    
                    // Submit form after a small delay to show the loading state
                    setTimeout(() => {
                        languageForm.submit();
                    }, 100);
                });
            }
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html> 