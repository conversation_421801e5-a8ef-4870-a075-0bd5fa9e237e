// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add fade-in animation to main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }

    // Handle quantity input changes
    const quantityInputs = document.querySelectorAll('.quantity-input');
    quantityInputs.forEach(input => {
        input.addEventListener('change', function() {
            const min = parseInt(this.getAttribute('min')) || 1;
            const max = parseInt(this.getAttribute('max')) || 99;
            let value = parseInt(this.value) || 0;

            // Ensure the value is within bounds
            value = Math.max(min, Math.min(max, value));
            this.value = value;

            // If this is in a cart form, submit it
            const cartForm = this.closest('form[data-cart-update]');
            if (cartForm) {
                cartForm.submit();
            }
        });
    });

    // Handle quantity buttons
    const quantityButtons = document.querySelectorAll('.quantity-btn');
    quantityButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const input = this.closest('.quantity-group').querySelector('.quantity-input');
            const currentValue = parseInt(input.value) || 0;
            const isIncrement = this.classList.contains('quantity-increment');
            const min = parseInt(input.getAttribute('min')) || 1;
            const max = parseInt(input.getAttribute('max')) || 99;

            if (isIncrement && currentValue < max) {
                input.value = currentValue + 1;
            } else if (!isIncrement && currentValue > min) {
                input.value = currentValue - 1;
            }

            // Trigger the change event
            input.dispatchEvent(new Event('change'));
        });
    });

    // Handle clear cart confirmation
    const clearCartBtn = document.querySelector('.clear-cart-btn');
    if (clearCartBtn) {
        clearCartBtn.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to clear your cart?')) {
                e.preventDefault();
            }
        });
    }

    // Handle product search with debounce
    const searchInput = document.querySelector('input[name="q"]');
    if (searchInput) {
        let timeout = null;
        searchInput.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                if (this.value.length >= 2) {
                    this.closest('form').submit();
                }
            }, 500);
        });
    }

    // Add to cart animation
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.classList.add('fa-bounce');
                setTimeout(() => {
                    icon.classList.remove('fa-bounce');
                }, 1000);
            }
        });
    });


}); 