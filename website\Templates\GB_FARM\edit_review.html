{% extends 'GB_FARM/base.html' %}

{% block title %}Edit Your Review{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Edit Your Review for {{ product.name }}</h5>
                </div>
                <div class="card-body">
                    {% if messages %}
                    <div class="messages mb-4">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            {{ message }}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <form method="post" action="{% url 'GB_FARM:edit_review' product.id review.id %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label class="form-label">Rating</label>
                            <div class="rating">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="rating" id="rating1" value="1" {% if review.rating == 1 %}checked{% endif %} required>
                                    <label class="form-check-label" for="rating1">1 ⭐</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="rating" id="rating2" value="2" {% if review.rating == 2 %}checked{% endif %}>
                                    <label class="form-check-label" for="rating2">2 ⭐</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="rating" id="rating3" value="3" {% if review.rating == 3 %}checked{% endif %}>
                                    <label class="form-check-label" for="rating3">3 ⭐</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="rating" id="rating4" value="4" {% if review.rating == 4 %}checked{% endif %}>
                                    <label class="form-check-label" for="rating4">4 ⭐</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="rating" id="rating5" value="5" {% if review.rating == 5 %}checked{% endif %}>
                                    <label class="form-check-label" for="rating5">5 ⭐</label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="comment" class="form-label">Your Review</label>
                            <textarea class="form-control" id="comment" name="comment" rows="4" required>{{ review.comment }}</textarea>
                        </div>
                        <div class="d-flex">
                            <button type="submit" class="btn btn-primary me-2">Save Changes</button>
                            <a href="{% url 'GB_FARM:product_reviews' product.id %}" class="btn btn-outline-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 