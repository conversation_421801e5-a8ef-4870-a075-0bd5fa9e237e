#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.test import Client
from django.conf import settings

def test_error_pages():
    """Test all custom error pages"""
    print("🧪 Testing Custom Error Pages...")
    print("=" * 50)
    
    client = Client()
    
    # Test error page URLs
    error_tests = [
        ('/test-400/', 400, '400 Bad Request'),
        ('/test-403/', 403, '403 Forbidden'),
        ('/test-404/', 404, '404 Not Found'),
        ('/test-500/', 500, '500 Server Error'),
    ]
    
    print(f"\n📋 Current DEBUG setting: {settings.DEBUG}")
    if settings.DEBUG:
        print("⚠️  Note: Custom error handlers only work when DEBUG=False in production")
        print("   But test views should still work to preview error pages")
    
    for url, expected_status, description in error_tests:
        print(f"\n🔍 Testing {description} ({url})...")
        
        try:
            response = client.get(url)
            print(f"   ✅ Status Code: {response.status_code}")
            
            # Check if the response contains the expected content
            content = response.content.decode('utf-8')
            
            # Check for error code in content
            error_code = str(expected_status)
            if error_code in content:
                print(f"   ✅ Error code '{error_code}' found in page")
            else:
                print(f"   ❌ Error code '{error_code}' NOT found in page")
            
            # Check for GB Farms branding
            if 'GB Farms' in content:
                print("   ✅ GB Farms branding found")
            else:
                print("   ❌ GB Farms branding NOT found")
            
            # Check for Tailwind CSS
            if 'tailwindcss.com' in content:
                print("   ✅ Tailwind CSS loaded")
            else:
                print("   ❌ Tailwind CSS NOT loaded")
            
            # Check for home link
            if 'href="/"' in content and 'Go to Home' in content:
                print("   ✅ Home link found")
            else:
                print("   ❌ Home link NOT found")
                
        except Exception as e:
            print(f"   ❌ Error testing {url}: {e}")
    
    # Test real 404 error
    print(f"\n🔍 Testing Real 404 Error...")
    try:
        response = client.get('/this-page-definitely-does-not-exist/')
        print(f"   ✅ Status Code: {response.status_code}")
        
        if settings.DEBUG:
            print("   ⚠️  Django debug page shown (expected in DEBUG mode)")
        else:
            content = response.content.decode('utf-8')
            if '404' in content and 'GB Farms' in content:
                print("   ✅ Custom 404 page working correctly")
            else:
                print("   ❌ Custom 404 page not working")
                
    except Exception as e:
        print(f"   ❌ Error testing real 404: {e}")
    
    # Test template files exist
    print(f"\n📁 Checking Error Template Files...")
    
    template_files = [
        'website/Templates/Extrnal.html/400.html',
        'website/Templates/Extrnal.html/403.html', 
        'website/Templates/Extrnal.html/404.html',
        'website/Templates/Extrnal.html/500.html'
    ]
    
    for template_file in template_files:
        if os.path.exists(template_file):
            print(f"   ✅ {template_file} exists")
            
            # Check file content
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'GB Farms' in content:
                    print(f"      ✅ Contains GB Farms branding")
                else:
                    print(f"      ❌ Missing GB Farms branding")
        else:
            print(f"   ❌ {template_file} NOT found")
    
    print("\n" + "=" * 50)
    print("✅ Error pages testing completed!")
    
    if settings.DEBUG:
        print("\n💡 To test error handlers in production:")
        print("   1. Set DEBUG=False in settings")
        print("   2. Set ALLOWED_HOSTS properly")
        print("   3. Visit non-existent URLs to trigger real errors")

if __name__ == "__main__":
    test_error_pages()
