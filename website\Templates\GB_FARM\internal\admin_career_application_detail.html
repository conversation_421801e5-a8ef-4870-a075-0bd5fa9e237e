{% extends "GB_FARM/admin_base.html" %}
{% load static %}

{% block title %}{{ application.full_name }} - Career Application Details{% endblock %}

{% block admin_content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="mb-0">Career Application Details</h4>
                <a href="{% url 'GB_FARM:admin_career_applications_dashboard' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Applications
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Application Details -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Applicant Information</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label class="form-label text-muted">Full Name</label>
                            <div class="h5 text-dark">{{ application.full_name }}</div>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label text-muted">Email</label>
                            <div>
                                <a href="mailto:{{ application.email }}" class="text-primary">
                                    {{ application.email }}
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        {% if application.phone_number %}
                        <div class="col-md-6">
                            <label class="form-label text-muted">Phone Number</label>
                            <div>
                                <a href="tel:{{ application.phone_number }}" class="text-primary">
                                    {{ application.phone_number }}
                                </a>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if application.years_of_experience %}
                        <div class="col-md-6">
                            <label class="form-label text-muted">Years of Experience</label>
                            <div class="text-dark">{{ application.years_of_experience }} years</div>
                        </div>
                        {% endif %}
                    </div>

                    <div class="row mb-4">
                        {% if application.current_position %}
                        <div class="col-md-6">
                            <label class="form-label text-muted">Current Position</label>
                            <div class="text-dark">{{ application.current_position }}</div>
                        </div>
                        {% endif %}
                        
                        {% if application.salary_expectation %}
                        <div class="col-md-6">
                            <label class="form-label text-muted">Salary Expectation</label>
                            <div class="text-dark">{{ application.salary_expectation }}</div>
                        </div>
                        {% endif %}
                    </div>

                    {% if application.available_start_date %}
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label class="form-label text-muted">Available Start Date</label>
                            <div class="text-dark">{{ application.available_start_date|date:"M d, Y" }}</div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Links -->
                    {% if application.linkedin_profile or application.portfolio_website %}
                    <div class="mb-4">
                        <label class="form-label text-muted">Professional Links</label>
                        <div>
                            {% if application.linkedin_profile %}
                            <div class="mb-2">
                                <a href="{{ application.linkedin_profile }}" target="_blank" class="text-primary text-decoration-underline">
                                    <i class="fab fa-linkedin me-2"></i>LinkedIn Profile
                                </a>
                            </div>
                            {% endif %}
                            
                            {% if application.portfolio_website %}
                            <div class="mb-2">
                                <a href="{{ application.portfolio_website }}" target="_blank" class="text-primary text-decoration-underline">
                                    <i class="fas fa-globe me-2"></i>Portfolio Website
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Cover Letter -->
                    {% if application.cover_letter %}
                    <div class="mb-4">
                        <label class="form-label text-muted">Cover Letter</label>
                        <div class="p-3 bg-light rounded">
                            <p class="mb-0" style="white-space: pre-line;">{{ application.cover_letter }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Position Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Position Applied For</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Title</label>
                        <div class="fw-bold text-dark">{{ application.career.title }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Department</label>
                        <div class="text-dark">{{ application.career.department }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Location</label>
                        <div class="text-dark">{{ application.career.location }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Type</label>
                        <div class="text-dark">{{ application.career.get_job_type_display }}</div>
                    </div>
                </div>
            </div>

            <!-- CV Download -->
            {% if application.cv_file %}
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">CV/Resume</h6>
                </div>
                <div class="card-body">
                    <a href="{{ application.cv_file.url }}" target="_blank" class="btn btn-primary w-100">
                        <i class="fas fa-download me-2"></i>Download CV
                    </a>
                </div>
            </div>
            {% endif %}

            <!-- Application Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Application Status</h6>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="is_processed" {% if application.is_processed %}checked{% endif %}
                                       class="form-check-input" id="processedCheck">
                                <label class="form-check-label" for="processedCheck">
                                    Mark as Processed
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">Admin Notes</label>
                            <textarea name="notes" id="notes" rows="4" class="form-control"
                                      placeholder="Add notes about this application...">{{ application.notes }}</textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-success w-100">
                            <i class="fas fa-save me-2"></i>Update Application
                        </button>
                    </form>
                </div>
            </div>

            <!-- Application Meta -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Application Info</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Submitted:</label>
                        <div class="text-dark">{{ application.created_at|date:"M d, Y g:i A" }}</div>
                    </div>
                    
                    {% if application.updated_at != application.created_at %}
                    <div class="mb-3">
                        <label class="form-label text-muted">Last Updated:</label>
                        <div class="text-dark">{{ application.updated_at|date:"M d, Y g:i A" }}</div>
                    </div>
                    {% endif %}
                    
                    {% if application.ip_address %}
                    <div class="mb-3">
                        <label class="form-label text-muted">IP Address:</label>
                        <div class="text-dark">{{ application.ip_address }}</div>
                    </div>
                    {% endif %}
                    
                    <div class="mb-0">
                        <label class="form-label text-muted">Status:</label>
                        <div>
                            {% if application.is_processed %}
                            <span class="badge bg-success">Processed</span>
                            {% else %}
                            <span class="badge bg-warning">Pending</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 