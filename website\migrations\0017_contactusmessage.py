# Generated by Django 4.2.20 on 2025-06-02 11:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0016_configuration_externalproduct_externalproductvariety_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContactUsMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('message', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Contact Us Message',
                'verbose_name_plural': 'Contact Us Messages',
                'ordering': ['-created_at'],
            },
        ),
    ]
