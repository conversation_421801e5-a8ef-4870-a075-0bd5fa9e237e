from django.core.management.base import BaseCommand
from website.models import Cart, CartItem
from django.db.models import Count

class Command(BaseCommand):
    help = 'Cleans up duplicate carts by merging them into a single cart per user'

    def handle(self, *args, **options):
        # Get users with multiple carts
        users_with_multiple_carts = Cart.objects.values('user').annotate(
            cart_count=Count('id')
        ).filter(cart_count__gt=1)

        for user_data in users_with_multiple_carts:
            user_id = user_data['user']
            carts = Cart.objects.filter(user_id=user_id)
            
            # Keep the first cart and merge others into it
            main_cart = carts.first()
            other_carts = carts.exclude(id=main_cart.id)
            
            # Move all items from other carts to the main cart
            for cart in other_carts:
                for item in cart.cartitem_set.all():
                    # Check if item already exists in main cart
                    existing_item = main_cart.cartitem_set.filter(
                        product=item.product
                    ).first()
                    
                    if existing_item:
                        # Update quantity if item exists
                        existing_item.quantity += item.quantity
                        existing_item.save()
                    else:
                        # Move item to main cart if it doesn't exist
                        item.cart = main_cart
                        item.save()
                
                # Delete the old cart
                cart.delete()
            
            self.stdout.write(
                self.style.SUCCESS(f'Successfully merged carts for user {user_id}')
            ) 