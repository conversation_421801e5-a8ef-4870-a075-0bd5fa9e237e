from django import forms


class ColorPickerWidget(forms.TextInput):
    """Widget مخصص لاختيار الألوان"""
    
    def __init__(self, attrs=None):
        default_attrs = {
            'type': 'color',
            'class': 'form-control color-picker',
            'style': 'width: 60px; height: 40px; border: none; cursor: pointer;'
        }
        if attrs:
            default_attrs.update(attrs)
        super().__init__(attrs=default_attrs)

    class Media:
        css = {
            'all': ('admin/css/color-picker.css',)
        }
        js = ('admin/js/color-picker.js',)
