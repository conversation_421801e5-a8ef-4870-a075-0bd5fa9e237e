{% extends 'GB_FARM/base.html' %}
{% load static %}
{% load form_filters %}

{% block extra_css %}
<style>
    /* Custom Registration Form Styling */
    .register-card {
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.9);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
        border: none;
    }
    
    .register-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(46, 204, 113, 0.2);
    }
    
    .register-heading {
        color: var(--primary-color);
        font-weight: 700;
        letter-spacing: 0.5px;
        position: relative;
        padding-bottom: 10px;
        margin-bottom: 30px;
    }
    
    .register-heading:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
        border-radius: 3px;
    }
    
    .form-control {
        border-radius: 30px;
        padding: 12px 20px;
        border: 2px solid #eaeaea;
        transition: all 0.3s ease;
        background-color: rgba(255, 255, 255, 0.9);
    }
    
    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.25rem rgba(46, 204, 113, 0.25);
        background-color: #fff;
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--text-color);
        padding-left: 10px;
    }
    
    .form-text {
        color: #6c757d;
        font-size: 0.85rem;
        padding-left: 10px;
        margin-top: 5px;
    }
    
    .register-btn {
        padding: 12px 25px;
        font-weight: 600;
        letter-spacing: 0.5px;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        border: none;
        box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
        transition: all 0.3s ease;
    }
    
    .register-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(46, 204, 113, 0.4);
    }
    
    .register-link {
        color: var(--primary-color);
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
    }
    
    .register-link:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }
    
    .form-container {
        animation: fadeIn 0.6s ease;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .invalid-feedback {
        color: #dc3545;
        font-size: 0.85rem;
        margin-top: 5px;
        padding-left: 15px;
    }
    
    /* Field animation effect */
    .form-group {
        position: relative;
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card register-card shadow">
                <div class="card-body p-5">
                    <h2 class="register-heading text-center mb-4">Register</h2>
                    
                    {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} rounded-pill">
                            {{ message }}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <div class="form-container">
                        <form method="POST" class="needs-validation" novalidate>
                            {% csrf_token %}
                            
                            <div class="form-group mb-4">
                                <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                                {{ form.username|add_class:"form-control" }}
                                {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.username.errors }}
                                </div>
                                {% endif %}
                                {% if form.username.help_text %}
                                <div class="form-text">{{ form.username.help_text }}</div>
                                {% endif %}
                            </div>

                            <div class="form-group mb-4">
                                <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                                {{ form.email|add_class:"form-control" }}
                                {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.email.errors }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="form-group mb-4">
                                <label for="{{ form.password1.id_for_label }}" class="form-label">Password</label>
                                {{ form.password1|add_class:"form-control" }}
                                {% if form.password1.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.password1.errors }}
                                </div>
                                {% endif %}
                                {% if form.password1.help_text %}
                                <div class="form-text">{{ form.password1.help_text }}</div>
                                {% endif %}
                            </div>

                            <div class="form-group mb-4">
                                <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password</label>
                                {{ form.password2|add_class:"form-control" }}
                                {% if form.password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.password2.errors }}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="d-grid gap-2 mt-4">
                                <button type="submit" class="btn btn-primary register-btn rounded-pill">Register</button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="text-center mt-4">
                        <p>Already have an account? <a href="{% url 'GB_FARM:loginpage' %}" class="register-link">Login here</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()
</script>
{% endblock %} 