{% extends 'GB_FARM/base.html' %}

{% block title %}My Profile{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Account Menu</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'GB_FARM:profile' %}" class="list-group-item list-group-item-action active">
                        <i class="bi bi-person-circle me-2"></i> My Profile
                    </a>
                    <a href="{% url 'GB_FARM:edit_profile' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-pencil-square me-2"></i> Edit Profile
                    </a>
                    <a href="{% url 'GB_FARM:change_password' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-key me-2"></i> Change Password
                    </a>
                    <a href="{% url 'GB_FARM:order_history' %}" class="list-group-item list-group-item-action">
                        <i class="bi bi-bag me-2"></i> Order History
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">My Profile</h5>
                    <a href="{% url 'GB_FARM:edit_profile' %}" class="btn btn-light btn-sm">
                        <i class="bi bi-pencil"></i> Edit
                    </a>
                </div>
                <div class="card-body">
                    {% if messages %}
                    <div class="messages mb-4">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            {{ message }}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <div class="row mb-4">
                        <div class="col-md-3 text-center">
                            <div class="profile-img-container mb-3">
                                {% if profile.profile_picture %}
                                <img src="{{ profile.profile_picture.url }}" alt="Profile Picture" class="img-thumbnail rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                                {% else %}
                                <div class="profile-img-placeholder bg-light d-flex align-items-center justify-content-center" style="width: 150px; height: 150px; border-radius: 50%; margin: 0 auto;">
                                    <i class="bi bi-person-fill" style="font-size: 5rem; color: #ccc;"></i>
                                </div>
                                {% endif %}
                            </div>
                            <h5>{{ user.username }}</h5>
                            <span class="badge bg-secondary">{{ user.get_user_type_display }}</span>
                        </div>
                        <div class="col-md-9">
                            <h4 class="border-bottom pb-2 mb-3">Account Information</h4>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Username:</div>
                                <div class="col-md-8">{{ user.username }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Full Name:</div>
                                <div class="col-md-8">
                                    {% if user.first_name or user.last_name %}
                                        {{ user.first_name }} {{ user.last_name }}
                                    {% else %}
                                        <span class="text-muted">Not provided</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Email:</div>
                                <div class="col-md-8">{{ user.email }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Phone:</div>
                                <div class="col-md-8">
                                    {% if user.user_phone %}
                                        {{ user.user_phone }}
                                    {% else %}
                                        <span class="text-muted">Not provided</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Member Since:</div>
                                <div class="col-md-8">{{ user.date_joined|date:"F j, Y" }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <h4 class="border-bottom pb-2 mb-3">Account Activity</h4>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Last Login:</div>
                                <div class="col-md-8">{{ user.last_login|date:"F j, Y, g:i a" }}</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Total Orders:</div>
                                <div class="col-md-8">{{ order_count }}</div>
                            </div>
                            {% if recent_order %}
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Last Order:</div>
                                <div class="col-md-8">
                                    <a href="{% url 'GB_FARM:order_details' recent_order.id %}">
                                        Order #{{ recent_order.id }} ({{ recent_order.created_at|date:"M d, Y" }})
                                    </a>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 