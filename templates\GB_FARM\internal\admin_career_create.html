{% extends "GB_FARM/internal/base.html" %}
{% load i18n %}

{% block title %}Admin - Create Career Opportunity{% endblock %}

{% block page_title %}Create Career Opportunity{% endblock %}

{% block page_actions %}
<a href="{% url 'GB_FARM:admin_career_dashboard' %}" class="btn btn-secondary">
    <i class="bi bi-arrow-left"></i> Back to Careers
</a>
{% endblock %}

{% block content %}
<div class="card shadow-sm">
    <div class="card-body">
        <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5 class="card-title mb-3">Basic Information</h5>
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="title_ar" class="form-label">Title (Arabic)</label>
                        <input type="text" class="form-control" id="title_ar" name="title_ar" dir="rtl">
                    </div>
                    
                    <div class="mb-3">
                        <label for="department" class="form-label">Department <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="department" name="department" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="department_ar" class="form-label">Department (Arabic)</label>
                        <input type="text" class="form-control" id="department_ar" name="department_ar" dir="rtl">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h5 class="card-title mb-3">Job Details</h5>
                    
                    <div class="mb-3">
                        <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="location" name="location" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="location_ar" class="form-label">Location (Arabic)</label>
                        <input type="text" class="form-control" id="location_ar" name="location_ar" dir="rtl">
                    </div>
                    
                    <div class="mb-3">
                        <label for="job_type" class="form-label">Job Type <span class="text-danger">*</span></label>
                        <select class="form-select" id="job_type" name="job_type" required>
                            <option value="">Select Job Type</option>
                            {% for job_type_value, job_type_display in job_types %}
                            <option value="{{ job_type_value }}">{{ job_type_display }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="experience_level" class="form-label">Experience Level <span class="text-danger">*</span></label>
                        <select class="form-select" id="experience_level" name="experience_level" required>
                            <option value="">Select Experience Level</option>
                            {% for level_value, level_display in experience_levels %}
                            <option value="{{ level_value }}">{{ level_display }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5 class="card-title mb-3">Job Description</h5>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description (English) <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="description" name="description" rows="6" required></textarea>
                        <div class="form-text">Provide a detailed description of the job position.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description_ar" class="form-label">Description (Arabic)</label>
                        <textarea class="form-control" id="description_ar" name="description_ar" rows="6" dir="rtl"></textarea>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h5 class="card-title mb-3">Requirements</h5>
                    
                    <div class="mb-3">
                        <label for="requirements" class="form-label">Requirements (English) <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="requirements" name="requirements" rows="6" required></textarea>
                        <div class="form-text">List the required skills, education, and experience for this position.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="requirements_ar" class="form-label">Requirements (Arabic)</label>
                        <textarea class="form-control" id="requirements_ar" name="requirements_ar" rows="6" dir="rtl"></textarea>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5 class="card-title mb-3">Responsibilities</h5>
                    
                    <div class="mb-3">
                        <label for="responsibilities" class="form-label">Responsibilities (English) <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="responsibilities" name="responsibilities" rows="6" required></textarea>
                        <div class="form-text">List the key responsibilities for this position.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="responsibilities_ar" class="form-label">Responsibilities (Arabic)</label>
                        <textarea class="form-control" id="responsibilities_ar" name="responsibilities_ar" rows="6" dir="rtl"></textarea>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h5 class="card-title mb-3">Benefits</h5>
                    
                    <div class="mb-3">
                        <label for="benefits" class="form-label">Benefits (English)</label>
                        <textarea class="form-control" id="benefits" name="benefits" rows="6"></textarea>
                        <div class="form-text">List the benefits offered for this position.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="benefits_ar" class="form-label">Benefits (Arabic)</label>
                        <textarea class="form-control" id="benefits_ar" name="benefits_ar" rows="6" dir="rtl"></textarea>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">
                            Active (Make this job posting visible to the public)
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-end">
                <a href="{% url 'GB_FARM:admin_career_dashboard' %}" class="btn btn-secondary me-2">Cancel</a>
                <button type="submit" class="btn btn-primary">Create Career Opportunity</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', event => {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        form.classList.add('was-validated');
    }, false);
});
</script>
{% endblock %} 