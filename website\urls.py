from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views, api_views
from django.contrib.sitemaps.views import sitemap
from .sitemaps import ProductSitemap, CategorySitemap, StaticSitemap
from django.utils.translation import gettext_lazy as _
from django.shortcuts import render


router = DefaultRouter()
router.register(r'products', api_views.ProductViewSet)
router.register(r'categories', api_views.CategoryViewSet)
router.register(r'orders', api_views.OrderViewSet, basename='order')
router.register(r'zones', api_views.ZoneViewSet)
router.register(r'deliveries', api_views.DeliveryViewSet)

# Nested router for reviews
product_router = DefaultRouter()
product_router.register(r'reviews', api_views.ReviewViewSet, basename='product-review')

app_name = 'GB_FARM'

# Define sitemaps dictionary
sitemaps = {
    'products': ProductSitemap,
    'categories': CategorySitemap,
    'static': StaticSitemap,
}

urlpatterns = [
    # Main pages
    path('', views.home, name='home'),
    path(_('products/'), views.product_list, name='product_list'),
    path(_('products/<int:product_id>/'), views.product_detail, name='product_detail'),
    path(_('categories/<int:category_id>/'), views.category_products, name='category_products'),
    path('_nested_admin/', include('nested_admin.urls')),  # ✅ مهم جدًا للمكتبة
    # Cart and Wishlist
    path(_('cart/'), views.cart, name='cart'),
    path(_('cart/add/<int:product_id>/'), views.add_to_cart, name='add_to_cart'),
    path(_('cart/update/<int:item_id>/'), views.update_cart, name='update_cart'),
    path(_('cart/remove/<int:item_id>/'), views.remove_from_cart, name='remove_from_cart'),
    path(_('cart/clear/'), views.clear_cart, name='clear_cart'),
    path(_('wishlist/'), views.wishlist, name='wishlist'),
    path(_('wishlist/add/<int:product_id>/'), views.add_to_wishlist, name='add_to_wishlist'),
    path(_('wishlist/remove/<int:product_id>/'), views.remove_from_wishlist, name='remove_from_wishlist'),

    # Orders
    path('checkout/', views.checkout, name='checkout'),
    path('orders/<int:order_id>/confirmation/', views.order_confirmation, name='order_confirmation'),
    path('orders/history/', views.order_history, name='order_history'),
    path('orders/<int:order_id>/update-status/', views.update_order_status, name='update_order_status'),

    # Reviews
    path('products/<int:product_id>/review/', views.add_review, name='add_review'),
    path('products/<int:product_id>/reviews/', views.product_review, name='product_reviews'),
    path('products/<int:product_id>/reviews/<int:review_id>/edit/', views.edit_review, name='edit_review'),
    path('reviews/<int:review_id>/delete/', views.delete_review, name='delete_review'),

    # API endpoints
    path('api/products/', views.api_product_list, name='api_product_list'),
    path('api/products/<int:product_id>/', views.api_product_detail, name='api_product_detail'),

    # Authentication views internal
    path('login/', views.loginpage, name='loginpage'),
    path('logout/', views.logoutuser, name='logoutuser'),
    path('register/', views.registerpage, name='registerpage'),

    # Search and Filter views
    path('search/', views.product_search, name='product_search'),
    
    # Checkout views
    path('create-checkout-session/<int:order_id>/', views.create_checkout_session, name='create_checkout_session'),
    path('payment-success/<int:order_id>/', views.payment_success, name='payment_success'),
    path('payment-cancel/', views.payment_cancel, name='payment_cancel'),
    path('process-payment/', views.process_payment, name='process_payment'),
    
    # Order views
    path('orders/<int:pk>/', views.order_details, name='order_details'),
    path('orders/<int:order_id>/messages/', views.order_messages, name='order_messages'),
    path('orders/<int:order_id>/invoice/', views.generate_invoice_pdf, name='generate_invoice'),
    path('orders/track/', views.track_order, name='track_order'),
    
    # Admin views
    path('dashboard/', views.admin_dashboard, name='admin_dashboard'),
    path('dashboard/orders/', views.admin_order_dashboard, name='admin_order_dashboard'),
    path('dashboard/orders/<int:order_id>/', views.admin_order_detail, name='admin_order_detail'),
    path('dashboard/orders/<int:order_id>/message/', views.admin_order_message, name='admin_order_message'),
    path('dashboard/invoices/', views.admin_invoice_logs, name='admin_invoice_logs'),
    path('dashboard/products/', views.admin_product_dashboard, name='admin_product_dashboard'),
    path('dashboard/products/create/', views.admin_product_create, name='admin_product_create'),
    path('dashboard/products/<int:product_id>/edit/', views.admin_product_edit, name='admin_product_edit'),
    path('dashboard/products/<int:product_id>/delete/', views.admin_product_delete, name='admin_product_delete'),
    path('dashboard/zones/create/', views.admin_zone_create, name='admin_zone_create'),
    path('dashboard/zones/<int:zone_id>/edit/', views.admin_zone_edit, name='admin_zone_edit'),
    path('dashboard/zones/<int:zone_id>/delete/', views.admin_zone_delete, name='admin_zone_delete'),
    path('dashboard/zones/', views.admin_zone_list, name='admin_zone_list'),
    path('dashboard/customers/', views.admin_customer_dashboard, name='admin_customer_dashboard'),
    path('dashboard/customers/<int:pk>/', views.admin_customer_detail, name='admin_customer_detail'),
    path('dashboard/analytics/', views.admin_analytics_dashboard, name='admin_analytics_dashboard'),
    path('dashboard/calendar/', views.admin_calendar_dashboard, name='admin_calendar_dashboard'),
    path('dashboard/grape-varieties/', views.admin_grape_varieties_dashboard, name='admin_grape_varieties_dashboard'),
    path('dashboard/mango-varieties/', views.admin_mango_varieties_dashboard, name='admin_mango_varieties_dashboard'),
    
    # User profile views
    path('profile/', views.profile_view, name='profile'),
    path('profile/edit/', views.edit_profile, name='edit_profile'),
    path('profile/change-password/', views.change_password, name='change_password'),
    
    # Stripe webhook
    path('webhook/stripe/', views.stripe_webhook, name='stripe_webhook'),
    
    # About Us view
    path('about/', views.about_view, name='about'),

    # Delivery
    path('delivery/', views.Delivery_view, name='delivery'),

    # Contact us
    path('contact/', views.contact_view, name='contact'),

    # Career pages
    path('careers/', views.career_list, name='career_list'),
    path('careers/<int:id>/', views.career_detail, name='career_detail'),
    path('dashboard/careers/', views.admin_career_dashboard, name='admin_career_dashboard'),
    path('dashboard/careers/create/', views.admin_career_create, name='admin_career_create'),
    path('dashboard/careers/<int:id>/edit/', views.admin_career_edit, name='admin_career_edit'),
    path('dashboard/careers/<int:id>/delete/', views.admin_career_delete, name='admin_career_delete'),

    path('contact-us/', views.contact_us_message, name='contact_us_message_form'),

    path('i18n/', include('django.conf.urls.i18n')),  # Add this for language switching
    path('sitemap.xml', sitemap, {'sitemaps': sitemaps}, name='django.contrib.sitemaps.views.sitemap'),


    # External views
    path('external-about/', views.external_about_view, name='external_about'),
    path('external-career/', views.external_career_view, name='external_career'),
    path('external-contact_us/', views.external_contact_view, name='external_contact'),
    path('submit-external-contact/', views.submit_external_contact, name='submit_external_contact'),
    path('external-mangoes/', views.external_product_mago_view, name='external_mangoes'),
    path('external-grapes/', views.external_product_graps_view, name='external_grapes'),
    path('external-Pomegranate/', views.external_product_others_view, name='external_others'),
    path('external-index/', views.home, name='external_index'),
    path('external-calendar/', views.external_calendar_view, name='external_calendar'),
    path('external-all-products/', views.external_all_products, name='ex_all_product'),

    path('dashboard/submissions/<int:submission_id>/process/', views.mark_submission_processed, name='mark_submission_processed'),


    
    # Newsletter Subscriptions
    path('subscribe_newsletter', views.subscribe_newsletter, name='subscribe_newsletter'),
    path('admin_newsletter_dashboard', views.admin_newsletter_dashboard, name='admin_newsletter_dashboard'),


# The code is mostly correct, but the handler paths should reference the correct module name.
# If your Django project is named "website" (not "GB_FARM"), update the handler paths accordingly.
# Also, these handler assignments should be at the root urls.py (not in an included app urls.py).
# Example for a project named "website":

    
]







